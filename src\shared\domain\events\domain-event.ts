import { v7 as uuidv7 } from 'uuid';

/**
 * 领域事件基类
 */
export abstract class DomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  public readonly aggregateId: string;
  public readonly eventVersion: number;

  constructor(aggregateId: string, eventVersion: number = 1) {
    this.eventId = uuidv7();
    this.occurredOn = new Date();
    this.aggregateId = aggregateId;
    this.eventVersion = eventVersion;
  }

  /**
   * 获取事件名称
   */
  abstract getEventName(): string;

  /**
   * 获取事件数据
   */
  abstract getEventData(): Record<string, any>;
}

/**
 * 领域事件发布器接口
 */
export interface DomainEventPublisher {
  publish(event: DomainEvent): Promise<void>;
  publishAll(events: DomainEvent[]): Promise<void>;
}

/**
 * 简单的领域事件发布器实现
 */
export class SimpleDomainEventPublisher implements DomainEventPublisher {
  constructor(private readonly eventBus: any) {}

  async publish(event: DomainEvent): Promise<void> {
    if (this.eventBus && this.eventBus.publish) {
      await this.eventBus.publish(event);
    } else {
      console.log(
        'Domain event published:',
        event.getEventName(),
        event.getEventData(),
      );
    }
  }

  async publishAll(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      await this.publish(event);
    }
  }
}

/**
 * 领域事件处理器接口
 */
export interface DomainEventHandler<T extends DomainEvent> {
  handle(event: T): Promise<void>;
}

/**
 * 领域事件总线接口
 */
export interface DomainEventBus {
  register<T extends DomainEvent>(
    eventName: string,
    handler: DomainEventHandler<T>,
  ): void;

  publish(event: DomainEvent): Promise<void>;
  publishAll(events: DomainEvent[]): Promise<void>;
}

import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../base.entity';

export enum TeacherType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  SUBSTITUTE = 'substitute',
}

/**
 * 教师用户实体
 */
@Entity('user_teachers')
export class UserTeacher extends BaseEntity {
  @Column({ unique: true, comment: '用户名' })
  username: string;

  @Column({ comment: '密码哈希' })
  passwordHash: string;

  @Column({ comment: '真实姓名' })
  realName: string;

  @Column({ nullable: true, comment: '邮箱' })
  email?: string;

  @Column({ nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ nullable: true, unique: true, comment: '工号' })
  employeeNumber?: string;

  @Column({ nullable: true, comment: '部门' })
  department?: string;

  @Column({ nullable: true, comment: '职位' })
  position?: string;

  @Column({
    type: 'enum',
    enum: TeacherType,
    default: TeacherType.FULL_TIME,
    comment: '教师类型',
  })
  teacherType: TeacherType;

  @Column({ nullable: true, comment: '专业领域' })
  specialization?: string;

  @Column({ nullable: true, comment: '学历' })
  education?: string;

  @Column({ nullable: true, comment: '身份证号' })
  idCard?: string;

  @Column({ default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({ nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ nullable: true, comment: '头像URL' })
  avatarUrl?: string;

  @Column({ nullable: true, comment: '入职时间' })
  hireDate?: Date;

  @Column({ nullable: true, comment: '办公室地址' })
  officeLocation?: string;

  @Column({ default: 'active', comment: '教师状态' })
  status: string;

  /**
   * 验证密码
   */
  validatePassword(password: string): boolean {
    // 这里应该使用实际的密码验证逻辑
    // 例如使用 bcrypt 比较密码
    return true; // 临时实现
  }

  /**
   * 更新最后登录时间
   */
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    return this.realName || this.username;
  }

  /**
   * 检查是否为全职教师
   */
  isFullTime(): boolean {
    return this.teacherType === TeacherType.FULL_TIME;
  }

  /**
   * 获取工作年限
   */
  getYearsOfService(): number | null {
    if (!this.hireDate) return null;
    
    const today = new Date();
    const hireDate = new Date(this.hireDate);
    const years = today.getFullYear() - hireDate.getFullYear();
    
    return years;
  }
}

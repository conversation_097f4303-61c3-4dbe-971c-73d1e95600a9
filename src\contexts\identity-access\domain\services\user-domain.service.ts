import { Injectable, Inject } from '@nestjs/common';
import { UserRepository } from '../repositories/user.repository';
import { User, StudentUser, TeacherUser } from '../aggregates/user.aggregate';
import { UserProfile } from '../value-objects/user-profile';
import { UserTypeEnum } from '../value-objects/user-type';
import {
  BusinessRuleException,
  EntityAlreadyExistsException,
} from '@/shared/application/exceptions/domain-exception';
import { USER_REPOSITORY } from '../tokens';

/**
 * 用户领域服务
 * 处理跨聚合的业务逻辑和复杂的业务规则
 */
@Injectable()
export class UserDomainService {
  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
  ) {}

  /**
   * 创建学生用户
   */
  async createStudent(
    profile: UserProfile,
    classId?: string,
    gradeId?: string,
  ): Promise<StudentUser> {
    // 检查手机号是否已存在
    await this.ensurePhoneNotExists(profile.phone);

    // 检查身份证号是否已存在
    if (profile.cardNo) {
      await this.ensureCardNoNotExists(profile.cardNo);
    }

    // 验证学生特定的业务规则
    this.validateStudentRules(profile);

    return new StudentUser(profile, classId, gradeId);
  }

  /**
   * 创建教师用户
   */
  async createTeacher(
    profile: UserProfile,
    carNo?: string,
  ): Promise<TeacherUser> {
    // 检查手机号是否已存在
    await this.ensurePhoneNotExists(profile.phone);

    // 检查身份证号是否已存在
    if (profile.cardNo) {
      await this.ensureCardNoNotExists(profile.cardNo);
    }

    // 验证教师特定的业务规则
    this.validateTeacherRules(profile);

    return new TeacherUser(profile, carNo);
  }

  /**
   * 验证用户档案更新
   */
  async validateProfileUpdate(
    userId: string,
    newProfile: UserProfile,
  ): Promise<void> {
    const existingUser = await this.userRepository.findById(userId);
    if (!existingUser) {
      throw new BusinessRuleException('用户不存在');
    }

    const currentProfile = existingUser.getProfile();

    // 如果没有当前档案，说明用户数据不完整，不能更新
    if (!currentProfile) {
      throw new BusinessRuleException('用户档案数据不完整，无法更新');
    }

    // 如果手机号发生变化，检查新手机号是否已被其他用户使用
    if (currentProfile.phone !== newProfile.phone) {
      const userWithPhone = await this.userRepository.findByPhone(
        newProfile.phone,
      );
      if (userWithPhone && userWithPhone.id !== userId) {
        throw new EntityAlreadyExistsException(
          '用户',
          '手机号',
          newProfile.phone,
        );
      }
    }

    // 如果身份证号发生变化，检查新身份证号是否已被其他用户使用
    if (currentProfile.cardNo !== newProfile.cardNo && newProfile.cardNo) {
      const userWithCardNo = await this.userRepository.findByCardNo(
        newProfile.cardNo,
      );
      if (userWithCardNo && userWithCardNo.id !== userId) {
        throw new EntityAlreadyExistsException(
          '用户',
          '身份证号',
          newProfile.cardNo,
        );
      }
    }
  }

  /**
   * 检查用户是否可以被删除
   */
  async canDeleteUser(userId: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      return false;
    }

    // 检查用户是否有关联的业务数据
    // 这里可以添加更多的业务规则检查
    // 例如：学生是否有未完成的请假申请、教师是否有未完成的审批等

    return true;
  }

  /**
   * 批量导入用户
   */
  async batchImportUsers(
    userDataList: Array<{
      userType: UserTypeEnum;
      profile: UserProfile;
      additionalData?: any;
    }>,
  ): Promise<User[]> {
    // 验证批量数据
    await this.validateBatchImportData(userDataList);

    const users: User[] = [];

    for (const userData of userDataList) {
      let user: User;

      switch (userData.userType) {
        case UserTypeEnum.STUDENT:
          user = await this.createStudent(
            userData.profile,
            userData.additionalData?.classId,
            userData.additionalData?.gradeId,
          );
          break;
        case UserTypeEnum.TEACHER:
          user = await this.createTeacher(
            userData.profile,
            userData.additionalData?.carNo,
          );
          break;
        default:
          throw new BusinessRuleException(
            `不支持的用户类型: ${userData.userType}`,
          );
      }

      users.push(user);
    }

    return await this.userRepository.saveMany(users);
  }

  /**
   * 确保手机号不存在
   */
  private async ensurePhoneNotExists(phone: string): Promise<void> {
    const exists = await this.userRepository.existsByPhone(phone);
    if (exists) {
      throw new EntityAlreadyExistsException('用户', '手机号', phone);
    }
  }

  /**
   * 确保身份证号不存在
   */
  private async ensureCardNoNotExists(cardNo: string): Promise<void> {
    const existingUser = await this.userRepository.findByCardNo(cardNo);
    if (existingUser) {
      throw new EntityAlreadyExistsException('用户', '身份证号', cardNo);
    }
  }

  /**
   * 验证学生特定的业务规则
   */
  private validateStudentRules(profile: UserProfile): void {
    // 学生年龄限制
    if (profile.age && (profile.age < 6 || profile.age > 25)) {
      throw new BusinessRuleException('学生年龄应在6-25岁之间');
    }

    // 其他学生特定的验证规则...
  }

  /**
   * 验证教师特定的业务规则
   */
  private validateTeacherRules(profile: UserProfile): void {
    // 教师年龄限制
    if (profile.age && (profile.age < 22 || profile.age > 65)) {
      throw new BusinessRuleException('教师年龄应在22-65岁之间');
    }

    // 教师必须是成年人
    if (!profile.isAdult()) {
      throw new BusinessRuleException('教师必须是成年人');
    }

    // 其他教师特定的验证规则...
  }

  /**
   * 验证批量导入数据
   */
  private async validateBatchImportData(
    userDataList: Array<{
      userType: UserTypeEnum;
      profile: UserProfile;
      additionalData?: any;
    }>,
  ): Promise<void> {
    const phones = userDataList.map((data) => data.profile.phone);
    const cardNos = userDataList
      .map((data) => data.profile.cardNo)
      .filter((cardNo) => cardNo);

    // 检查批量数据中是否有重复的手机号
    const duplicatePhones = phones.filter(
      (phone, index) => phones.indexOf(phone) !== index,
    );
    if (duplicatePhones.length > 0) {
      throw new BusinessRuleException(
        `批量数据中存在重复的手机号: ${duplicatePhones.join(', ')}`,
      );
    }

    // 检查批量数据中是否有重复的身份证号
    if (cardNos.length > 0) {
      const duplicateCardNos = cardNos.filter(
        (cardNo, index) => cardNos.indexOf(cardNo) !== index,
      );
      if (duplicateCardNos.length > 0) {
        throw new BusinessRuleException(
          `批量数据中存在重复的身份证号: ${duplicateCardNos.join(', ')}`,
        );
      }
    }

    // 检查数据库中是否已存在这些手机号和身份证号
    for (const phone of phones) {
      await this.ensurePhoneNotExists(phone);
    }

    for (const cardNo of cardNos) {
      if (cardNo) {
        await this.ensureCardNoNotExists(cardNo);
      }
    }
  }

  /**
   * 验证手机号唯一性（别名方法，用于应用服务）
   */
  async validatePhoneUniqueness(
    phone: string,
    excludeUserId?: string,
  ): Promise<void> {
    if (excludeUserId) {
      // 如果有排除的用户ID，需要检查是否是其他用户使用了这个手机号
      const existingUser = await this.userRepository.findByPhone(phone);
      if (existingUser && existingUser.id !== excludeUserId) {
        throw new EntityAlreadyExistsException('User', 'phone', phone);
      }
    } else {
      await this.ensurePhoneNotExists(phone);
    }
  }
}

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { TenantContext, TenantContextData } from '../../domain/context/tenant.context';
import { Reflector } from '@nestjs/core';
import { TENANT_ISOLATION_KEY, CROSS_TENANT_KEY } from '../../domain/decorators/tenant.decorator';

/**
 * 多租户拦截器
 * 自动设置租户上下文并进行租户隔离
 */
@Injectable()
export class TenantInterceptor implements NestInterceptor {
  constructor(private readonly reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // 提取租户信息
    const tenantContext = this.extractTenantContext(request);
    
    // 检查租户隔离设置
    const tenantIsolation = this.reflector.getAllAndOverride<boolean>(
      TENANT_ISOLATION_KEY,
      [context.getHandler(), context.getClass()]
    );
    
    const crossTenant = this.reflector.getAllAndOverride<boolean>(
      CROSS_TENANT_KEY,
      [context.getHandler(), context.getClass()]
    );

    // 如果启用了租户隔离且不允许跨租户访问，验证租户信息
    if (tenantIsolation !== false && !crossTenant) {
      this.validateTenantAccess(tenantContext, request);
    }

    // 在租户上下文中执行请求
    return new Observable(observer => {
      TenantContext.run(tenantContext, () => {
        next.handle().subscribe({
          next: (value) => observer.next(value),
          error: (error) => observer.error(error),
          complete: () => observer.complete(),
        });
      });
    });
  }

  /**
   * 从请求中提取租户上下文
   */
  private extractTenantContext(request: Request): TenantContextData {
    // 1. 从JWT令牌中提取
    const user = (request as any).user;
    if (user) {
      return TenantContext.extractFromJwtPayload(user);
    }

    // 2. 从请求头中提取
    const tenantIdFromHeader = request.headers['x-tenant-id'] as string;
    if (tenantIdFromHeader) {
      return TenantContext.createContext(tenantIdFromHeader);
    }

    // 3. 从查询参数中提取
    const tenantIdFromQuery = request.query.tenantId as string;
    if (tenantIdFromQuery) {
      return TenantContext.createContext(tenantIdFromQuery);
    }

    // 4. 从子域名中提取
    const tenantIdFromSubdomain = this.extractTenantFromSubdomain(request);
    if (tenantIdFromSubdomain) {
      return TenantContext.createContext(tenantIdFromSubdomain);
    }

    // 5. 使用默认租户
    return TenantContext.createContext(TenantContext.getDefaultTenantId());
  }

  /**
   * 从子域名中提取租户ID
   */
  private extractTenantFromSubdomain(request: Request): string | null {
    const host = request.get('host');
    if (!host) return null;

    // 假设格式为: tenant.domain.com
    const parts = host.split('.');
    if (parts.length >= 3) {
      const subdomain = parts[0];
      // 排除常见的非租户子域名
      const excludedSubdomains = ['www', 'api', 'admin', 'app'];
      if (!excludedSubdomains.includes(subdomain)) {
        return subdomain;
      }
    }

    return null;
  }

  /**
   * 验证租户访问权限
   */
  private validateTenantAccess(tenantContext: TenantContextData, request: Request): void {
    if (!tenantContext.tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }

    // 这里可以添加更多的租户验证逻辑
    // 例如检查租户是否存在、是否激活等
  }
}

/**
 * 租户上下文提取器
 * 用于从不同来源提取租户信息
 */
@Injectable()
export class TenantExtractor {
  /**
   * 从JWT载荷中提取租户信息
   */
  extractFromJwt(payload: any): string {
    return payload.tenantId || payload.tenant_id || TenantContext.getDefaultTenantId();
  }

  /**
   * 从请求头中提取租户信息
   */
  extractFromHeaders(request: Request): string | null {
    return (
      request.headers['x-tenant-id'] ||
      request.headers['tenant-id'] ||
      request.headers['tenantid']
    ) as string | null;
  }

  /**
   * 从查询参数中提取租户信息
   */
  extractFromQuery(request: Request): string | null {
    return (
      request.query.tenantId ||
      request.query.tenant_id ||
      request.query.tenant
    ) as string | null;
  }

  /**
   * 从路径参数中提取租户信息
   */
  extractFromPath(request: Request): string | null {
    // 假设路径格式为: /api/tenants/:tenantId/...
    const pathParts = request.path.split('/');
    const tenantIndex = pathParts.indexOf('tenants');
    
    if (tenantIndex !== -1 && pathParts.length > tenantIndex + 1) {
      return pathParts[tenantIndex + 1];
    }

    return null;
  }

  /**
   * 从Cookie中提取租户信息
   */
  extractFromCookie(request: Request): string | null {
    return request.cookies?.tenantId || request.cookies?.tenant_id || null;
  }

  /**
   * 按优先级提取租户信息
   */
  extractWithPriority(request: Request, user?: any): string {
    // 优先级：JWT > Header > Query > Path > Cookie > Default
    return (
      (user && this.extractFromJwt(user)) ||
      this.extractFromHeaders(request) ||
      this.extractFromQuery(request) ||
      this.extractFromPath(request) ||
      this.extractFromCookie(request) ||
      TenantContext.getDefaultTenantId()
    );
  }
}

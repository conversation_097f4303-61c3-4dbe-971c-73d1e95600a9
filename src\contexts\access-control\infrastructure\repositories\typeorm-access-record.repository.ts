import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan, <PERSON>Than } from 'typeorm';
import { TypeOrmBaseRepository } from '@/shared/infrastructure/repositories/typeorm-base.repository';
import { AccessRecord, AccessType, AccessRecordStatus } from '../../domain/aggregates/access-record.aggregate';
import { AccessRecordRepository, AccessRecordQueryRepository } from '../../domain/repositories/access-record.repository';

/**
 * TypeORM门禁记录仓储实现
 */
@Injectable()
export class TypeOrmAccessRecordRepository
  extends TypeOrmBaseRepository<AccessRecord>
  implements AccessRecordRepository, AccessRecordQueryRepository {

  constructor(
    @InjectRepository(AccessRecord)
    repository: Repository<AccessRecord>,
  ) {
    super(repository, 'AccessRecordRepository');
  }

  /**
   * 根据用户ID查找门禁记录
   */
  async findByUserId(userId: string): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const records = await this.repository
        .createQueryBuilder('record')
        .where('record.userId = :userId', { userId })
        .orderBy('record.accessTime', 'DESC')
        .getMany();

      this.logPerformance('findByUserId', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding records by user ID: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 根据用户ID和时间范围查找门禁记录
   */
  async findByUserIdAndTimeRange(
    userId: string,
    startTime: Date,
    endTime: Date
  ): Promise<AccessRecord[]> {
    const queryStartTime = Date.now();
    try {
      const records = await this.repository
        .createQueryBuilder('record')
        .where('record.userId = :userId', { userId })
        .andWhere('record.accessTime BETWEEN :startTime AND :endTime', { startTime, endTime })
        .orderBy('record.accessTime', 'DESC')
        .getMany();

      this.logPerformance('findByUserIdAndTimeRange', queryStartTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding records by user ID and time range`, error);
      throw error;
    }
  }

  /**
   * 根据设备ID查找门禁记录
   */
  async findByDeviceId(deviceId: string): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const records = await this.repository
        .createQueryBuilder('record')
        .where('record.deviceId = :deviceId', { deviceId })
        .orderBy('record.accessTime', 'DESC')
        .getMany();

      this.logPerformance('findByDeviceId', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding records by device ID: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 根据位置查找门禁记录
   */
  async findByLocation(locationName: string, building?: string): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const whereConditions: any = { locationName };
      if (building) {
        whereConditions.locationBuilding = building;
      }

      const records = await this.repository.find({
        where: whereConditions,
        order: { createdAt: 'DESC' },
      });

      this.logPerformance('findByLocation', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding records by location`, error);
      throw error;
    }
  }

  /**
   * 查找异常门禁记录
   */
  async findAbnormalRecords(limit: number = 100): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const query = this.repository
        .createQueryBuilder('record')
        .where('record.status = :status', { status: AccessRecordStatus.ABNORMAL })
        .orderBy('record.accessTime', 'DESC');

      if (limit) {
        query.limit(limit);
      }

      const records = await query.getMany();

      this.logPerformance('findAbnormalRecords', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding abnormal records`, error);
      throw error;
    }
  }

  /**
   * 查找可疑门禁记录
   */
  async findSuspiciousRecords(limit: number = 100): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const query = this.repository
        .createQueryBuilder('record')
        .where('record.status = :status', { status: AccessRecordStatus.SUSPICIOUS })
        .orderBy('record.accessTime', 'DESC');

      if (limit) {
        query.limit(limit);
      }

      const records = await query.getMany();

      this.logPerformance('findSuspiciousRecords', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error finding suspicious records`, error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取最后一条门禁记录
   */
  async findLastRecordByUserId(userId: string): Promise<AccessRecord | null> {
    const startTime = Date.now();
    try {
      const record = await this.repository
        .createQueryBuilder('record')
        .where('record.userId = :userId', { userId })
        .orderBy('record.accessTime', 'DESC')
        .getOne();

      this.logPerformance('findLastRecordByUserId', startTime);
      return record;
    } catch (error) {
      this.logger.error(`Error finding last record by user ID: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 检查用户是否在校内
   */
  async isUserInCampus(userId: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      const lastRecord = await this.findLastRecordByUserId(userId);

      if (!lastRecord) {
        return false;
      }

      // 如果最后一条记录是进入，则认为用户在校内
      const isInCampus = lastRecord.getAccessType() === AccessType.ENTER;

      this.logPerformance('isUserInCampus', startTime);
      return isInCampus;
    } catch (error) {
      this.logger.error(`Error checking if user is in campus: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 根据条件搜索门禁记录
   */
  async searchRecords(criteria: {
    userId?: string;
    accessType?: AccessType;
    status?: AccessRecordStatus;
    locationName?: string;
    building?: string;
    deviceId?: string;
    startTime?: Date;
    endTime?: Date;
    verificationMethod?: string;
    minConfidenceScore?: number;
    maxTemperature?: number;
    minTemperature?: number;
  }): Promise<AccessRecord[]> {
    const startTime = Date.now();
    try {
      const queryBuilder = this.repository.createQueryBuilder('record');

      // 基础条件
      if (criteria.userId) {
        queryBuilder.andWhere('record.userId = :userId', { userId: criteria.userId });
      }

      if (criteria.accessType) {
        queryBuilder.andWhere('record.accessType = :accessType', { accessType: criteria.accessType });
      }

      if (criteria.status) {
        queryBuilder.andWhere('record.status = :status', { status: criteria.status });
      }

      if (criteria.locationName) {
        queryBuilder.andWhere('record.locationName LIKE :locationName', {
          locationName: `%${criteria.locationName}%`
        });
      }

      if (criteria.building) {
        queryBuilder.andWhere('record.locationBuilding = :building', { building: criteria.building });
      }

      if (criteria.deviceId) {
        queryBuilder.andWhere('record.deviceId = :deviceId', { deviceId: criteria.deviceId });
      }

      // 时间范围
      if (criteria.startTime) {
        queryBuilder.andWhere('record.accessTime >= :startTime', { startTime: criteria.startTime });
      }

      if (criteria.endTime) {
        queryBuilder.andWhere('record.accessTime <= :endTime', { endTime: criteria.endTime });
      }

      // 验证相关条件
      if (criteria.verificationMethod) {
        queryBuilder.andWhere('record.verificationMethod = :verificationMethod', {
          verificationMethod: criteria.verificationMethod
        });
      }

      if (criteria.minConfidenceScore !== undefined) {
        queryBuilder.andWhere('record.confidenceScore >= :minConfidenceScore', {
          minConfidenceScore: criteria.minConfidenceScore
        });
      }

      if (criteria.minTemperature !== undefined) {
        queryBuilder.andWhere('record.temperature >= :minTemperature', {
          minTemperature: criteria.minTemperature
        });
      }

      if (criteria.maxTemperature !== undefined) {
        queryBuilder.andWhere('record.temperature <= :maxTemperature', {
          maxTemperature: criteria.maxTemperature
        });
      }

      const records = await queryBuilder
        .orderBy('record.accessTime', 'DESC')
        .getMany();

      this.logPerformance('searchRecords', startTime);
      return records;
    } catch (error) {
      this.logger.error(`Error searching records`, error);
      throw error;
    }
  }

  /**
   * 获取门禁统计信息
   */
  async getAccessStatistics(
    startTime: Date,
    endTime: Date
  ): Promise<{
    totalRecords: number;
    enterRecords: number;
    leaveRecords: number;
    abnormalRecords: number;
    suspiciousRecords: number;
    uniqueUsers: number;
    averageTemperature: number;
    deviceUsage: Array<{ deviceId: string; count: number }>;
    locationUsage: Array<{ locationName: string; count: number }>;
    hourlyDistribution: Array<{ hour: number; count: number }>;
  }> {
    const queryStartTime = Date.now();
    try {
      const baseQuery = this.repository.createQueryBuilder('record')
        .where('record.accessTime BETWEEN :startTime AND :endTime', { startTime, endTime });

      // 基础统计
      const [
        totalRecords,
        enterRecords,
        leaveRecords,
        abnormalRecords,
        suspiciousRecords,
      ] = await Promise.all([
        baseQuery.getCount(),
        baseQuery.clone().andWhere('record.accessType = :type', { type: AccessType.ENTER }).getCount(),
        baseQuery.clone().andWhere('record.accessType = :type', { type: AccessType.LEAVE }).getCount(),
        baseQuery.clone().andWhere('record.status = :status', { status: AccessRecordStatus.ABNORMAL }).getCount(),
        baseQuery.clone().andWhere('record.status = :status', { status: AccessRecordStatus.SUSPICIOUS }).getCount(),
      ]);

      // 唯一用户数
      const uniqueUsersResult = await baseQuery.clone()
        .select('COUNT(DISTINCT record.userId)', 'count')
        .getRawOne();
      const uniqueUsers = parseInt(uniqueUsersResult.count);

      // 平均体温
      const avgTempResult = await baseQuery.clone()
        .select('AVG(record.temperature)', 'avgTemp')
        .where('record.temperature IS NOT NULL')
        .getRawOne();
      const averageTemperature = parseFloat(avgTempResult.avgTemp) || 0;

      // 设备使用统计
      const deviceUsage = await baseQuery.clone()
        .select('record.deviceId', 'deviceId')
        .addSelect('COUNT(*)', 'count')
        .where('record.deviceId IS NOT NULL')
        .groupBy('record.deviceId')
        .orderBy('count', 'DESC')
        .limit(10)
        .getRawMany();

      // 位置使用统计
      const locationUsage = await baseQuery.clone()
        .select('record.locationName', 'locationName')
        .addSelect('COUNT(*)', 'count')
        .groupBy('record.locationName')
        .orderBy('count', 'DESC')
        .limit(10)
        .getRawMany();

      // 小时分布统计
      const hourlyDistribution = await baseQuery.clone()
        .select('HOUR(record.accessTime)', 'hour')
        .addSelect('COUNT(*)', 'count')
        .groupBy('HOUR(record.accessTime)')
        .orderBy('hour', 'ASC')
        .getRawMany();

      this.logPerformance('getAccessStatistics', queryStartTime);

      return {
        totalRecords,
        enterRecords,
        leaveRecords,
        abnormalRecords,
        suspiciousRecords,
        uniqueUsers,
        averageTemperature,
        deviceUsage: deviceUsage.map(item => ({
          deviceId: item.deviceId,
          count: parseInt(item.count),
        })),
        locationUsage: locationUsage.map(item => ({
          locationName: item.locationName,
          count: parseInt(item.count),
        })),
        hourlyDistribution: hourlyDistribution.map((item) => ({
          hour: parseInt(item.hour),
          count: parseInt(item.count),
        })),
      };
    } catch (error) {
      this.logger.error(`Error getting access statistics`, error);
      throw error;
    }
  }

  /**
   * 获取用户门禁统计
   */
  async getUserAccessStatistics(
    userId: string,
    startTime: Date,
    endTime: Date
  ): Promise<{
    totalRecords: number;
    enterRecords: number;
    leaveRecords: number;
    abnormalRecords: number;
    averageTemperature: number;
    mostUsedLocation: string;
    firstAccessTime: Date | null;
    lastAccessTime: Date | null;
  }> {
    const queryStartTime = Date.now();
    try {
      const baseQuery = this.repository.createQueryBuilder('record')
        .where('record.userId = :userId', { userId })
        .andWhere('record.accessTime BETWEEN :startTime AND :endTime', { startTime, endTime });

      // 基础统计
      const [
        totalRecords,
        enterRecords,
        leaveRecords,
        abnormalRecords,
      ] = await Promise.all([
        baseQuery.getCount(),
        baseQuery.clone().andWhere('record.accessType = :type', { type: AccessType.ENTER }).getCount(),
        baseQuery.clone().andWhere('record.accessType = :type', { type: AccessType.LEAVE }).getCount(),
        baseQuery.clone().andWhere('record.status = :status', { status: AccessRecordStatus.ABNORMAL }).getCount(),
      ]);

      // 平均体温
      const avgTempResult = await baseQuery.clone()
        .select('AVG(record.temperature)', 'avgTemp')
        .where('record.temperature IS NOT NULL')
        .getRawOne();
      const averageTemperature = parseFloat(avgTempResult.avgTemp) || 0;

      // 最常用位置
      const mostUsedLocationResult = await baseQuery.clone()
        .select('record.locationName', 'locationName')
        .addSelect('COUNT(*)', 'count')
        .groupBy('record.locationName')
        .orderBy('count', 'DESC')
        .limit(1)
        .getRawOne();
      const mostUsedLocation = mostUsedLocationResult?.locationName || '';

      // 首次和最后访问时间
      const timeRangeResult = await baseQuery.clone()
        .select('MIN(record.accessTime)', 'firstTime')
        .addSelect('MAX(record.accessTime)', 'lastTime')
        .getRawOne();

      this.logPerformance('getUserAccessStatistics', queryStartTime);

      return {
        totalRecords,
        enterRecords,
        leaveRecords,
        abnormalRecords,
        averageTemperature,
        mostUsedLocation,
        firstAccessTime: timeRangeResult.firstTime,
        lastAccessTime: timeRangeResult.lastTime,
      };
    } catch (error) {
      this.logger.error(`Error getting user access statistics: ${userId}`, error);
      throw error;
    }
  }

  /**
   * 获取实时在校人数
   */
  async getCurrentCampusPopulation(): Promise<{
    totalInCampus: number;
    studentCount: number;
    teacherCount: number;
    visitorCount: number;
    locationDistribution: Array<{ locationName: string; count: number }>;
  }> {
    // 这是一个复杂的查询，需要找到每个用户的最后一条记录
    // 如果是进入记录，则认为用户在校内
    const queryStartTime = Date.now();
    try {
      // 使用子查询找到每个用户的最后一条记录
      const lastRecordsQuery = `
        SELECT r1.userId, r1.accessType, r1.locationName, u.userType
        FROM access_records r1
        INNER JOIN users u ON r1.userId = u.id
        INNER JOIN (
          SELECT userId, MAX(accessTime) as maxTime
          FROM access_records
          WHERE deletedAt IS NULL
          GROUP BY userId
        ) r2 ON r1.userId = r2.userId AND r1.accessTime = r2.maxTime
        WHERE r1.deletedAt IS NULL AND r1.accessType = 'enter'
      `;

      const results = await this.repository.query(lastRecordsQuery);

      const totalInCampus = results.length;
      const studentCount = results.filter((r: any) => r.userType === 'student').length;
      const teacherCount = results.filter((r: any) => r.userType === 'teacher').length;
      const visitorCount = results.filter((r: any) => r.userType === 'visitor').length;

      // 位置分布统计
      const locationMap = new Map<string, number>();
      results.forEach((r: any) => {
        const count = locationMap.get(r.locationName) || 0;
        locationMap.set(r.locationName, count + 1);
      });

      const locationDistribution = Array.from(locationMap.entries())
        .map(([locationName, count]) => ({ locationName, count }))
        .sort((a, b) => b.count - a.count);

      this.logPerformance('getCurrentCampusPopulation', queryStartTime);

      return {
        totalInCampus,
        studentCount,
        teacherCount,
        visitorCount,
        locationDistribution,
      };
    } catch (error) {
      this.logger.error(`Error getting current campus population`, error);
      throw error;
    }
  }

  /**
   * 查找长时间未离开的用户
   */
  async findUsersStayingTooLong(hours: number): Promise<Array<{
    userId: string;
    lastEnterTime: Date;
    location: string;
    stayDuration: number;
  }>> {
    const queryStartTime = Date.now();
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

    try {
      const query = `
        SELECT r1.userId, r1.accessTime as lastEnterTime, r1.locationName as location,
               TIMESTAMPDIFF(HOUR, r1.accessTime, NOW()) as stayDuration
        FROM access_records r1
        INNER JOIN (
          SELECT userId, MAX(accessTime) as maxTime
          FROM access_records
          WHERE deletedAt IS NULL
          GROUP BY userId
        ) r2 ON r1.userId = r2.userId AND r1.accessTime = r2.maxTime
        WHERE r1.deletedAt IS NULL
          AND r1.accessType = 'enter'
          AND r1.accessTime < ?
      `;

      const results = await this.repository.query(query, [cutoffTime]);

      this.logPerformance('findUsersStayingTooLong', queryStartTime);

      return results.map((r: any) => ({
        userId: r.userId,
        lastEnterTime: r.lastEnterTime,
        location: r.location,
        stayDuration: r.stayDuration,
      }));
    } catch (error) {
      this.logger.error(`Error finding users staying too long`, error);
      throw error;
    }
  }

  /**
   * 查找频繁进出的用户
   */
  async findFrequentAccessUsers(
    startTime: Date,
    endTime: Date,
    minAccessCount: number
  ): Promise<Array<{
    userId: string;
    accessCount: number;
    enterCount: number;
    leaveCount: number;
  }>> {
    const queryStartTime = Date.now();
    try {
      const query = `
        SELECT
          userId,
          COUNT(*) as accessCount,
          SUM(CASE WHEN accessType = 'enter' THEN 1 ELSE 0 END) as enterCount,
          SUM(CASE WHEN accessType = 'leave' THEN 1 ELSE 0 END) as leaveCount
        FROM access_records
        WHERE deletedAt IS NULL
          AND accessTime BETWEEN ? AND ?
        GROUP BY userId
        HAVING accessCount >= ?
        ORDER BY accessCount DESC
      `;

      const results = await this.repository.query(query, [startTime, endTime, minAccessCount]);

      this.logPerformance('findFrequentAccessUsers', queryStartTime);

      return results.map((r: any) => ({
        userId: r.userId,
        accessCount: parseInt(r.accessCount),
        enterCount: parseInt(r.enterCount),
        leaveCount: parseInt(r.leaveCount),
      }));
    } catch (error) {
      this.logger.error(`Error finding frequent access users`, error);
      throw error;
    }
  }

  /**
   * 获取设备健康状态
   */
  async getDeviceHealthStatus(): Promise<Array<{
    deviceId: string;
    lastActiveTime: Date;
    totalRecords: number;
    abnormalRecords: number;
    averageConfidenceScore: number;
    status: 'online' | 'offline' | 'warning';
  }>> {
    const queryStartTime = Date.now();
    try {
      const query = `
        SELECT
          deviceId,
          MAX(accessTime) as lastActiveTime,
          COUNT(*) as totalRecords,
          SUM(CASE WHEN status = 'abnormal' THEN 1 ELSE 0 END) as abnormalRecords,
          AVG(CASE WHEN confidenceScore IS NOT NULL THEN confidenceScore ELSE NULL END) as averageConfidenceScore
        FROM access_records
        WHERE deletedAt IS NULL
          AND deviceId IS NOT NULL
          AND accessTime >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY deviceId
        ORDER BY lastActiveTime DESC
      `;

      const results = await this.repository.query(query);

      this.logPerformance('getDeviceHealthStatus', queryStartTime);

      const now = new Date();
      return results.map((r: any) => {
        const lastActiveTime = new Date(r.lastActiveTime);
        const hoursSinceLastActive = (now.getTime() - lastActiveTime.getTime()) / (1000 * 60 * 60);
        const abnormalRate = r.totalRecords > 0 ? r.abnormalRecords / r.totalRecords : 0;

        let status: 'online' | 'offline' | 'warning';
        if (hoursSinceLastActive > 2) {
          status = 'offline';
        } else if (abnormalRate > 0.1 || (r.averageConfidenceScore && r.averageConfidenceScore < 0.8)) {
          status = 'warning';
        } else {
          status = 'online';
        }

        return {
          deviceId: r.deviceId,
          lastActiveTime,
          totalRecords: parseInt(r.totalRecords),
          abnormalRecords: parseInt(r.abnormalRecords),
          averageConfidenceScore: parseFloat(r.averageConfidenceScore) || 0,
          status,
        };
      });
    } catch (error) {
      this.logger.error(`Error getting device health status`, error);
      throw error;
    }
  }

  /**
   * 分页查询门禁记录
   */
  async findRecordsWithPagination(
    page: number,
    pageSize: number,
    condition?: {
      userId?: string;
      deviceId?: string;
      status?: AccessRecordStatus;
      startTime?: Date;
      endTime?: Date;
    }
  ): Promise<{
    data: AccessRecord[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const startTime = Date.now();
    try {
      const query = this.repository.createQueryBuilder('record');

      if (condition?.userId) {
        query.andWhere('record.userId = :userId', { userId: condition.userId });
      }

      if (condition?.deviceId) {
        query.andWhere('record.deviceId = :deviceId', { deviceId: condition.deviceId });
      }

      if (condition?.status) {
        query.andWhere('record.status = :status', { status: condition.status });
      }

      if (condition?.startTime && condition?.endTime) {
        query.andWhere('record.accessTime BETWEEN :startTime AND :endTime', {
          startTime: condition.startTime,
          endTime: condition.endTime,
        });
      }

      query.orderBy('record.accessTime', 'DESC');

      const [data, total] = await query
        .skip((page - 1) * pageSize)
        .take(pageSize)
        .getManyAndCount();

      const totalPages = Math.ceil(total / pageSize);

      this.logPerformance('findWithPagination', startTime);
      return {
        data,
        total,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      this.logger.error('Error in findWithPagination', error);
      throw error;
    }
  }

  /**
   * 根据条件查找
   */
  async findByCriteria(criteria: any): Promise<AccessRecord[]> {
    return await this.findMany({
      where: this.buildWhereConditions(criteria),
      order: this.buildOrderConditions(),
    });
  }

  /**
   * 获取实体名称
   */
  protected getEntityName(): string {
    return '门禁记录';
  }
}

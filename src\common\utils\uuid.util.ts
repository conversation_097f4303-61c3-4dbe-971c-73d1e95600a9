import { v4 as uuidv4, validate as uuidValidate } from 'uuid';

/**
 * UUID 工具类
 */
export class UuidUtil {
  /**
   * 生成新的 UUID
   */
  static generate(): string {
    return uuidv4();
  }

  /**
   * 验证 UUID 格式
   */
  static validate(uuid: string): boolean {
    return uuidValidate(uuid);
  }

  /**
   * 生成短 UUID（去掉连字符）
   */
  static generateShort(): string {
    return uuidv4().replace(/-/g, '');
  }

  /**
   * 格式化 UUID（添加连字符）
   */
  static format(uuid: string): string {
    if (uuid.length === 32) {
      return [
        uuid.slice(0, 8),
        uuid.slice(8, 12),
        uuid.slice(12, 16),
        uuid.slice(16, 20),
        uuid.slice(20, 32),
      ].join('-');
    }
    return uuid;
  }
}

import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { ApiSecurity, ApiResponse } from '@nestjs/swagger';

/**
 * 开发者专用装饰器元数据键
 */
export const DEVELOPER_ONLY_KEY = 'developer_only';
export const DEV_ENVIRONMENT_ONLY_KEY = 'dev_environment_only';
export const API_KEY_REQUIRED_KEY = 'api_key_required';

/**
 * 标记接口仅限开发者访问
 */
export const DeveloperOnly = () => SetMetadata(DEVELOPER_ONLY_KEY, true);

/**
 * 标记接口仅在开发环境可用
 */
export const DevEnvironmentOnly = () => SetMetadata(DEV_ENVIRONMENT_ONLY_KEY, true);

/**
 * 标记接口需要API密钥认证
 */
export const ApiKeyRequired = () => SetMetadata(API_KEY_REQUIRED_KEY, true);

/**
 * 开发者API装饰器
 * 组合多个装饰器，用于开发者专用接口
 */
export function DeveloperApi(options?: {
  requireApiKey?: boolean;
  devEnvironmentOnly?: boolean;
  description?: string;
}) {
  const decorators = [
    DeveloperOnly(),
    ApiSecurity('bearer'),
    ApiResponse({
      status: 403,
      description: '权限不足 - 仅限开发者访问',
    }),
  ];

  if (options?.requireApiKey) {
    decorators.push(
      ApiKeyRequired(),
      ApiSecurity('api-key'),
      ApiResponse({
        status: 401,
        description: 'API密钥无效或缺失',
      }),
    );
  }

  if (options?.devEnvironmentOnly) {
    decorators.push(
      DevEnvironmentOnly(),
      ApiResponse({
        status: 503,
        description: '服务不可用 - 仅在开发环境可用',
      }),
    );
  }

  return applyDecorators(...decorators);
}

/**
 * 数据初始化API装饰器
 * 用于数据初始化相关的接口
 */
export function DataInitializationApi() {
  return applyDecorators(
    DeveloperOnly(),
    DevEnvironmentOnly(),
    ApiKeyRequired(),
    ApiSecurity('bearer'),
    ApiSecurity('api-key'),
    ApiResponse({
      status: 200,
      description: '数据初始化成功',
    }),
    ApiResponse({
      status: 403,
      description: '权限不足 - 仅限开发者在开发环境使用',
    }),
    ApiResponse({
      status: 409,
      description: '数据已存在，无需重复初始化',
    }),
  );
}

/**
 * 系统管理API装饰器
 * 用于系统管理相关的接口
 */
export function SystemManagementApi() {
  return applyDecorators(
    DeveloperOnly(),
    ApiKeyRequired(),
    ApiSecurity('bearer'),
    ApiSecurity('api-key'),
    ApiResponse({
      status: 200,
      description: '操作成功',
    }),
    ApiResponse({
      status: 403,
      description: '权限不足 - 仅限开发者访问',
    }),
    ApiResponse({
      status: 500,
      description: '系统操作失败',
    }),
  );
}

/**
 * 调试API装饰器
 * 用于调试和诊断相关的接口
 */
export function DebugApi() {
  return applyDecorators(
    DeveloperOnly(),
    DevEnvironmentOnly(),
    ApiSecurity('bearer'),
    ApiResponse({
      status: 200,
      description: '调试信息获取成功',
    }),
    ApiResponse({
      status: 403,
      description: '权限不足 - 仅限开发者在开发环境使用',
    }),
  );
}

/**
 * 测试数据API装饰器
 * 用于测试数据生成和管理的接口
 */
export function TestDataApi() {
  return applyDecorators(
    DeveloperOnly(),
    DevEnvironmentOnly(),
    ApiKeyRequired(),
    ApiSecurity('bearer'),
    ApiSecurity('api-key'),
    ApiResponse({
      status: 201,
      description: '测试数据创建成功',
    }),
    ApiResponse({
      status: 200,
      description: '测试数据操作成功',
    }),
    ApiResponse({
      status: 403,
      description: '权限不足 - 仅限开发者在开发环境使用',
    }),
    ApiResponse({
      status: 422,
      description: '测试数据参数无效',
    }),
  );
}

import { ValueObject } from '@/shared/domain/base/value-object';
import { ValidationException } from '@/shared/application/exceptions/domain-exception';

/**
 * 用户类型枚举
 */
export enum UserTypeEnum {
  STUDENT = 'student',
  TEACHER = 'teacher',
  PARENT = 'parent',
  VISITOR = 'visitor',
  DEVELOPER = 'developer',  // 新增开发者类型
}

/**
 * 用户类型值对象
 */
export class UserType extends ValueObject<UserTypeEnum> {
  constructor(type: UserTypeEnum) {
    UserType.validate(type);
    super(type);
  }

  private static validate(type: UserTypeEnum): void {
    if (!Object.values(UserTypeEnum).includes(type)) {
      throw new ValidationException({
        userType: ['无效的用户类型']
      });
    }
  }

  get type(): UserTypeEnum {
    return this._value;
  }

  /**
   * 是否为学生
   */
  isStudent(): boolean {
    return this._value === UserTypeEnum.STUDENT;
  }

  /**
   * 是否为教师
   */
  isTeacher(): boolean {
    return this._value === UserTypeEnum.TEACHER;
  }

  /**
   * 是否为家长
   */
  isParent(): boolean {
    return this._value === UserTypeEnum.PARENT;
  }

  /**
   * 是否为访客
   */
  isVisitor(): boolean {
    return this._value === UserTypeEnum.VISITOR;
  }

  /**
   * 是否为内部用户（学生或教师）
   */
  isInternal(): boolean {
    return this.isStudent() || this.isTeacher();
  }

  /**
   * 是否为外部用户（家长或访客）
   */
  isExternal(): boolean {
    return this.isParent() || this.isVisitor();
  }

  /**
   * 获取用户类型显示名称
   */
  getDisplayName(): string {
    const displayNames = {
      [UserTypeEnum.STUDENT]: '学生',
      [UserTypeEnum.TEACHER]: '教师',
      [UserTypeEnum.PARENT]: '家长',
      [UserTypeEnum.VISITOR]: '访客',
      [UserTypeEnum.DEVELOPER]: '开发者',
    };
    return displayNames[this._value];
  }

  /**
   * 获取用户权限级别
   */
  getPermissionLevel(): number {
    const levels = {
      [UserTypeEnum.VISITOR]: 1,
      [UserTypeEnum.PARENT]: 2,
      [UserTypeEnum.STUDENT]: 3,
      [UserTypeEnum.TEACHER]: 4,
      [UserTypeEnum.DEVELOPER]: 5, // 开发者拥有最高权限
    };
    return levels[this._value];
  }

  /**
   * 检查是否有权限访问某个资源
   */
  canAccess(requiredLevel: number): boolean {
    return this.getPermissionLevel() >= requiredLevel;
  }

  /**
   * 创建学生类型
   */
  static student(): UserType {
    return new UserType(UserTypeEnum.STUDENT);
  }

  /**
   * 创建教师类型
   */
  static teacher(): UserType {
    return new UserType(UserTypeEnum.TEACHER);
  }

  /**
   * 创建家长类型
   */
  static parent(): UserType {
    return new UserType(UserTypeEnum.PARENT);
  }

  /**
   * 创建访客类型
   */
  static visitor(): UserType {
    return new UserType(UserTypeEnum.VISITOR);
  }

  /**
   * 创建开发者类型
   */
  static developer(): UserType {
    return new UserType(UserTypeEnum.DEVELOPER);
  }

  /**
   * 是否为开发者
   */
  isDeveloper(): boolean {
    return this._value === UserTypeEnum.DEVELOPER;
  }
}

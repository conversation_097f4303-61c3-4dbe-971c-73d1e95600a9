import { IsString, IsOptional, <PERSON>Email, IsPhoneNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 创建家长用户 DTO
 */
export class CreateUserParentDto {
  @ApiProperty({ description: '用户名' })
  @IsString()
  username: string;

  @ApiProperty({ description: '密码' })
  @IsString()
  password: string;

  @ApiProperty({ description: '真实姓名' })
  @IsString()
  realName: string;

  @ApiPropertyOptional({ description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  @IsOptional()
  @IsString()
  idCard?: string;

  @ApiPropertyOptional({ description: '工作单位' })
  @IsOptional()
  @IsString()
  workplace?: string;

  @ApiPropertyOptional({ description: '职业' })
  @IsOptional()
  @IsString()
  occupation?: string;

  @ApiPropertyOptional({ description: '与学生关系' })
  @IsOptional()
  @IsString()
  relationship?: string;
}

/**
 * 更新家长用户 DTO
 */
export class UpdateUserParentDto {
  @ApiPropertyOptional({ description: '真实姓名' })
  @IsOptional()
  @IsString()
  realName?: string;

  @ApiPropertyOptional({ description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  @IsOptional()
  @IsString()
  idCard?: string;

  @ApiPropertyOptional({ description: '工作单位' })
  @IsOptional()
  @IsString()
  workplace?: string;

  @ApiPropertyOptional({ description: '职业' })
  @IsOptional()
  @IsString()
  occupation?: string;

  @ApiPropertyOptional({ description: '与学生关系' })
  @IsOptional()
  @IsString()
  relationship?: string;
}

/**
 * 家长用户响应 DTO
 */
export class UserParentResponseDto {
  @ApiProperty({ description: '用户ID' })
  id: string;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiProperty({ description: '真实姓名' })
  realName: string;

  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  idCard?: string;

  @ApiPropertyOptional({ description: '工作单位' })
  workplace?: string;

  @ApiPropertyOptional({ description: '职业' })
  occupation?: string;

  @ApiPropertyOptional({ description: '与学生关系' })
  relationship?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

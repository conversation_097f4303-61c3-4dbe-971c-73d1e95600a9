import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

/**
 * Swagger配置
 */
export const createSwaggerConfig = () => {
  return new DocumentBuilder()
    .setTitle('智慧校园 API')
    .setDescription('基于DDD架构的智慧校园系统API文档')
    .setVersion('2.0')
    .addTag('身份访问', '用户认证、授权和身份管理')
    .addTag('用户管理', '学生、教师、家长、访客管理')
    .addTag('多租户', '租户管理和数据隔离')
    .addTag('审计', '操作审计和日志记录')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .build();
};

/**
 * 设置Swagger文档
 */
export const setupSwagger = (app: INestApplication): void => {
  const config = createSwaggerConfig();
  const document = SwaggerModule.createDocument(app, config);
  
  SwaggerModule.setup('api-docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
    customSiteTitle: '智慧校园 API 文档',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info { margin: 20px 0 }
      .swagger-ui .info .title { color: #3b4151 }
    `,
  });
};

/**
 * 预定义的响应模式
 */
export const ApiResponseSchemas = {
  // 成功响应
  Success: (dataType?: string) => ({
    type: 'object',
    properties: {
      errCode: { type: 'number', example: 0 },
      message: { type: 'string', example: 'success' },
      data: dataType ? { $ref: `#/components/schemas/${dataType}` } : { type: 'object' },
      timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
    },
  }),

  // 分页响应
  Pagination: (itemType?: string) => ({
    type: 'object',
    properties: {
      errCode: { type: 'number', example: 0 },
      message: { type: 'string', example: 'success' },
      data: {
        type: 'object',
        properties: {
          list: {
            type: 'array',
            items: itemType ? { $ref: `#/components/schemas/${itemType}` } : { type: 'object' },
          },
          pagination: {
            type: 'object',
            properties: {
              total: { type: 'number', example: 100 },
              page: { type: 'number', example: 1 },
              pageSize: { type: 'number', example: 10 },
              totalPages: { type: 'number', example: 10 },
            },
          },
        },
      },
      timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
    },
  }),

  // 错误响应
  Error: (status: number, message: string) => ({
    type: 'object',
    properties: {
      errCode: { type: 'number', example: status },
      message: { type: 'string', example: message },
      data: { type: 'null', example: null },
      timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
    },
  }),

  // 常用错误响应
  BadRequest: () => ApiResponseSchemas.Error(400, '请求参数错误'),
  Unauthorized: () => ApiResponseSchemas.Error(401, '未授权访问'),
  Forbidden: () => ApiResponseSchemas.Error(403, '权限不足'),
  NotFound: () => ApiResponseSchemas.Error(404, '资源不存在'),
  Conflict: () => ApiResponseSchemas.Error(409, '资源冲突'),
  InternalServerError: () => ApiResponseSchemas.Error(500, '服务器内部错误'),
};

/**
 * 常用API操作装饰器配置
 */
export const ApiOperations = {
  // CRUD操作
  Create: (resource: string) => ({
    summary: `创建${resource}`,
    description: `创建新的${resource}记录`,
  }),
  
  GetList: (resource: string) => ({
    summary: `获取${resource}列表`,
    description: `分页获取${resource}列表，支持筛选和排序`,
  }),
  
  GetById: (resource: string) => ({
    summary: `获取${resource}详情`,
    description: `根据ID获取${resource}的详细信息`,
  }),
  
  Update: (resource: string) => ({
    summary: `更新${resource}`,
    description: `更新指定${resource}的信息`,
  }),
  
  Delete: (resource: string) => ({
    summary: `删除${resource}`,
    description: `删除指定的${resource}记录`,
  }),

  // 认证操作
  Login: () => ({
    summary: '用户登录',
    description: '使用用户名/手机号和密码进行登录认证',
  }),
  
  Logout: () => ({
    summary: '用户登出',
    description: '退出当前登录状态',
  }),
  
  RefreshToken: () => ({
    summary: '刷新令牌',
    description: '使用刷新令牌获取新的访问令牌',
  }),

  // 多租户操作
  SwitchTenant: () => ({
    summary: '切换租户',
    description: '切换到指定的租户上下文',
  }),
  
  GetTenantInfo: () => ({
    summary: '获取租户信息',
    description: '获取当前租户的详细信息',
  }),
};

import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

/**
 * 数据库配置工厂
 */
export class DatabaseConfigFactory {
  private static readonly logger = new Logger(DatabaseConfigFactory.name);

  /**
   * 创建TypeORM配置
   */
  static createTypeOrmOptions(
    configService: ConfigService,
  ): TypeOrmModuleOptions {
    const config: TypeOrmModuleOptions = {
      type: 'mysql',
      host: configService.get<string>('DB_HOST', 'localhost'),
      port: configService.get<number>('DB_PORT', 3306),
      username: configService.get<string>('DB_USERNAME', 'root'),
      password: configService.get<string>('DB_PASSWORD', ''),
      database: configService.get<string>('DB_DATABASE', 'smart_campus'),
      charset: 'utf8mb4',
      timezone: '+08:00',

      // 实体配置 - 使用autoLoadEntities避免路径问题
      entities: [],
      autoLoadEntities: true,

      // 同步配置
      synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
      migrationsRun: configService.get<boolean>('DB_MIGRATIONS_RUN', true),
      migrations: ['dist/migrations/*{.ts,.js}'],
      migrationsTableName: 'migrations',

      // 连接池配置 - 修复MySQL2兼容性问题
      extra: {
        connectionLimit: configService.get<number>('DB_CONNECTION_LIMIT', 10),
        // 移除MySQL2不支持的选项
        // acquireTimeout, timeout, reconnect 已被移除
        charset: 'utf8mb4_unicode_ci',
      },

      // 日志配置
      logging: DatabaseConfigFactory.getLoggingConfig(configService),
      logger: 'advanced-console',

      // 缓存配置
      cache: DatabaseConfigFactory.getCacheConfig(configService),

      // 其他配置
      dropSchema: false,
      retryAttempts: configService.get<number>('DB_RETRY_ATTEMPTS', 3),
      retryDelay: configService.get<number>('DB_RETRY_DELAY', 3000),
    };

    this.logger.log('Database configuration created', {
      host: (config as any).host,
      port: (config as any).port,
      database: (config as any).database,
      synchronize: (config as any).synchronize,
      migrationsRun: (config as any).migrationsRun,
    });

    return config;
  }

  /**
   * 获取日志配置
   */
  private static getLoggingConfig(
    configService: ConfigService,
  ):
    | boolean
    | ('query' | 'error' | 'schema' | 'warn' | 'info' | 'log' | 'migration')[] {
    const env = configService.get<string>('NODE_ENV', 'development');
    const logLevel = configService.get<string>('DB_LOG_LEVEL', 'error');

    if (env === 'production') {
      return ['error', 'warn', 'migration'];
    }

    if (env === 'test') {
      return false;
    }

    // 开发环境
    switch (logLevel) {
      case 'all':
        return ['query', 'error', 'schema', 'warn', 'info', 'log', 'migration'];
      case 'query':
        return ['query', 'error', 'warn', 'migration'];
      case 'error':
        return ['error', 'warn', 'migration'];
      default:
        return ['error', 'warn', 'migration'];
    }
  }

  /**
   * 获取缓存配置
   */
  private static getCacheConfig(
    configService: ConfigService,
  ): boolean | { type: 'redis'; options: any } {
    const cacheEnabled = configService.get<boolean>('DB_CACHE_ENABLED', false);

    if (!cacheEnabled) {
      return false;
    }

    const redisHost = configService.get<string>('REDIS_HOST', 'localhost');
    const redisPort = configService.get<number>('REDIS_PORT', 6379);
    const redisPassword = configService.get<string>('REDIS_PASSWORD');
    const redisDb = configService.get<number>('REDIS_DB', 1);

    return {
      type: 'redis',
      options: {
        host: redisHost,
        port: redisPort,
        password: redisPassword,
        db: redisDb,
        duration: configService.get<number>('DB_CACHE_DURATION', 30000), // 30秒
      },
    };
  }

  /**
   * 验证数据库配置
   */
  static validateConfig(configService: ConfigService): void {
    const requiredVars = ['DB_HOST', 'DB_USERNAME', 'DB_DATABASE'];
    const missingVars = requiredVars.filter(
      (varName) => !configService.get(varName),
    );

    if (missingVars.length > 0) {
      throw new Error(
        `Missing required database environment variables: ${missingVars.join(', ')}`,
      );
    }

    this.logger.log('Database configuration validated successfully');
  }
}

/**
 * 数据库健康检查
 */
export class DatabaseHealthCheck {
  private static readonly logger = new Logger(DatabaseHealthCheck.name);

  /**
   * 检查数据库连接健康状态
   */
  static async checkHealth(connection: any): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      activeConnections: number;
      totalConnections: number;
      lastError?: string;
    };
  }> {
    const startTime = Date.now();

    try {
      // 执行简单查询测试连接
      await connection.query('SELECT 1');
      const responseTime = Date.now() - startTime;

      // 获取连接池信息
      const poolInfo = connection.driver.pool;

      return {
        status: 'healthy',
        details: {
          connected: connection.isConnected,
          responseTime,
          activeConnections: poolInfo?.acquiredConnections || 0,
          totalConnections: poolInfo?.totalConnections || 0,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      this.logger.error('Database health check failed', {
        error: error.message,
        responseTime,
      });

      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime,
          activeConnections: 0,
          totalConnections: 0,
          lastError: error.message,
        },
      };
    }
  }
}

/**
 * 数据库性能监控
 */
export class DatabasePerformanceMonitor {
  private static readonly logger = new Logger(DatabasePerformanceMonitor.name);
  private static slowQueryThreshold = 1000; // 1秒

  /**
   * 监控慢查询
   */
  static monitorSlowQueries(connection: any): void {
    if (connection.logger) {
      const originalLogQuery = connection.logger.logQuery;

      connection.logger.logQuery = function (
        query: string,
        parameters?: any[],
        queryRunner?: any,
      ) {
        const startTime = Date.now();

        // 调用原始日志方法
        const result = originalLogQuery.call(
          this,
          query,
          parameters,
          queryRunner,
        );

        const executionTime = Date.now() - startTime;

        // 记录慢查询
        if (executionTime > DatabasePerformanceMonitor.slowQueryThreshold) {
          DatabasePerformanceMonitor.logger.warn('Slow query detected', {
            query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
            executionTime: `${executionTime}ms`,
            parameters: parameters?.length || 0,
          });
        }

        return result;
      };
    }
  }

  /**
   * 设置慢查询阈值
   */
  static setSlowQueryThreshold(threshold: number): void {
    this.slowQueryThreshold = threshold;
    this.logger.log(`Slow query threshold set to ${threshold}ms`);
  }
}

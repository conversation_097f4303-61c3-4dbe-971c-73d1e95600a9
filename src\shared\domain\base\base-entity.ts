import {
  PrimaryColumn,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  VersionColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { v7 as uuidv7 } from 'uuid';
import { TenantContext } from '../context/tenant.context';

/**
 * 基础实体类
 * 所有实体都应该继承此类
 */
export abstract class BaseEntity {
  @PrimaryColumn('varchar', { length: 36, comment: '主键ID' })
  id: string;

  @CreateDateColumn({
    name: 'created_at',
    comment: '创建时间',
    type: 'datetime',
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: 'updated_at',
    comment: '更新时间',
    type: 'datetime',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: 'deleted_at',
    comment: '删除时间',
    type: 'datetime',
    nullable: true,
  })
  deletedAt?: Date;

  @Column({
    name: 'created_by',
    comment: '创建者ID',
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  createdBy?: string;

  @Column({
    name: 'updated_by',
    comment: '更新者ID',
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  updatedBy?: string;

  @Column({
    name: 'deleted_by',
    comment: '删除者ID',
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  deletedBy?: string;

  @Column({
    name: 'is_deleted',
    comment: '是否已删除',
    type: 'boolean',
    default: false,
  })
  isDeleted: boolean;

  @Column({
    name: 'tenant_id',
    comment: '租户ID',
    type: 'varchar',
    length: 36,
    nullable: false,
  })
  tenantId: string;

  @VersionColumn({
    name: 'revision',
    comment: '乐观锁版本号',
  })
  revision: number;

  constructor() {
    if (!this.id) {
      this.id = uuidv7();
    }
    this.isDeleted = false;
    // 租户ID将在BeforeInsert中设置
  }

  /**
   * 在插入前自动设置租户ID
   */
  @BeforeInsert()
  setTenantOnInsert(): void {
    if (!this.tenantId) {
      this.tenantId = this.getCurrentTenantId();
    }
  }

  /**
   * 在更新前验证租户ID
   */
  @BeforeUpdate()
  validateTenantOnUpdate(): void {
    const currentTenantId = this.getCurrentTenantId();
    if (this.tenantId !== currentTenantId) {
      throw new Error(`Tenant mismatch: entity belongs to ${this.tenantId}, current tenant is ${currentTenantId}`);
    }
  }

  /**
   * 获取当前租户ID（子类可以重写此方法）
   */
  protected getCurrentTenantId(): string {
    return TenantContext.getCurrentTenantId() || TenantContext.getDefaultTenantId();
  }

  /**
   * 软删除
   */
  softDelete(deletedBy?: string): void {
    this.isDeleted = true;
    this.deletedAt = new Date();
    if (deletedBy) {
      this.deletedBy = deletedBy;
    }
  }

  /**
   * 恢复删除
   */
  restore(): void {
    this.isDeleted = false;
    this.deletedAt = undefined;
    this.deletedBy = undefined;
  }

  /**
   * 设置创建者
   */
  setCreatedBy(userId: string): void {
    this.createdBy = userId;
  }

  /**
   * 设置更新者
   */
  setUpdatedBy(userId: string): void {
    this.updatedBy = userId;
  }

  /**
   * 获取创建者ID
   */
  getCreatedBy(): string | undefined {
    return this.createdBy;
  }

  /**
   * 获取更新者ID
   */
  getUpdatedBy(): string | undefined {
    return this.updatedBy;
  }

  /**
   * 获取删除者ID
   */
  getDeletedBy(): string | undefined {
    return this.deletedBy;
  }

  /**
   * 设置租户ID
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * 获取租户ID
   */
  getTenantId(): string {
    return this.tenantId;
  }

  /**
   * 获取当前版本号
   */
  getRevision(): number {
    return this.revision;
  }

  /**
   * 检查是否属于指定租户
   */
  belongsToTenant(tenantId: string): boolean {
    return this.tenantId === tenantId;
  }

  /**
   * 验证租户访问权限
   */
  validateTenantAccess(requiredTenantId: string): void {
    if (!this.belongsToTenant(requiredTenantId)) {
      throw new Error(`Access denied: entity belongs to tenant ${this.tenantId}, required ${requiredTenantId}`);
    }
  }

  /**
   * 检查是否相等
   */
  equals(other: BaseEntity): boolean {
    if (!other || other.constructor !== this.constructor) {
      return false;
    }
    return this.id === other.id;
  }

  /**
   * 检查是否相等（包含版本号）
   */
  equalsWithVersion(other: BaseEntity): boolean {
    return this.equals(other) && this.revision === other.revision;
  }
}

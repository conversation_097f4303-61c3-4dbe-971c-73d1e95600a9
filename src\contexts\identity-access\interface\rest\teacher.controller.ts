import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { TeacherApplicationService } from '../../application/services/teacher-application.service';
import { TeacherUser } from '../../domain/aggregates/user.aggregate';
import { ResponseInterceptor } from '@/shared/infrastructure/interceptors/response.interceptor';
import { GlobalExceptionFilter } from '@/shared/infrastructure/filters/global-exception.filter';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';
import {
  JwtAuthGuard,
  RequestUser,
} from '../../infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '../../infrastructure/guards/permissions.guard';
import { AuditInterceptor } from '@/shared/infrastructure/interceptors/audit.interceptor';

/**
 * 教师管理控制器
 */
@ApiTags('教师管理')
@Controller('teachers')
@UseGuards(JwtAuthGuard)
@UseInterceptors(ResponseInterceptor, AuditInterceptor)
@ApiBearerAuth()
export class TeacherController {
  constructor(
    private readonly teacherApplicationService: TeacherApplicationService,
  ) {}

  /**
   * 创建教师
   */
  @Post()
  @ApiOperation({
    summary: '创建教师',
    description: '创建新的教师用户',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '教师创建成功',
    type: TeacherUser,
  })
  async createTeacher(
    @Body()
    createTeacherDto: {
      name: string;
      phone: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      carNo?: string;
    },
    @CurrentUser() currentUser: RequestUser,
  ): Promise<TeacherUser> {
    return await this.teacherApplicationService.createTeacher(
      createTeacherDto,
      currentUser.id,
    );
  }

  /**
   * 获取教师列表
   */
  @Get()
  @ApiOperation({
    summary: '获取教师列表',
    description: '分页获取教师列表，支持筛选和排序',
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: '每页数量',
    example: 10,
  })
  @ApiQuery({ name: 'name', required: false, description: '教师姓名' })
  @ApiQuery({ name: 'phone', required: false, description: '手机号' })
  @ApiQuery({ name: 'isActive', required: false, description: '是否激活' })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: '排序字段',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: '排序方向',
    enum: ['ASC', 'DESC'],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取教师列表成功',
    type: PaginatedResponseDto<TeacherUser>,
  })
  async getTeachers(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('name') name?: string,
    @Query('phone') phone?: string,
    @Query('isActive') isActive?: boolean,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResponseDto<TeacherUser>> {
    return await this.teacherApplicationService.getTeachers({
      page,
      pageSize,
      name,
      phone,
      isActive,
      sortBy,
      sortOrder,
    });
  }

  /**
   * 搜索教师
   */
  @Get('search')
  @ApiOperation({
    summary: '搜索教师',
    description: '根据关键词搜索教师',
  })
  @ApiQuery({ name: 'keyword', required: true, description: '搜索关键词' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: '返回数量限制',
    example: 20,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索教师成功',
    type: [TeacherUser],
  })
  async searchTeachers(
    @Query('keyword') keyword: string,
    @Query('limit') limit?: number,
  ): Promise<TeacherUser[]> {
    return await this.teacherApplicationService.searchTeachers(keyword, limit);
  }

  /**
   * 获取教师统计信息
   */
  @Get('statistics')
  @ApiOperation({
    summary: '获取教师统计信息',
    description: '获取教师相关的统计数据',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取统计信息成功',
  })
  async getTeacherStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    recentRegistrations: number;
  }> {
    return await this.teacherApplicationService.getTeacherStatistics();
  }

  /**
   * 获取教师详情
   */
  @Get(':id')
  @ApiOperation({
    summary: '获取教师详情',
    description: '根据ID获取教师详细信息',
  })
  @ApiParam({ name: 'id', description: '教师ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取教师详情成功',
    type: TeacherUser,
  })
  async getTeacher(@Param('id') id: string): Promise<TeacherUser> {
    return await this.teacherApplicationService.getTeacher(id);
  }

  /**
   * 更新教师信息
   */
  @Put(':id')
  @ApiOperation({
    summary: '更新教师信息',
    description: '更新指定教师的信息',
  })
  @ApiParam({ name: 'id', description: '教师ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '教师信息更新成功',
    type: TeacherUser,
  })
  async updateTeacher(
    @Param('id') id: string,
    @Body()
    updateTeacherDto: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      carNo?: string;
    },
    @CurrentUser() currentUser: RequestUser,
  ): Promise<TeacherUser> {
    return await this.teacherApplicationService.updateTeacher(
      id,
      updateTeacherDto,
      currentUser.id,
    );
  }

  /**
   * 切换教师状态
   */
  @Put(':id/toggle-status')
  @ApiOperation({
    summary: '切换教师状态',
    description: '激活或停用教师账户',
  })
  @ApiParam({ name: 'id', description: '教师ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '教师状态切换成功',
    type: TeacherUser,
  })
  async toggleTeacherStatus(@Param('id') id: string): Promise<TeacherUser> {
    return await this.teacherApplicationService.toggleTeacherStatus(id);
  }

  /**
   * 删除教师
   */
  @Delete(':id')
  @ApiOperation({
    summary: '删除教师',
    description: '删除指定的教师',
  })
  @ApiParam({ name: 'id', description: '教师ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '教师删除成功',
  })
  async deleteTeacher(@Param('id') id: string): Promise<void> {
    return await this.teacherApplicationService.deleteTeacher(id);
  }

  /**
   * 批量删除教师
   */
  @Delete('batch')
  @ApiOperation({
    summary: '批量删除教师',
    description: '批量删除多个教师',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量删除教师成功',
  })
  async batchDeleteTeachers(
    @Body() deleteDto: { teacherIds: string[] },
  ): Promise<void> {
    return await this.teacherApplicationService.batchDeleteTeachers(
      deleteDto.teacherIds,
    );
  }
}

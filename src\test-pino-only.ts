import { NestFactory } from '@nestjs/core';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggingModule } from './shared/infrastructure/logging/logging.module';
import { PinoLoggerService } from './shared/infrastructure/logging/pino-logger.service';

/**
 * 仅测试 Pino 日志系统的最小应用模块
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    LoggingModule,
  ],
})
class TestPinoModule {}

/**
 * 测试 Pino 日志系统的启动函数
 */
async function testPinoBootstrap() {
  console.log('🚀 启动 Pino 日志系统测试应用...\n');

  try {
    // 创建 NestJS 应用
    const app = await NestFactory.create(TestPinoModule, {
      logger: false, // 禁用默认日志，使用我们的 Pino 日志
    });

    // 获取 Pino 日志服务
    const logger = app.get(PinoLoggerService);
    logger.setContext('TestPinoApp');

    // 设置为应用的日志服务
    app.useLogger(logger);

    console.log('✅ NestJS 应用创建成功');
    console.log('✅ Pino 日志服务注入成功');

    // 测试日志功能
    logger.log('🎉 Pino 日志系统在 NestJS 中运行成功！');
    logger.log('📝 这是通过 NestJS 依赖注入的 Pino 日志服务');
    
    logger.debug('调试信息：应用启动完成', {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      logLevel: process.env.LOG_LEVEL || 'info',
    }, 'TestPinoApp');

    logger.logBusinessOperation(
      'app_startup',
      'system',
      'test_tenant',
      {
        startupTime: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
      }
    );

    // 启动应用（监听端口）
    const port = 3001; // 使用不同的端口避免冲突
    await app.listen(port);

    logger.log(`🚀 测试应用已启动，监听端口: ${port}`);
    logger.log('📁 请检查 logs/ 目录下的日志文件');
    logger.log('🌐 访问 http://localhost:3001 测试应用');

    console.log(`\n🎉 Pino 日志系统测试应用启动成功！`);
    console.log(`🌐 应用地址: http://localhost:${port}`);
    console.log(`📁 日志文件: logs/`);
    console.log(`⏹️  按 Ctrl+C 停止应用\n`);

  } catch (error) {
    console.error('❌ Pino 日志系统测试失败:', error);
    process.exit(1);
  }
}

// 启动测试应用
testPinoBootstrap();

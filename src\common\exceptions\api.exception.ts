import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * API 异常基类
 */
export class ApiException extends HttpException {
  constructor(
    message: string,
    status: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    code?: string,
  ) {
    super(
      {
        message,
        code,
        timestamp: new Date().toISOString(),
      },
      status,
    );
  }

  /**
   * 创建业务异常
   */
  static business(message: string, code?: string): ApiException {
    return new ApiException(message, HttpStatus.BAD_REQUEST, code);
  }

  /**
   * 创建错误异常
   */
  static Error(message: string, code?: string): ApiException {
    return new ApiException(message, HttpStatus.BAD_REQUEST, code);
  }

  /**
   * 创建未找到异常
   */
  static notFound(message: string = '资源未找到'): ApiException {
    return new ApiException(message, HttpStatus.NOT_FOUND, 'NOT_FOUND');
  }

  /**
   * 创建未授权异常
   */
  static unauthorized(message: string = '未授权访问'): ApiException {
    return new ApiException(message, HttpStatus.UNAUTHORIZED, 'UNAUTHORIZED');
  }

  /**
   * 创建禁止访问异常
   */
  static forbidden(message: string = '禁止访问'): ApiException {
    return new ApiException(message, HttpStatus.FORBIDDEN, 'FORBIDDEN');
  }

  /**
   * 创建参数验证异常
   */
  static validation(message: string = '参数验证失败'): ApiException {
    return new ApiException(message, HttpStatus.BAD_REQUEST, 'VALIDATION_ERROR');
  }
}

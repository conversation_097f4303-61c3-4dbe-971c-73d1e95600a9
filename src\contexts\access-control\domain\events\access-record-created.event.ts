import { DomainEvent } from '@/shared/domain/events/domain-event';
import { AccessType } from '../aggregates/access-record.aggregate';
import { AccessLocation } from '../value-objects/access-location';

/**
 * 门禁记录创建事件
 */
export class AccessRecordCreatedEvent extends DomainEvent {
  constructor(
    public readonly recordId: string,
    public readonly userId: string,
    public readonly accessType: AccessType,
    public readonly accessTime: Date,
    public readonly location: AccessLocation,
  ) {
    super(recordId);
  }

  getEventName(): string {
    return 'access-record.created';
  }

  getEventData(): Record<string, any> {
    return {
      recordId: this.recordId,
      userId: this.userId,
      accessType: this.accessType,
      accessTime: this.accessTime.toISOString(),
      location: {
        name: this.location.name,
        description: this.location.description,
        building: this.location.building,
        floor: this.location.floor,
        fullDescription: this.location.getFullDescription(),
      },
    };
  }
}

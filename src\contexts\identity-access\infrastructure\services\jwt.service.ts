import { Injectable, Inject } from '@nestjs/common';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { IJwtService } from '../../domain/services/jwt.service.interface';
import { JwtToken, JwtPayload } from '../../domain/value-objects/jwt-token';
import { BusinessRuleException } from '@/shared/application/exceptions/domain-exception';
import * as crypto from 'crypto';

/**
 * JWT服务实现
 */
@Injectable()
export class JwtService implements IJwtService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly accessTokenExpiresIn: string;
  private readonly refreshTokenExpiresIn: string;

  constructor(
    private readonly nestJwtService: NestJwtService,
    private readonly configService: ConfigService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {
    this.accessTokenSecret = this.configService.get<string>('JWT_ACCESS_SECRET') || 'access-secret';
    this.refreshTokenSecret = this.configService.get<string>('JWT_REFRESH_SECRET') || 'refresh-secret';
    this.accessTokenExpiresIn = this.configService.get<string>('JWT_ACCESS_EXPIRES_IN') || '24h';
    this.refreshTokenExpiresIn = this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d';
  }

  /**
   * 生成访问令牌
   */
  async generateAccessToken(payload: JwtPayload): Promise<string> {
    return this.nestJwtService.signAsync(
      {
        sub: payload.userId,
        username: payload.username,
        userType: payload.userType,
        roles: payload.roles,
        permissions: payload.permissions,
      },
      {
        secret: this.accessTokenSecret,
        expiresIn: this.accessTokenExpiresIn,
      }
    );
  }

  /**
   * 生成刷新令牌
   */
  async generateRefreshToken(userId: string): Promise<string> {
    const jti = crypto.randomUUID(); // JWT ID，用于令牌撤销

    return this.nestJwtService.signAsync(
      {
        sub: userId,
        jti,
        type: 'refresh',
      },
      {
        secret: this.refreshTokenSecret,
        expiresIn: this.refreshTokenExpiresIn,
      }
    );
  }

  /**
   * 生成完整的JWT令牌对象
   */
  async generateTokens(payload: JwtPayload): Promise<JwtToken> {
    const [accessToken, refreshToken] = await Promise.all([
      this.generateAccessToken(payload),
      this.generateRefreshToken(payload.userId),
    ]);

    // 计算过期时间（秒）
    const expiresIn = this.parseExpiresIn(this.accessTokenExpiresIn);

    return new JwtToken({
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: 'Bearer',
    });
  }

  /**
   * 验证访问令牌
   */
  async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      // 检查令牌是否被撤销
      if (await this.isTokenRevoked(token)) {
        throw new BusinessRuleException('令牌已被撤销');
      }

      const decoded = await this.nestJwtService.verifyAsync(token, {
        secret: this.accessTokenSecret,
      });

      return new JwtPayload({
        sub: decoded.sub,
        username: decoded.username,
        userType: decoded.userType,
        tenantId: decoded.tenantId || 'system',
        roles: decoded.roles || [],
        permissions: decoded.permissions || [],
        iat: decoded.iat,
        exp: decoded.exp,
      });
    } catch (error) {
      throw new BusinessRuleException('无效的访问令牌');
    }
  }

  /**
   * 验证刷新令牌
   */
  async verifyRefreshToken(token: string): Promise<{ userId: string; iat: number; exp: number }> {
    try {
      // 检查令牌是否被撤销
      if (await this.isTokenRevoked(token)) {
        throw new BusinessRuleException('刷新令牌已被撤销');
      }

      const decoded = await this.nestJwtService.verifyAsync(token, {
        secret: this.refreshTokenSecret,
      });

      if (decoded.type !== 'refresh') {
        throw new BusinessRuleException('无效的刷新令牌类型');
      }

      return {
        userId: decoded.sub,
        iat: decoded.iat,
        exp: decoded.exp,
      };
    } catch (error) {
      throw new BusinessRuleException('无效的刷新令牌');
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken: string): Promise<JwtToken> {
    // 验证刷新令牌
    const { userId } = await this.verifyRefreshToken(refreshToken);

    // 这里需要重新获取用户信息来生成新的载荷
    // 由于这是基础设施层，我们需要通过应用服务来处理
    throw new Error('此方法应该在应用服务层实现');
  }

  /**
   * 撤销令牌（加入黑名单）
   */
  async revokeToken(token: string): Promise<void> {
    try {
      const decoded = this.nestJwtService.decode(token) as any;
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          await this.cacheManager.set(`revoked_token:${token}`, true, ttl * 1000);
        }
      }
    } catch (error) {
      // 忽略解码错误，令牌可能已经无效
    }
  }

  /**
   * 检查令牌是否被撤销
   */
  async isTokenRevoked(token: string): Promise<boolean> {
    const revoked = await this.cacheManager.get(`revoked_token:${token}`);
    return !!revoked;
  }

  /**
   * 从请求头中提取令牌
   */
  extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * 获取令牌剩余有效时间（秒）
   */
  async getTokenRemainingTime(token: string): Promise<number> {
    try {
      const decoded = this.nestJwtService.decode(token) as any;
      if (decoded && decoded.exp) {
        const now = Math.floor(Date.now() / 1000);
        return Math.max(0, decoded.exp - now);
      }
      return 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 解析过期时间字符串为秒数
   */
  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 3600; // 默认1小时
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'h':
        return value * 60 * 60;
      case 'd':
        return value * 24 * 60 * 60;
      default:
        return 3600;
    }
  }
}

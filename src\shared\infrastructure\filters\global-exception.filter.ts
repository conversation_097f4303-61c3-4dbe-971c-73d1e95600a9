import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError } from 'typeorm';
import {
  DomainException,
  ValidationException,
  BusinessRuleException,
  EntityNotFoundException
} from '@/shared/application/exceptions/domain-exception';
import { ApiResponse } from '@/shared/application/dto/base-response.dto';

/**
 * 全局异常过滤器
 * 统一处理所有异常并返回标准格式的错误响应
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const errorResponse = this.buildErrorResponse(exception, request);

    // 记录错误日志
    this.logError(exception, request, errorResponse);

    response.status(errorResponse.statusCode).json(errorResponse.body);
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(exception: unknown, request: Request): {
    statusCode: number;
    body: ApiResponse<null>;
  } {
    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';
    let errors: string[] = [];
    let errorCode = 'INTERNAL_SERVER_ERROR';

    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const response = exception.getResponse();

      if (typeof response === 'string') {
        message = response;
      } else if (typeof response === 'object' && response !== null) {
        const responseObj = response as any;
        message = responseObj.message || responseObj.error || message;
        errors = Array.isArray(responseObj.message) ? responseObj.message : [];
        errorCode = responseObj.error || 'HTTP_EXCEPTION';
      }
    } else if (exception instanceof ValidationException) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = '数据验证失败';
      errors = this.flattenValidationErrors(exception.errors);
      errorCode = 'VALIDATION_ERROR';
    } else if (exception instanceof BusinessRuleException) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = exception.message;
      errorCode = 'BUSINESS_RULE_VIOLATION';
    } else if (exception instanceof EntityNotFoundException) {
      statusCode = HttpStatus.NOT_FOUND;
      message = exception.message;
      errorCode = 'ENTITY_NOT_FOUND';
    } else if (exception instanceof DomainException) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = exception.message;
      errorCode = 'DOMAIN_ERROR';
    } else if (exception instanceof QueryFailedError) {
      statusCode = HttpStatus.BAD_REQUEST;
      message = this.handleDatabaseError(exception);
      errorCode = 'DATABASE_ERROR';
    } else if (exception instanceof Error) {
      message = exception.message;
      errorCode = 'APPLICATION_ERROR';
    }

    return {
      statusCode,
      body: ApiResponse.error(message, statusCode, request.url),
    };
  }

  /**
   * 处理数据库错误
   */
  private handleDatabaseError(error: QueryFailedError): string {
    const message = error.message;

    // MySQL错误码处理
    if ((error as any).driverError?.code) {
      switch ((error as any).driverError.code) {
        case 'ER_DUP_ENTRY':
          return '数据已存在，请检查唯一性约束';
        case 'ER_NO_REFERENCED_ROW_2':
          return '关联的数据不存在';
        case 'ER_ROW_IS_REFERENCED_2':
          return '数据被其他记录引用，无法删除';
        case 'ER_DATA_TOO_LONG':
          return '数据长度超出限制';
        case 'ER_BAD_NULL_ERROR':
          return '必填字段不能为空';
        case 'ER_PARSE_ERROR':
          return 'SQL语法错误';
        case 'ER_ACCESS_DENIED_ERROR':
          return '数据库访问权限不足';
        case 'ER_LOCK_WAIT_TIMEOUT':
          return '数据库锁等待超时';
        case 'ER_LOCK_DEADLOCK':
          return '数据库死锁';
        default:
          return '数据库操作失败';
      }
    }

    // 通用数据库错误处理
    if (message.includes('duplicate key')) {
      return '数据已存在';
    }
    if (message.includes('foreign key constraint')) {
      return '外键约束违反';
    }
    if (message.includes('not null constraint')) {
      return '必填字段不能为空';
    }
    if (message.includes('check constraint')) {
      return '数据格式不符合要求';
    }

    return '数据库操作失败';
  }

  /**
   * 扁平化验证错误
   */
  private flattenValidationErrors(errors: Record<string, string[]>): string[] {
    const result: string[] = [];

    Object.entries(errors).forEach(([field, messages]) => {
      messages.forEach(message => {
        result.push(`${field}: ${message}`);
      });
    });

    return result;
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    errorResponse: { statusCode: number; body: ApiResponse<null> }
  ): void {
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).user?.id || 'anonymous';

    const logData = {
      method,
      url,
      ip,
      userId,
      userAgent: userAgent.substring(0, 100),
      statusCode: errorResponse.statusCode,
      errorCode: (errorResponse.body as any).errorCode || 'UNKNOWN',
      message: errorResponse.body.message,
      requestId: (errorResponse.body as any).metadata?.requestId,
    };

    if (errorResponse.statusCode >= 500) {
      // 服务器错误，记录完整堆栈
      this.logger.error('Server error occurred', {
        ...logData,
        stack: exception instanceof Error ? exception.stack : undefined,
        exception: exception instanceof Error ? exception.constructor.name : typeof exception,
      });
    } else if (errorResponse.statusCode >= 400) {
      // 客户端错误，记录基本信息
      this.logger.warn('Client error occurred', logData);
    } else {
      // 其他情况
      this.logger.log('Exception handled', logData);
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * HTTP异常过滤器
 * 专门处理HTTP异常
 */
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    const errorResponse = exception.getResponse();
    let message = exception.message;
    let errors: string[] = [];

    if (typeof errorResponse === 'object' && errorResponse !== null) {
      const responseObj = errorResponse as any;
      message = responseObj.message || message;
      errors = Array.isArray(responseObj.message) ? responseObj.message : [];
    }

    const apiResponse = ApiResponse.error(message, status, request.url);

    this.logger.warn(`HTTP Exception: ${status} ${message}`, {
      method: request.method,
      url: request.url,
      statusCode: status,
      message,
    });

    response.status(status).json(apiResponse);
  }
}

/**
 * 验证异常过滤器
 * 专门处理数据验证异常
 */
@Catch(ValidationException)
export class ValidationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(ValidationExceptionFilter.name);

  catch(exception: ValidationException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const errors = this.formatValidationErrors(exception.errors);

    const apiResponse = ApiResponse.error('数据验证失败', HttpStatus.BAD_REQUEST, request.url);

    this.logger.warn('Validation error occurred', {
      method: request.method,
      url: request.url,
      errors: exception.errors,
    });

    response.status(HttpStatus.BAD_REQUEST).json(apiResponse);
  }

  /**
   * 格式化验证错误
   */
  private formatValidationErrors(errors: Record<string, string[]>): string[] {
    const result: string[] = [];

    Object.entries(errors).forEach(([field, messages]) => {
      messages.forEach(message => {
        result.push(`${field}: ${message}`);
      });
    });

    return result;
  }
}

/**
 * 业务规则异常过滤器
 * 专门处理业务规则违反异常
 */
@Catch(BusinessRuleException)
export class BusinessRuleExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(BusinessRuleExceptionFilter.name);

  catch(exception: BusinessRuleException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const apiResponse = ApiResponse.error(exception.message, HttpStatus.BAD_REQUEST, request.url);

    this.logger.warn('Business rule violation', {
      method: request.method,
      url: request.url,
      rule: (exception as any).rule || 'UNKNOWN',
      message: exception.message,
    });

    response.status(HttpStatus.BAD_REQUEST).json(apiResponse);
  }
}

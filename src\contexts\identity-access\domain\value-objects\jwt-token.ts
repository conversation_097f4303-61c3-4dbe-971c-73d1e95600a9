/**
 * 基础值对象类
 */
abstract class ValueObject<T> {
  protected readonly props: T;

  constructor(props: T) {
    this.props = props;
  }

  equals(other: ValueObject<T>): boolean {
    return JSON.stringify(this.props) === JSON.stringify(other.props);
  }
}

/**
 * JWT令牌值对象
 */
export interface JwtTokenProps {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export class JwtToken extends ValueObject<JwtTokenProps> {
  constructor(props: JwtTokenProps) {
    super(props);
  }

  get accessToken(): string {
    return this.props.accessToken;
  }

  get refreshToken(): string {
    return this.props.refreshToken;
  }

  get expiresIn(): number {
    return this.props.expiresIn;
  }

  get tokenType(): string {
    return this.props.tokenType;
  }

  /**
   * 检查访问令牌是否即将过期（剩余时间少于5分钟）
   */
  isAccessTokenExpiringSoon(): boolean {
    const fiveMinutesInSeconds = 5 * 60;
    return this.props.expiresIn < fiveMinutesInSeconds;
  }

  /**
   * 创建Bearer格式的授权头
   */
  toBearerToken(): string {
    return `${this.props.tokenType} ${this.props.accessToken}`;
  }

  /**
   * 转换为响应DTO格式
   */
  toResponseDto(): {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  } {
    return {
      access_token: this.props.accessToken,
      refresh_token: this.props.refreshToken,
      expires_in: this.props.expiresIn,
      token_type: this.props.tokenType,
    };
  }
}

/**
 * JWT载荷值对象
 */
export interface JwtPayloadProps {
  sub: string; // 用户ID
  username: string; // 用户名
  userType: string; // 用户类型
  tenantId: string; // 租户ID
  roles: string[]; // 用户角色
  permissions: string[]; // 用户权限
  iat: number; // 签发时间
  exp: number; // 过期时间
}

export class JwtPayload extends ValueObject<JwtPayloadProps> {
  constructor(props: JwtPayloadProps) {
    super(props);
  }

  get userId(): string {
    return this.props.sub;
  }

  get username(): string {
    return this.props.username;
  }

  get userType(): string {
    return this.props.userType;
  }

  get tenantId(): string {
    return this.props.tenantId;
  }

  get roles(): string[] {
    return this.props.roles;
  }

  get permissions(): string[] {
    return this.props.permissions;
  }

  get issuedAt(): number {
    return this.props.iat;
  }

  get expiresAt(): number {
    return this.props.exp;
  }

  /**
   * 检查令牌是否已过期
   */
  isExpired(): boolean {
    const now = Math.floor(Date.now() / 1000);
    return now >= this.props.exp;
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(role: string): boolean {
    return this.props.roles.includes(role);
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(permission: string): boolean {
    return this.props.permissions.includes(permission);
  }

  /**
   * 检查用户是否有任意一个指定权限
   */
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.props.permissions.includes(permission));
  }

  /**
   * 检查用户是否有所有指定权限
   */
  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.props.permissions.includes(permission));
  }
}

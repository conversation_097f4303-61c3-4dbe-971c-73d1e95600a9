import { Injectable, Inject } from '@nestjs/common';
import { User } from '../aggregates/user.aggregate';
import { JwtToken, JwtPayload } from '../value-objects/jwt-token';
import { UserRepository } from '../repositories/user.repository';
import { USER_REPOSITORY } from '../tokens';
import {
  BusinessRuleException,
  EntityNotFoundException,
} from '@/shared/application/exceptions/domain-exception';
import { LoginRestrictionService } from '../../infrastructure/services/login-restriction.service';
import * as bcrypt from 'bcrypt';

/**
 * 认证领域服务
 */
@Injectable()
export class AuthDomainService {
  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly loginRestrictionService: LoginRestrictionService,
  ) {}

  /**
   * 验证用户凭据
   */
  async validateUserCredentials(
    phone: string,
    password: string,
  ): Promise<User> {
    // 根据手机号查找用户
    const user = await this.userRepository.findByPhone(phone);
    if (!user) {
      throw new EntityNotFoundException('User', phone);
    }

    // 检查用户是否激活
    if (!user.isUserActive()) {
      throw new BusinessRuleException('用户账户已被停用');
    }

    // 验证密码
    const isPasswordValid = await this.validatePassword(
      password,
      user.getPasswordHash(),
    );
    if (!isPasswordValid) {
      throw new BusinessRuleException('用户名或密码错误');
    }

    return user;
  }

  /**
   * 验证密码
   */
  private async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    try {
      return await bcrypt.compare(plainPassword, hashedPassword);
    } catch (error) {
      return false;
    }
  }

  /**
   * 哈希密码
   */
  async hashUserPassword(plainPassword: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(plainPassword, saltRounds);
  }

  /**
   * 生成用户权限列表
   */
  generateUserPermissions(user: User): string[] {
    const permissions: string[] = [];

    // 基础权限
    permissions.push('user:read');

    // 根据用户类型添加权限
    switch (user.getUserTypeString()) {
      case 'student':
        permissions.push(
          'student:read',
          'student:update-self',
          'leave:create',
          'leave:read-self',
        );
        break;

      case 'teacher':
        permissions.push(
          'teacher:read',
          'teacher:update-self',
          'student:read',
          'leave:read',
          'leave:approve',
          'access:manage',
        );
        break;

      case 'parent':
        permissions.push(
          'parent:read',
          'parent:update-self',
          'student:read-children',
          'leave:create-for-children',
          'leave:read-children',
        );
        break;

      case 'visitor':
        permissions.push(
          'visitor:read',
          'visitor:update-self',
          'visit:create',
          'visit:read-self',
        );
        break;

      default:
        break;
    }

    return permissions;
  }

  /**
   * 生成用户角色列表
   */
  generateUserRoles(user: User): string[] {
    const roles: string[] = ['user'];

    // 根据用户类型添加角色
    switch (user.getUserTypeString()) {
      case 'student':
        roles.push('student');
        break;
      case 'teacher':
        roles.push('teacher', 'staff');
        break;
      case 'parent':
        roles.push('parent');
        break;
      case 'visitor':
        roles.push('visitor');
        break;
      default:
        break;
    }

    return roles;
  }

  /**
   * 创建JWT载荷
   */
  createJwtPayload(user: User): JwtPayload {
    const now = Math.floor(Date.now() / 1000);
    const expiresIn = 24 * 60 * 60; // 24小时

    const profile = user.getProfile();
    if (!profile) {
      throw new BusinessRuleException('用户档案信息不完整');
    }

    return new JwtPayload({
      sub: user.id,
      username: profile.name,
      userType: user.getUserTypeString(),
      tenantId: user.getTenantId(),
      roles: this.generateUserRoles(user),
      permissions: this.generateUserPermissions(user),
      iat: now,
      exp: now + expiresIn,
    });
  }

  /**
   * 验证刷新令牌
   */
  async validateRefreshToken(
    refreshToken: string,
    userId: string,
  ): Promise<boolean> {
    // 这里可以实现刷新令牌的验证逻辑
    // 例如检查令牌是否在黑名单中，是否过期等

    // 验证用户是否存在且激活
    const user = await this.userRepository.findById(userId);
    if (!user || !user.isUserActive()) {
      return false;
    }

    // 这里可以添加更多的刷新令牌验证逻辑
    // 例如检查令牌是否在数据库中存在，是否被撤销等

    return true;
  }

  /**
   * 检查用户登录限制
   */
  async checkLoginRestrictions(
    user: User,
    clientIP: string = '127.0.0.1',
    userAgent?: string,
  ): Promise<void> {
    // 检查用户是否被锁定
    if (user.isLocked()) {
      throw new BusinessRuleException('用户账户已被锁定');
    }

    // 使用登录限制服务进行详细检查
    const restrictionResult =
      await this.loginRestrictionService.checkLoginRestrictions(
        user,
        clientIP,
        userAgent,
      );

    if (!restrictionResult.allowed) {
      throw new BusinessRuleException(
        restrictionResult.reason || '登录被限制',
        restrictionResult.code,
      );
    }
  }

  /**
   * 记录登录日志
   */
  async recordLoginLog(
    user: User,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    // 这里可以实现登录日志记录逻辑
    // 例如记录登录时间、IP地址、设备信息等

    // 更新用户最后登录时间
    user.updateLastLoginTime();
    await this.userRepository.save(user);
  }
}

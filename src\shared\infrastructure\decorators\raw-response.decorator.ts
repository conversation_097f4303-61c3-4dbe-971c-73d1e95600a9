import { SetMetadata } from '@nestjs/common';

/**
 * 原始响应装饰器的元数据键
 */
export const RAW_RESPONSE_KEY = 'raw_response';

/**
 * 原始响应装饰器
 * 标记控制器方法返回原始响应，不进行统一格式化
 * 
 * @example
 * ```typescript
 * @Get('download')
 * @RawResponse()
 * async downloadFile(): Promise<StreamableFile> {
 *   return new StreamableFile(buffer);
 * }
 * ```
 */
export const RawResponse = () => SetMetadata(RAW_RESPONSE_KEY, true);

/**
 * 跳过响应拦截器装饰器
 * 与RawResponse功能相同，提供更明确的语义
 */
export const SkipResponseInterceptor = () => SetMetadata(RAW_RESPONSE_KEY, true);

/**
 * 自定义响应格式装饰器
 * 允许指定自定义的响应格式化函数
 */
export const CustomResponse = (formatter?: (data: any) => any) => {
  return SetMetadata('custom_response_formatter', formatter || ((data: any) => data));
};

/**
 * 流式响应装饰器
 * 用于标记流式响应（如文件下载、SSE等）
 */
export const StreamResponse = () => SetMetadata('stream_response', true);

/**
 * 二进制响应装饰器
 * 用于标记二进制响应（如图片、文件等）
 */
export const BinaryResponse = () => SetMetadata('binary_response', true);

/**
 * HTML响应装饰器
 * 用于标记HTML响应（如模板渲染）
 */
export const HtmlResponse = () => SetMetadata('html_response', true);

/**
 * XML响应装饰器
 * 用于标记XML响应
 */
export const XmlResponse = () => SetMetadata('xml_response', true);

/**
 * 纯文本响应装饰器
 * 用于标记纯文本响应
 */
export const TextResponse = () => SetMetadata('text_response', true);

/**
 * 响应类型枚举
 */
export enum ResponseType {
  JSON = 'json',
  HTML = 'html',
  XML = 'xml',
  TEXT = 'text',
  BINARY = 'binary',
  STREAM = 'stream',
  RAW = 'raw',
}

/**
 * 响应配置接口
 */
export interface ResponseConfig {
  type: ResponseType;
  contentType?: string;
  headers?: Record<string, string>;
  formatter?: (data: any) => any;
}

/**
 * 配置响应装饰器
 * 提供完整的响应配置选项
 */
export const ConfigureResponse = (config: ResponseConfig) => {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // 设置响应类型
    SetMetadata('response_type', config.type)(target, propertyKey, descriptor);
    
    // 设置内容类型
    if (config.contentType) {
      SetMetadata('response_content_type', config.contentType)(target, propertyKey, descriptor);
    }
    
    // 设置响应头
    if (config.headers) {
      SetMetadata('response_headers', config.headers)(target, propertyKey, descriptor);
    }
    
    // 设置格式化函数
    if (config.formatter) {
      SetMetadata('response_formatter', config.formatter)(target, propertyKey, descriptor);
    }
    
    return descriptor;
  };
};

/**
 * 缓存响应装饰器
 * 设置响应缓存策略
 */
export const CacheResponse = (options: {
  maxAge?: number;
  private?: boolean;
  noCache?: boolean;
  noStore?: boolean;
  mustRevalidate?: boolean;
}) => {
  return SetMetadata('cache_response', options);
};

/**
 * 压缩响应装饰器
 * 启用响应压缩
 */
export const CompressResponse = (enabled: boolean = true) => {
  return SetMetadata('compress_response', enabled);
};

/**
 * 响应状态码装饰器
 * 设置自定义响应状态码
 */
export const ResponseStatus = (statusCode: number) => {
  return SetMetadata('response_status_code', statusCode);
};

/**
 * 响应装饰器工具类
 */
export class ResponseDecoratorUtils {
  /**
   * 检查是否为原始响应
   */
  static isRawResponse(target: any): boolean {
    return Reflect.getMetadata(RAW_RESPONSE_KEY, target) === true;
  }

  /**
   * 获取响应类型
   */
  static getResponseType(target: any): ResponseType | undefined {
    return Reflect.getMetadata('response_type', target);
  }

  /**
   * 获取内容类型
   */
  static getContentType(target: any): string | undefined {
    return Reflect.getMetadata('response_content_type', target);
  }

  /**
   * 获取响应头
   */
  static getResponseHeaders(target: any): Record<string, string> | undefined {
    return Reflect.getMetadata('response_headers', target);
  }

  /**
   * 获取格式化函数
   */
  static getFormatter(target: any): ((data: any) => any) | undefined {
    return Reflect.getMetadata('response_formatter', target);
  }

  /**
   * 获取缓存配置
   */
  static getCacheConfig(target: any): any {
    return Reflect.getMetadata('cache_response', target);
  }

  /**
   * 检查是否启用压缩
   */
  static isCompressionEnabled(target: any): boolean {
    return Reflect.getMetadata('compress_response', target) === true;
  }

  /**
   * 获取状态码
   */
  static getStatusCode(target: any): number | undefined {
    return Reflect.getMetadata('response_status_code', target);
  }
}

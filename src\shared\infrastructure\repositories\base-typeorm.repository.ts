import { Repository, EntityManager } from 'typeorm';
import { Logger } from '@nestjs/common';
import { BaseRepository, AuditContext, AuditAction } from '../../domain/repositories/base-repository';
import { AggregateRoot } from '../../domain/base/aggregate-root';
import { BaseEntity } from '../../domain/base/base-entity';
import { AuditService } from '../../application/services/audit.service';

/**
 * 基础TypeORM仓储实现
 * 提供审计功能支持
 */
export abstract class BaseTypeOrmRepository<T extends AggregateRoot & BaseEntity>
  implements BaseRepository<T>
{
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly repository: Repository<T>,
    protected readonly auditService: AuditService,
    protected readonly entityManager?: EntityManager,
  ) {}

  /**
   * 根据ID查找聚合
   */
  async findById(id: string): Promise<T | null> {
    const startTime = Date.now();
    try {
      const entity = await this.repository.findOne({
        where: { id, isDeleted: false } as any,
      });

      this.logPerformance('findById', startTime);
      return entity;
    } catch (error) {
      this.logger.error(`Error finding ${this.getEntityName()} by id: ${id}`, error);
      throw error;
    }
  }

  /**
   * 保存聚合
   */
  async save(aggregate: T, auditContext?: AuditContext): Promise<T> {
    const startTime = Date.now();
    try {
      // 应用审计信息
      if (auditContext) {
        this.auditService.applyAudit(aggregate, auditContext);
      }

      // 保存实体
      const savedEntity = await this.repository.save(aggregate);

      // 记录审计日志
      if (auditContext) {
        await this.auditService.logAudit({
          ...auditContext,
          entityName: this.getEntityName(),
          entityId: savedEntity.id,
        });
      }

      this.logPerformance('save', startTime);
      return savedEntity;
    } catch (error) {
      this.logger.error(`Error saving ${this.getEntityName()}`, error);
      throw error;
    }
  }

  /**
   * 删除聚合（硬删除）
   */
  async delete(id: string, auditContext?: AuditContext): Promise<void> {
    const startTime = Date.now();
    try {
      const entity = await this.findById(id);
      if (!entity) {
        throw new Error(`${this.getEntityName()} with id ${id} not found`);
      }

      await this.repository.delete(id);

      // 记录审计日志
      if (auditContext) {
        await this.auditService.logAudit({
          ...auditContext,
          action: AuditAction.DELETE,
          entityName: this.getEntityName(),
          entityId: id,
        });
      }

      this.logPerformance('delete', startTime);
    } catch (error) {
      this.logger.error(`Error deleting ${this.getEntityName()} with id: ${id}`, error);
      throw error;
    }
  }

  /**
   * 软删除聚合
   */
  async softDelete(id: string, auditContext?: AuditContext): Promise<void> {
    const startTime = Date.now();
    try {
      const entity = await this.findById(id);
      if (!entity) {
        throw new Error(`${this.getEntityName()} with id ${id} not found`);
      }

      // 应用软删除审计
      const deleteContext: AuditContext = {
        ...auditContext,
        action: AuditAction.DELETE,
      };

      this.auditService.applyAudit(entity, deleteContext);
      await this.repository.save(entity);

      // 记录审计日志
      await this.auditService.logAudit({
        ...deleteContext,
        entityName: this.getEntityName(),
        entityId: id,
      });

      this.logPerformance('softDelete', startTime);
    } catch (error) {
      this.logger.error(`Error soft deleting ${this.getEntityName()} with id: ${id}`, error);
      throw error;
    }
  }

  /**
   * 恢复删除的聚合
   */
  async restore(id: string, auditContext?: AuditContext): Promise<void> {
    const startTime = Date.now();
    try {
      // 查找包括已删除的实体
      const entity = await this.repository.findOne({
        where: { id } as any,
        withDeleted: true,
      });

      if (!entity) {
        throw new Error(`${this.getEntityName()} with id ${id} not found`);
      }

      // 应用恢复审计
      const restoreContext: AuditContext = {
        ...auditContext,
        action: AuditAction.RESTORE,
      };

      this.auditService.applyAudit(entity, restoreContext);
      await this.repository.save(entity);

      // 记录审计日志
      await this.auditService.logAudit({
        ...restoreContext,
        entityName: this.getEntityName(),
        entityId: id,
      });

      this.logPerformance('restore', startTime);
    } catch (error) {
      this.logger.error(`Error restoring ${this.getEntityName()} with id: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查聚合是否存在
   */
  async exists(id: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      const count = await this.repository.count({
        where: { id, isDeleted: false } as any,
      });

      this.logPerformance('exists', startTime);
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking existence of ${this.getEntityName()} with id: ${id}`, error);
      throw error;
    }
  }

  /**
   * 批量保存
   */
  async saveBatch(aggregates: T[], auditContext?: AuditContext): Promise<T[]> {
    const startTime = Date.now();
    try {
      // 应用审计信息
      if (auditContext) {
        this.auditService.applyBatchAudit(aggregates, auditContext);
      }

      const savedEntities = await this.repository.save(aggregates);

      // 记录审计日志
      if (auditContext) {
        for (const entity of savedEntities) {
          await this.auditService.logAudit({
            ...auditContext,
            entityName: this.getEntityName(),
            entityId: entity.id,
          });
        }
      }

      this.logPerformance('saveBatch', startTime);
      return savedEntities;
    } catch (error) {
      this.logger.error(`Error batch saving ${this.getEntityName()}`, error);
      throw error;
    }
  }

  /**
   * 创建审计上下文
   */
  protected createAuditContext(
    action: AuditAction,
    userId?: string,
    metadata?: Record<string, any>,
  ): AuditContext {
    return {
      userId,
      action,
      metadata,
    };
  }

  /**
   * 记录性能日志
   */
  protected logPerformance(operation: string, startTime: number): void {
    const duration = Date.now() - startTime;
    if (duration > 1000) {
      this.logger.warn(`Slow ${operation} operation: ${duration}ms`);
    } else {
      this.logger.debug(`${operation} completed in ${duration}ms`);
    }
  }

  /**
   * 获取实体名称（子类需要实现）
   */
  protected abstract getEntityName(): string;
}

import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../base.entity';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
}

/**
 * 学生用户实体
 */
@Entity('user_students')
export class UserStudent extends BaseEntity {
  @Column({ unique: true, comment: '用户名' })
  username: string;

  @Column({ comment: '密码哈希' })
  passwordHash: string;

  @Column({ comment: '真实姓名' })
  realName: string;

  @Column({ nullable: true, comment: '邮箱' })
  email?: string;

  @Column({ nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ nullable: true, unique: true, comment: '学号' })
  studentNumber?: string;

  @Column({ nullable: true, comment: '班级ID' })
  classId?: string;

  @Column({ nullable: true, comment: '年级' })
  grade?: string;

  @Column({
    type: 'enum',
    enum: Gender,
    nullable: true,
    comment: '性别',
  })
  gender?: Gender;

  @Column({ type: 'date', nullable: true, comment: '出生日期' })
  birthDate?: Date;

  @Column({ nullable: true, comment: '身份证号' })
  idCard?: string;

  @Column({ nullable: true, comment: '家庭地址' })
  address?: string;

  @Column({ default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({ nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ nullable: true, comment: '头像URL' })
  avatarUrl?: string;

  @Column({ nullable: true, comment: '入学时间' })
  enrollmentDate?: Date;

  @Column({ default: 'enrolled', comment: '学生状态' })
  status: string;

  /**
   * 验证密码
   */
  validatePassword(password: string): boolean {
    // 这里应该使用实际的密码验证逻辑
    // 例如使用 bcrypt 比较密码
    return true; // 临时实现
  }

  /**
   * 更新最后登录时间
   */
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    return this.realName || this.username;
  }

  /**
   * 获取年龄
   */
  getAge(): number | null {
    if (!this.birthDate) return null;
    
    const today = new Date();
    const birthDate = new Date(this.birthDate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }
}

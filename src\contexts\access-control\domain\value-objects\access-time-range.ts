import { ValueObject } from '@/shared/domain/base/value-object';
import { ValidationException } from '@/shared/application/exceptions/domain-exception';

/**
 * 门禁时间范围值对象
 */
export interface AccessTimeRangeProps {
  startTime: Date;
  endTime: Date;
  timezone?: string;
}

export class AccessTimeRange extends ValueObject<AccessTimeRangeProps> {
  constructor(props: AccessTimeRangeProps) {
    AccessTimeRange.validate(props);
    super(props);
  }

  private static validate(props: AccessTimeRangeProps): void {
    const errors: Record<string, string[]> = {};

    // 验证开始时间
    if (!props.startTime) {
      errors.startTime = ['开始时间不能为空'];
    }

    // 验证结束时间
    if (!props.endTime) {
      errors.endTime = ['结束时间不能为空'];
    }

    // 验证时间范围
    if (props.startTime && props.endTime) {
      if (props.startTime >= props.endTime) {
        errors.timeRange = ['开始时间必须早于结束时间'];
      }

      // 验证时间范围不能超过24小时
      const diffHours = (props.endTime.getTime() - props.startTime.getTime()) / (1000 * 60 * 60);
      if (diffHours > 24) {
        errors.timeRange = [...(errors.timeRange || []), '时间范围不能超过24小时'];
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new ValidationException(errors);
    }
  }

  get startTime(): Date {
    return this._value.startTime;
  }

  get endTime(): Date {
    return this._value.endTime;
  }

  get timezone(): string | undefined {
    return this._value.timezone;
  }

  /**
   * 获取持续时间（分钟）
   */
  getDurationInMinutes(): number {
    return (this._value.endTime.getTime() - this._value.startTime.getTime()) / (1000 * 60);
  }

  /**
   * 获取持续时间（小时）
   */
  getDurationInHours(): number {
    return this.getDurationInMinutes() / 60;
  }

  /**
   * 检查指定时间是否在范围内
   */
  contains(time: Date): boolean {
    return time >= this._value.startTime && time <= this._value.endTime;
  }

  /**
   * 检查是否与另一个时间范围重叠
   */
  overlaps(other: AccessTimeRange): boolean {
    return this._value.startTime < other.endTime && this._value.endTime > other.startTime;
  }

  /**
   * 检查是否完全包含另一个时间范围
   */
  fullyContains(other: AccessTimeRange): boolean {
    return this._value.startTime <= other.startTime && this._value.endTime >= other.endTime;
  }

  /**
   * 扩展时间范围
   */
  extend(minutes: number): AccessTimeRange {
    return new AccessTimeRange({
      startTime: new Date(this._value.startTime.getTime() - minutes * 60 * 1000),
      endTime: new Date(this._value.endTime.getTime() + minutes * 60 * 1000),
      timezone: this._value.timezone
    });
  }

  /**
   * 检查是否是当天的时间范围
   */
  isToday(): boolean {
    const today = new Date();
    const startDate = new Date(this._value.startTime);
    const endDate = new Date(this._value.endTime);

    return (
      startDate.toDateString() === today.toDateString() ||
      endDate.toDateString() === today.toDateString()
    );
  }

  /**
   * 检查是否已过期
   */
  isExpired(): boolean {
    return new Date() > this._value.endTime;
  }

  /**
   * 检查是否即将开始（在指定分钟内）
   */
  isStartingSoon(minutes: number = 15): boolean {
    const now = new Date();
    const timeDiff = this._value.startTime.getTime() - now.getTime();
    const minutesDiff = timeDiff / (1000 * 60);
    
    return minutesDiff > 0 && minutesDiff <= minutes;
  }

  /**
   * 格式化为字符串
   */
  format(locale: string = 'zh-CN'): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: this._value.timezone
    };

    const startStr = this._value.startTime.toLocaleString(locale, options);
    const endStr = this._value.endTime.toLocaleString(locale, options);

    return `${startStr} - ${endStr}`;
  }

  /**
   * 创建今天的时间范围
   */
  static today(startHour: number, startMinute: number, endHour: number, endMinute: number): AccessTimeRange {
    const today = new Date();
    const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), startHour, startMinute);
    const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), endHour, endMinute);

    return new AccessTimeRange({ startTime, endTime });
  }

  /**
   * 创建工作时间范围（9:00-17:00）
   */
  static workingHours(date?: Date): AccessTimeRange {
    const targetDate = date || new Date();
    const startTime = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), 9, 0);
    const endTime = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), 17, 0);

    return new AccessTimeRange({ startTime, endTime });
  }

  /**
   * 创建全天时间范围
   */
  static allDay(date?: Date): AccessTimeRange {
    const targetDate = date || new Date();
    const startTime = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), 0, 0);
    const endTime = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate(), 23, 59, 59);

    return new AccessTimeRange({ startTime, endTime });
  }
}

import {
  Controller,
  Post,
  Get,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DeveloperAccountService } from '../../infrastructure/services/developer-account.service';
import { DataInitializationService } from '../../infrastructure/services/data-initialization.service';
import { ResponseUtil } from '@/shared/application/utils/response.util';

/**
 * 开发环境设置控制器
 * 用于手动创建开发者账号和初始化数据
 */
@ApiTags('开发环境设置')
@Controller('dev-setup')
export class DevSetupController {
  constructor(
    private readonly developerAccountService: DeveloperAccountService,
    private readonly dataInitializationService: DataInitializationService,
  ) {}

  /**
   * 手动创建开发者账号
   */
  @Post('create-developer')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '手动创建开发者账号' })
  @ApiResponse({ status: 201, description: '开发者账号创建成功' })
  async createDeveloper() {
    try {
      const developer = await this.developerAccountService.ensureDeveloperAccountExists();
      const profile = developer.getProfile();

      return ResponseUtil.success(
        {
          id: developer.id,
          name: profile?.name,
          phone: profile?.phone,
          accessLevel: developer.getAccessLevel(),
          allowedEnvironments: developer.getAllowedEnvironments(),
          apiKey: developer.getApiKey()?.substring(0, 8) + '...',
          loginStats: developer.getLoginStats(),
        },
        '开发者账号创建成功',
      );
    } catch (error) {
      return ResponseUtil.error(`创建开发者账号失败: ${error.message}`, 500);
    }
  }

  /**
   * 检查开发者账号状态
   */
  @Get('check-developer')
  @ApiOperation({ summary: '检查开发者账号状态' })
  @ApiResponse({ status: 200, description: '检查成功' })
  async checkDeveloper() {
    try {
      const devPhone = '***********'; // 从配置中获取
      const info = await this.developerAccountService.getDeveloperInfo(devPhone);

      if (info) {
        return ResponseUtil.success(
          {
            exists: true,
            ...info,
            apiKey: info.apiKey.substring(0, 8) + '...',
          },
          '开发者账号已存在',
        );
      } else {
        return ResponseUtil.success(
          { exists: false },
          '开发者账号不存在',
        );
      }
    } catch (error) {
      return ResponseUtil.error(`检查开发者账号失败: ${error.message}`, 500);
    }
  }

  /**
   * 初始化所有数据
   */
  @Post('initialize-data')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '初始化所有数据' })
  @ApiResponse({ status: 201, description: '数据初始化成功' })
  async initializeData() {
    try {
      const results = await this.dataInitializationService.initializeAllData();

      return ResponseUtil.success(
        results,
        '数据初始化完成',
      );
    } catch (error) {
      return ResponseUtil.error(`数据初始化失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取初始化状态
   */
  @Get('initialization-status')
  @ApiOperation({ summary: '获取初始化状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInitializationStatus() {
    try {
      const status = await this.dataInitializationService.getInitializationStatus();

      return ResponseUtil.success(
        status,
        '获取初始化状态成功',
      );
    } catch (error) {
      return ResponseUtil.error(`获取初始化状态失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取开发者登录信息
   */
  @Get('login-info')
  @ApiOperation({ summary: '获取开发者登录信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getLoginInfo() {
    try {
      const devPhone = '***********';
      const info = await this.developerAccountService.getDeveloperInfo(devPhone);

      if (info) {
        return ResponseUtil.success(
          {
            phone: info.phone,
            password: 'Dev@123456', // 从配置中获取
            name: info.name,
            accessLevel: info.accessLevel,
            apiKey: info.apiKey,
            loginUrl: '/api/auth/login',
            instructions: [
              '1. 使用上述手机号和密码登录',
              '2. 登录成功后会获得access_token',
              '3. 在请求头中添加: Authorization: Bearer <access_token>',
              '4. 对于需要API密钥的接口，添加: X-API-Key: <apiKey>',
            ],
          },
          '开发者登录信息',
        );
      } else {
        return ResponseUtil.error('开发者账号不存在，请先创建', 404);
      }
    } catch (error) {
      return ResponseUtil.error(`获取登录信息失败: ${error.message}`, 500);
    }
  }

  /**
   * 重置开发者密码
   */
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重置开发者密码' })
  @ApiResponse({ status: 200, description: '密码重置成功' })
  async resetPassword() {
    try {
      const devPhone = '***********';
      const newPassword = 'Dev@123456';

      await this.developerAccountService.resetDeveloperPassword(devPhone, newPassword);

      return ResponseUtil.success(
        {
          phone: devPhone,
          newPassword: newPassword,
          message: '密码已重置为默认密码',
        },
        '密码重置成功',
      );
    } catch (error) {
      return ResponseUtil.error(`密码重置失败: ${error.message}`, 500);
    }
  }

  /**
   * 获取系统信息
   */
  @Get('system-info')
  @ApiOperation({ summary: '获取系统信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSystemInfo() {
    return ResponseUtil.success(
      {
        environment: process.env.NODE_ENV || 'development',
        createDevAccount: process.env.CREATE_DEV_ACCOUNT || 'false',
        devAccountPhone: process.env.DEV_ACCOUNT_PHONE || '***********',
        devAccountName: process.env.DEV_ACCOUNT_NAME || '系统开发者',
        devAccessLevel: process.env.DEV_ACCESS_LEVEL || 'admin',
        timestamp: new Date().toISOString(),
      },
      '系统信息',
    );
  }
}

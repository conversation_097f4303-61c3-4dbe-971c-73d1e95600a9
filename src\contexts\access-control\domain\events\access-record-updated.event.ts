import { DomainEvent } from '@/shared/domain/events/domain-event';
import { AccessRecordStatus } from '../aggregates/access-record.aggregate';

/**
 * 门禁记录更新事件
 */
export class AccessRecordUpdatedEvent extends DomainEvent {
  constructor(
    public readonly recordId: string,
    public readonly changes: {
      verificationMethod?: string;
      confidenceScore?: number;
      temperature?: number;
      status?: AccessRecordStatus;
      remark?: string;
    },
  ) {
    super(recordId);
  }

  getEventName(): string {
    return 'access-record.updated';
  }

  getEventData(): Record<string, any> {
    return {
      recordId: this.recordId,
      changes: this.changes,
    };
  }

  /**
   * 检查是否状态发生了变化
   */
  hasStatusChanged(): boolean {
    return this.changes.status !== undefined;
  }

  /**
   * 检查是否变为异常状态
   */
  becameAbnormal(): boolean {
    return this.changes.status === AccessRecordStatus.ABNORMAL;
  }

  /**
   * 检查是否变为可疑状态
   */
  becameSuspicious(): boolean {
    return this.changes.status === AccessRecordStatus.SUSPICIOUS;
  }

  /**
   * 检查是否体温异常
   */
  hasAbnormalTemperature(): boolean {
    return this.changes.temperature !== undefined && 
           (this.changes.temperature < 36.0 || this.changes.temperature > 37.5);
  }

  /**
   * 检查是否置信度过低
   */
  hasLowConfidence(): boolean {
    return this.changes.confidenceScore !== undefined && 
           this.changes.confidenceScore < 0.7;
  }
}

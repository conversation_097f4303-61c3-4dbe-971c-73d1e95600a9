import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService } from './app.service';
import { RawResponse } from './shared/infrastructure/decorators/raw-response.decorator';

@ApiTags('应用')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({
    summary: '获取问候语',
    description: '返回一个简单的问候语',
  })
  @ApiResponse({
    status: 200,
    description: '成功返回问候语',
    schema: {
      type: 'object',
      properties: {
        errCode: { type: 'number', example: 0 },
        message: { type: 'string', example: 'success' },
        data: { type: 'string', example: 'Hello World!' },
      },
    },
  })
  getHello() {
    this.logger.log('收到根路径请求');
    // 拦截器会自动包装返回值
    return this.appService.getHello();
  }

  @Get('health')
  @ApiOperation({
    summary: '健康检查',
    description: '检查应用程序的健康状态',
  })
  @ApiResponse({
    status: 200,
    description: '健康检查成功',
    schema: {
      type: 'object',
      properties: {
        errCode: { type: 'number', example: 0 },
        message: { type: 'string', example: 'success' },
        data: { $ref: '#/components/schemas/HealthResponseDto' },
      },
    },
  })
  getHealth() {
    this.logger.log('收到健康检查请求');
    // 拦截器会自动包装返回值
    return this.appService.getHealth();
  }

  @Get('raw')
  @RawResponse()
  @ApiOperation({
    summary: '原始响应示例',
    description: '返回原始响应，不会被拦截器包装',
  })
  @ApiResponse({
    status: 200,
    description: '原始响应',
    schema: {
      type: 'object',
      properties: {
        customField: { type: 'string' },
        timestamp: { type: 'number' },
      },
    },
  })
  getRawResponse() {
    this.logger.log('收到原始响应请求');
    // 使用 @RawResponse() 装饰器，不会被拦截器包装
    return {
      customField: '这是原始响应',
      timestamp: Date.now(),
    };
  }
}

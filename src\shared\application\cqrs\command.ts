/**
 * 命令接口
 * 所有命令都应该实现此接口
 */
export interface Command {
  readonly commandId?: string;
  readonly timestamp?: Date;
}

/**
 * 命令处理器接口
 */
export interface CommandHandler<TCommand extends Command, TResult = void> {
  handle(command: TCommand): Promise<TResult>;
}

/**
 * 命令总线接口
 */
export interface CommandBus {
  /**
   * 执行命令
   */
  execute<TCommand extends Command, TResult = void>(
    command: TCommand,
  ): Promise<TResult>;

  /**
   * 注册命令处理器
   */
  register<TCommand extends Command, TResult = void>(
    commandType: new (...args: any[]) => TCommand,
    handler: CommandHandler<TCommand, TResult>,
  ): void;
}

/**
 * 基础命令类
 */
export abstract class BaseCommand implements Command {
  public readonly commandId: string;
  public readonly timestamp: Date;

  constructor() {
    this.commandId = this.generateId();
    this.timestamp = new Date();
  }

  private generateId(): string {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 命令验证器接口
 */
export interface CommandValidator<TCommand extends Command> {
  validate(command: TCommand): Promise<ValidationResult>;
}

/**
 * 验证结果
 */
export class ValidationResult {
  constructor(
    public readonly isValid: boolean,
    public readonly errors: string[] = [],
  ) {}

  static success(): ValidationResult {
    return new ValidationResult(true);
  }

  static failure(errors: string[]): ValidationResult {
    return new ValidationResult(false, errors);
  }
}

/**
 * 命令执行结果
 */
export class CommandResult<T = any> {
  constructor(
    public readonly success: boolean,
    public readonly data?: T,
    public readonly error?: string,
    public readonly errors?: string[],
  ) {}

  static success<T>(data?: T): CommandResult<T> {
    return new CommandResult(true, data);
  }

  static failure(error: string, errors?: string[]): CommandResult {
    return new CommandResult(false, undefined, error, errors);
  }
}

/**
 * 命令装饰器
 */
export function CommandHandler(commandType: new (...args: any[]) => Command) {
  return function (target: any) {
    Reflect.defineMetadata('command-handler', commandType, target);
    return target;
  };
}

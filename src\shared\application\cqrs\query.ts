/**
 * 查询接口
 * 所有查询都应该实现此接口
 */
export interface Query {
  readonly queryId?: string;
  readonly timestamp?: Date;
}

/**
 * 查询处理器接口
 */
export interface QueryHandler<TQuery extends Query, TResult = any> {
  handle(query: TQuery): Promise<TResult>;
}

/**
 * 查询总线接口
 */
export interface QueryBus {
  /**
   * 执行查询
   */
  execute<TQuery extends Query, TResult = any>(query: TQuery): Promise<TResult>;

  /**
   * 注册查询处理器
   */
  register<TQuery extends Query, TResult = any>(
    queryType: new (...args: any[]) => TQuery,
    handler: QueryHandler<TQuery, TResult>,
  ): void;
}

/**
 * 基础查询类
 */
export abstract class BaseQuery implements Query {
  public readonly queryId: string;
  public readonly timestamp: Date;

  constructor() {
    this.queryId = this.generateId();
    this.timestamp = new Date();
  }

  private generateId(): string {
    return `qry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 分页查询基类
 */
export abstract class PaginatedQuery extends BaseQuery {
  constructor(
    public readonly page: number = 1,
    public readonly pageSize: number = 10,
    public readonly sortBy?: string,
    public readonly sortOrder: 'ASC' | 'DESC' = 'DESC',
  ) {
    super();
  }

  /**
   * 获取跳过的记录数
   */
  getSkip(): number {
    return (this.page - 1) * this.pageSize;
  }

  /**
   * 获取排序配置
   */
  getSort(): Record<string, 'ASC' | 'DESC'> | undefined {
    return this.sortBy ? { [this.sortBy]: this.sortOrder } : undefined;
  }
}

/**
 * 查询结果包装器
 */
export class QueryResult<T = any> {
  constructor(
    public readonly success: boolean,
    public readonly data?: T,
    public readonly error?: string,
    public readonly metadata?: Record<string, any>,
  ) {}

  static success<T>(data: T, metadata?: Record<string, any>): QueryResult<T> {
    return new QueryResult(true, data, undefined, metadata);
  }

  static failure(error: string): QueryResult {
    return new QueryResult(false, undefined, error);
  }
}

/**
 * 分页查询结果
 */
export class PaginatedQueryResult<T = any> extends QueryResult<T[]> {
  constructor(
    public readonly items: T[],
    public readonly total: number,
    public readonly page: number,
    public readonly pageSize: number,
    public readonly totalPages: number = Math.ceil(total / pageSize),
  ) {
    super(true, items, undefined, {
      pagination: {
        total,
        page,
        pageSize,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  }
}

/**
 * 查询装饰器
 */
export function QueryHandler(queryType: new (...args: any[]) => Query) {
  return function (target: any) {
    Reflect.defineMetadata('query-handler', queryType, target);
    return target;
  };
}

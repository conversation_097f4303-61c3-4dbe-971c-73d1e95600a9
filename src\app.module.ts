import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from '@/app.controller';
import { AppService } from '@/app.service';

// 新的DDD架构模块
import { SharedModule } from '@/shared/shared.module';
import { IdentityAccessModule } from '@/contexts/identity-access/identity-access.module';

// 基础设施模块
import { CacheModule } from '@/shared/infrastructure/cache/cache.module';

// 数据库配置
import { DatabaseConfigFactory } from '@/shared/infrastructure/database/database.config';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // 数据库配置
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        return DatabaseConfigFactory.createTypeOrmOptions(configService);
      },
      inject: [ConfigService],
    }),

    // 共享模块（必须在其他模块之前）
    SharedModule,

    // 新的DDD架构模块
    IdentityAccessModule,

    // 基础设施模块
    CacheModule.forRoot(),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    console.log('AppModule initialized with new DDD architecture');
  }
}

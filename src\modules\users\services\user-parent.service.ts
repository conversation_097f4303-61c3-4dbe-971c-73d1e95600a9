import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like } from 'typeorm';
import { UserParent } from '@entities/users/user-parent.entity';
import {
  CreateUserParentDto,
  UpdateUserParentDto,
  QueryUserParentDto,
  UserParentResponseDto,
} from '../dto/user-parent.dto';
import { ApiException } from '@/common/exceptions/api.exception';

@Injectable()
export class UserParentService {
  private readonly logger = new Logger(UserParentService.name);

  constructor(
    @InjectRepository(UserParent)
    private readonly userParentRepository: Repository<UserParent>,
  ) {}

  /**
   * 创建家长
   */
  async create(createDto: CreateUserParentDto): Promise<UserParentResponseDto> {
    // 检查手机号是否已存在
    const existingPhone = await this.userParentRepository.findOne({
      where: { phone: createDto.phone },
    });

    if (existingPhone) {
      throw ApiException.Error('手机号已存在');
    }

    // 转换日期字符串为Date对象
    const parentData = {
      ...createDto,
      birthday: createDto.birthday ? new Date(createDto.birthday) : undefined,
    };

    const parent = this.userParentRepository.create(parentData);
    const savedParent = await this.userParentRepository.save(parent);
    return this.transformToResponseDto(savedParent);
  }

  /**
   * 根据ID查找家长
   */
  async findById(id: string): Promise<UserParentResponseDto> {
    const parent = await this.userParentRepository.findOne({
      where: { id },
    });

    if (!parent) {
      throw ApiException.Error('家长不存在');
    }

    return this.transformToResponseDto(parent);
  }

  /**
   * 分页查询家长
   */
  async findMany(
    query: QueryUserParentDto,
  ): Promise<{
    data: UserParentResponseDto[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const { page = 1, pageSize = 10, name, phone } = query;
    const skip = (page - 1) * pageSize;

    const whereConditions: FindOptionsWhere<UserParent> = {};

    if (name) {
      whereConditions.name = Like(`%${name}%`);
    }

    if (phone) {
      whereConditions.phone = Like(`%${phone}%`);
    }

    const [parents, total] = await this.userParentRepository.findAndCount({
      where: whereConditions,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' },
    });

    return {
      data: parents.map(this.transformToResponseDto),
      total,
      page,
      pageSize,
    };
  }

  /**
   * 更新家长信息
   */
  async update(
    id: string,
    updateDto: UpdateUserParentDto,
  ): Promise<UserParentResponseDto> {
    const parent = await this.userParentRepository.findOne({
      where: { id },
    });

    if (!parent) {
      throw ApiException.Error('家长不存在');
    }

    // 如果更新手机号，检查是否已存在
    if (updateDto.phone && updateDto.phone !== parent.phone) {
      const existingPhone = await this.userParentRepository.findOne({
        where: { phone: updateDto.phone },
      });

      if (existingPhone) {
        throw ApiException.Error('手机号已存在');
      }
    }

    // 转换日期字符串为Date对象
    const updateData = {
      ...updateDto,
      birthday: updateDto.birthday ? new Date(updateDto.birthday) : undefined,
    };

    await this.userParentRepository.update(id, updateData);
    const updatedParent = await this.userParentRepository.findOne({
      where: { id },
    });

    return this.transformToResponseDto(updatedParent!);
  }

  /**
   * 删除家长
   */
  async delete(id: string): Promise<void> {
    const parent = await this.userParentRepository.findOne({
      where: { id },
    });

    if (!parent) {
      throw ApiException.Error('家长不存在');
    }

    await this.userParentRepository.softDelete(id);
  }

  /**
   * 批量删除家长
   */
  async batchDelete(ids: string[]): Promise<void> {
    await this.userParentRepository.softDelete(ids);
  }

  /**
   * 根据手机号查找家长
   */
  async findByPhone(phone: string): Promise<UserParent | null> {
    return await this.userParentRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto = (
    parent: UserParent,
  ): UserParentResponseDto => ({
    id: parent.id,
    name: parent.name,
    phone: parent.phone,
    gender: parent.gender,
    avatar: parent.avatar,
    age: parent.age,
    birthday: parent.birthday,
    cardNo: parent.cardNo,
    cardType: parent.cardType,
    carNo: parent.carNo,
    createdAt: parent.createdAt,
    updatedAt: parent.updatedAt,
  });
}

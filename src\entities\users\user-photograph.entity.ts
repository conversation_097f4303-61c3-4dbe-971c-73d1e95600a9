import { Enti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '../base.entity';

/**
 * 用户照片实体
 */
@Entity('user_photographs')
export class UserPhotograph extends BaseEntity {
  @Column({ comment: '用户ID' })
  userId: string;

  @Column({ comment: '照片URL' })
  photoUrl: string;

  @Column({ comment: '照片类型' })
  photoType: string; // 'avatar', 'id_photo', 'profile', etc.

  @Column({ nullable: true, comment: '照片描述' })
  description?: string;

  @Column({ nullable: true, comment: '文件名' })
  fileName?: string;

  @Column({ nullable: true, comment: '文件大小（字节）' })
  fileSize?: number;

  @Column({ nullable: true, comment: '文件MIME类型' })
  mimeType?: string;

  @Column({ default: false, comment: '是否为主照片' })
  isPrimary: boolean;

  @Column({ default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({ nullable: true, comment: '上传者ID' })
  uploadedBy?: string;

  @Column({ nullable: true, comment: '照片拍摄时间' })
  takenAt?: Date;

  /**
   * 获取照片完整URL
   */
  getFullUrl(baseUrl?: string): string {
    if (this.photoUrl.startsWith('http')) {
      return this.photoUrl;
    }
    
    const base = baseUrl || process.env.FILE_BASE_URL || '';
    return `${base}${this.photoUrl}`;
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(): string | null {
    if (!this.fileName) return null;
    
    const lastDotIndex = this.fileName.lastIndexOf('.');
    if (lastDotIndex === -1) return null;
    
    return this.fileName.substring(lastDotIndex + 1).toLowerCase();
  }

  /**
   * 检查是否为图片文件
   */
  isImageFile(): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const extension = this.getFileExtension();
    
    return extension ? imageExtensions.includes(extension) : false;
  }

  /**
   * 获取格式化的文件大小
   */
  getFormattedFileSize(): string {
    if (!this.fileSize) return '未知';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 设置为主照片
   */
  setPrimary(): void {
    this.isPrimary = true;
  }

  /**
   * 取消主照片状态
   */
  unsetPrimary(): void {
    this.isPrimary = false;
  }
}

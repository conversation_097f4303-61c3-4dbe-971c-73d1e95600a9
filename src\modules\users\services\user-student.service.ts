import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like } from 'typeorm';
import { UserStudent } from '@entities/users/user-student.entity';
import {
  CreateUserStudentDto,
  UpdateUserStudentDto,
  QueryUserStudentDto,
  UserStudentResponseDto,
} from '../dto/user-student.dto';
import { ApiException } from '@/common/exceptions/api.exception';

@Injectable()
export class UserStudentService {
  private readonly logger = new Logger(UserStudentService.name);

  constructor(
    @InjectRepository(UserStudent)
    private readonly userStudentRepository: Repository<UserStudent>,
  ) {}

  /**
   * 创建学生
   */
  async create(
    createDto: CreateUserStudentDto,
  ): Promise<UserStudentResponseDto> {
    // 检查手机号是否已存在
    const existingPhone = await this.userStudentRepository.findOne({
      where: { phone: createDto.phone },
    });

    if (existingPhone) {
      throw ApiException.Error('手机号已存在');
    }

    // 转换日期字符串为Date对象
    const studentData = {
      ...createDto,
      birthday: createDto.birthday ? new Date(createDto.birthday) : undefined,
      enrollmentTime: createDto.enrollmentTime
        ? new Date(createDto.enrollmentTime)
        : undefined,
      graduationTime: createDto.graduationTime
        ? new Date(createDto.graduationTime)
        : undefined,
    };

    const student = this.userStudentRepository.create(studentData);
    const savedStudent = await this.userStudentRepository.save(student);
    return this.transformToResponseDto(savedStudent);
  }

  /**
   * 根据ID查找学生
   */
  async findById(id: string): Promise<UserStudentResponseDto> {
    const student = await this.userStudentRepository.findOne({
      where: { id },
    });

    if (!student) {
      throw ApiException.Error('学生不存在');
    }

    return this.transformToResponseDto(student);
  }

  /**
   * 分页查询学生
   */
  async findMany(
    query: QueryUserStudentDto,
  ): Promise<{
    data: UserStudentResponseDto[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    const { page = 1, pageSize = 10, name, phone, classId, gradeId } = query;
    const skip = (page - 1) * pageSize;

    const whereConditions: FindOptionsWhere<UserStudent> = {};

    if (name) {
      whereConditions.name = Like(`%${name}%`);
    }

    if (phone) {
      whereConditions.phone = Like(`%${phone}%`);
    }

    if (classId) {
      whereConditions.classId = classId;
    }

    if (gradeId) {
      whereConditions.gradeId = gradeId;
    }

    const [students, total] = await this.userStudentRepository.findAndCount({
      where: whereConditions,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' },
    });

    return {
      data: students.map(this.transformToResponseDto),
      total,
      page,
      pageSize,
    };
  }

  /**
   * 更新学生信息
   */
  async update(
    id: string,
    updateDto: UpdateUserStudentDto,
  ): Promise<UserStudentResponseDto> {
    const student = await this.userStudentRepository.findOne({
      where: { id },
    });

    if (!student) {
      throw ApiException.Error('学生不存在');
    }

    // 如果更新手机号，检查是否已存在
    if (updateDto.phone && updateDto.phone !== student.phone) {
      const existingPhone = await this.userStudentRepository.findOne({
        where: { phone: updateDto.phone },
      });

      if (existingPhone) {
        throw ApiException.Error('手机号已存在');
      }
    }

    // 转换日期字符串为Date对象
    const updateData = {
      ...updateDto,
      birthday: updateDto.birthday ? new Date(updateDto.birthday) : undefined,
      enrollmentTime: updateDto.enrollmentTime
        ? new Date(updateDto.enrollmentTime)
        : undefined,
      graduationTime: updateDto.graduationTime
        ? new Date(updateDto.graduationTime)
        : undefined,
    };

    await this.userStudentRepository.update(id, updateData);
    const updatedStudent = await this.userStudentRepository.findOne({
      where: { id },
    });

    return this.transformToResponseDto(updatedStudent!);
  }

  /**
   * 删除学生
   */
  async delete(id: string): Promise<void> {
    const student = await this.userStudentRepository.findOne({
      where: { id },
    });

    if (!student) {
      throw ApiException.Error('学生不存在');
    }

    await this.userStudentRepository.softDelete(id);
  }

  /**
   * 批量删除学生
   */
  async batchDelete(ids: string[]): Promise<void> {
    await this.userStudentRepository.softDelete(ids);
  }

  /**
   * 根据手机号查找学生
   */
  async findByPhone(phone: string): Promise<UserStudent | null> {
    return await this.userStudentRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto = (
    student: UserStudent,
  ): UserStudentResponseDto => ({
    id: student.id,
    name: student.name,
    phone: student.phone,
    gender: student.gender,
    avatar: student.avatar,
    age: student.age,
    birthday: student.birthday,
    cardNo: student.cardNo,
    cardType: student.cardType,
    classId: student.classId,
    gradeId: student.gradeId,
    enrollmentTime: student.enrollmentTime,
    graduationTime: student.graduationTime,
    createdAt: student.createdAt,
    updatedAt: student.updatedAt,
  });
}

import { Command } from '@/shared/application/cqrs/command';

/**
 * 创建学生命令
 */
export class CreateStudentCommand implements Command {
  public readonly commandId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly name: string,
    public readonly phone: string,
    public readonly gender?: number,
    public readonly avatar?: string,
    public readonly age?: number,
    public readonly birthday?: string,
    public readonly cardNo?: string,
    public readonly cardType?: string,
    public readonly classId?: string,
    public readonly gradeId?: string,
    public readonly enrollmentTime?: string,
    public readonly graduationTime?: string,
  ) {
    this.commandId = `create-student-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 更新学生命令
 */
export class UpdateStudentCommand implements Command {
  public readonly commandId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly id: string,
    public readonly name?: string,
    public readonly phone?: string,
    public readonly gender?: number,
    public readonly avatar?: string,
    public readonly age?: number,
    public readonly birthday?: string,
    public readonly cardNo?: string,
    public readonly cardType?: string,
    public readonly classId?: string,
    public readonly gradeId?: string,
    public readonly enrollmentTime?: string,
    public readonly graduationTime?: string,
  ) {
    this.commandId = `update-student-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 删除学生命令
 */
export class DeleteStudentCommand implements Command {
  public readonly commandId?: string;
  public readonly timestamp?: Date;

  constructor(public readonly id: string) {
    this.commandId = `delete-student-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 批量删除学生命令
 */
export class BatchDeleteStudentsCommand {
  constructor(public readonly ids: string[]) {}
}

/**
 * 激活学生命令
 */
export class ActivateStudentCommand {
  constructor(public readonly id: string) {}
}

/**
 * 停用学生命令
 */
export class DeactivateStudentCommand {
  constructor(public readonly id: string) {}
}

/**
 * 兼容性JWT认证守卫
 * 重新导出新框架中的JWT认证守卫，保持向后兼容
 */

// 从新框架中导出JWT认证守卫
export {
  JwtAuthGuard,
  Public,
  IS_PUBLIC_KEY,
  RequestUser,
} from '@/contexts/identity-access/infrastructure/guards/jwt-auth.guard';

// 从新框架中导出权限相关装饰器和守卫
export {
  PermissionsGuard,
  RolesGuard,
  RequirePermissions,
  RequireRoles,
  CurrentUser,
  PERMISSIONS_KEY,
  ROLES_KEY,
} from '@/contexts/identity-access/infrastructure/guards/permissions.guard';

// 从新框架中导出多租户相关
export {
  TenantGuard,
  TenantResourceGuard,
} from '@/shared/infrastructure/guards/tenant.guard';

export {
  TenantIsolation,
  CrossTenant,
  TenantAdmin,
  SuperAdmin,
  RequireTenant,
  AllowTenants,
  SystemOnly,
} from '@/shared/domain/decorators/tenant.decorator';

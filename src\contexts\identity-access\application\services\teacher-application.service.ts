import { Injectable, Inject } from '@nestjs/common';
import { TeacherUser } from '../../domain/aggregates/user.aggregate';
import { UserProfile } from '../../domain/value-objects/user-profile';
import { UserDomainService } from '../../domain/services/user-domain.service';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import {
  EntityNotFoundException,
  BusinessRuleException,
} from '@/shared/application/exceptions/domain-exception';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';
import { UserTypeEnum } from '../../domain/value-objects/user-type';
import { AuditAction } from '@/shared/domain/repositories/base-repository';

/**
 * 教师应用服务
 */
@Injectable()
export class TeacherApplicationService {
  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  /**
   * 创建教师
   */
  async createTeacher(
    teacherData: {
      name: string;
      phone: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      carNo?: string;
    },
    currentUserId?: string,
  ): Promise<TeacherUser> {
    // 验证手机号是否已存在
    await this.userDomainService.validatePhoneUniqueness(teacherData.phone);

    // 创建用户档案
    const profile = new UserProfile({
      name: teacherData.name,
      phone: teacherData.phone,
      gender: teacherData.gender,
      avatar: teacherData.avatar,
      age: teacherData.age,
      birthday: teacherData.birthday,
      cardNo: teacherData.cardNo,
      cardType: teacherData.cardType,
    });

    // 创建教师用户
    const teacher = new TeacherUser(profile, teacherData.carNo);

    // 保存到数据库
    const auditContext = {
      userId: currentUserId,
      action: AuditAction.CREATE,
      metadata: { userType: 'teacher' },
    };

    return (await this.userRepository.save(
      teacher,
      auditContext,
    )) as TeacherUser;
  }

  /**
   * 更新教师信息
   */
  async updateTeacher(
    teacherId: string,
    updateData: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      carNo?: string;
    },
    currentUserId?: string,
  ): Promise<TeacherUser> {
    const teacher = await this.userRepository.findById(teacherId);
    if (!teacher || !(teacher instanceof TeacherUser)) {
      throw new EntityNotFoundException('Teacher', teacherId);
    }

    // 如果更新了基本信息，需要验证和更新档案
    if (
      updateData.name ||
      updateData.phone ||
      updateData.gender ||
      updateData.avatar ||
      updateData.age ||
      updateData.birthday ||
      updateData.cardNo ||
      updateData.cardType
    ) {
      const currentProfile = teacher.getProfile();
      if (!currentProfile) {
        throw new BusinessRuleException('教师档案数据不完整');
      }

      const newProfile = new UserProfile({
        name: updateData.name || currentProfile.name,
        phone: updateData.phone || currentProfile.phone,
        gender:
          updateData.gender !== undefined
            ? updateData.gender
            : currentProfile.gender,
        avatar: updateData.avatar || currentProfile.avatar,
        age: updateData.age !== undefined ? updateData.age : currentProfile.age,
        birthday: updateData.birthday || currentProfile.birthday,
        cardNo: updateData.cardNo || currentProfile.cardNo,
        cardType: updateData.cardType || currentProfile.cardType,
      });

      // 验证更新
      await this.userDomainService.validateProfileUpdate(teacherId, newProfile);
      teacher.updateProfile(newProfile);
    }

    // 更新车牌号
    if (updateData.carNo !== undefined) {
      teacher.updateCarNo(updateData.carNo);
    }

    const auditContext = {
      userId: currentUserId,
      action: AuditAction.UPDATE,
      metadata: { userType: 'teacher', changes: updateData },
    };

    return (await this.userRepository.save(
      teacher,
      auditContext,
    )) as TeacherUser;
  }

  /**
   * 删除教师
   */
  async deleteTeacher(
    teacherId: string,
    currentUserId?: string,
  ): Promise<void> {
    const teacher = await this.userRepository.findById(teacherId);
    if (!teacher || !(teacher instanceof TeacherUser)) {
      throw new EntityNotFoundException('Teacher', teacherId);
    }

    const auditContext = {
      userId: currentUserId,
      action: AuditAction.DELETE,
      metadata: { userType: 'teacher' },
    };

    await this.userRepository.softDelete(teacherId, auditContext);
  }

  /**
   * 获取教师详情
   */
  async getTeacher(teacherId: string): Promise<TeacherUser> {
    const teacher = await this.userRepository.findById(teacherId);
    if (!teacher || !(teacher instanceof TeacherUser)) {
      throw new EntityNotFoundException('Teacher', teacherId);
    }

    return teacher;
  }

  /**
   * 获取教师列表
   */
  async getTeachers(params: {
    page?: number;
    pageSize?: number;
    name?: string;
    phone?: string;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<PaginatedResponseDto<TeacherUser>> {
    const {
      page = 1,
      pageSize = 10,
      name,
      phone,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = params;

    const criteria = {
      userTypes: ['teacher'],
      names: name ? [name] : undefined,
      phones: phone ? [phone] : undefined,
      isActive,
      limit: pageSize,
      offset: (page - 1) * pageSize,
      sortBy,
      sortOrder,
    };

    // 临时实现：使用现有方法
    const allTeachers = await this.userRepository.findByUserType(
      UserTypeEnum.TEACHER,
    );
    const teachers = allTeachers.slice((page - 1) * pageSize, page * pageSize);
    const total = allTeachers.length;

    return new PaginatedResponseDto(
      teachers.filter((user) => user) as TeacherUser[],
      total,
      page,
      pageSize,
    );
  }

  /**
   * 搜索教师
   */
  async searchTeachers(
    keyword: string,
    limit: number = 20,
  ): Promise<TeacherUser[]> {
    // 临时实现：使用现有方法进行简单搜索
    const allTeachers = await this.userRepository.findByUserType(
      UserTypeEnum.TEACHER,
    );
    const teachers = allTeachers
      .filter((teacher) => {
        const profile = teacher.getProfile();
        return (
          profile?.name?.includes(keyword) || profile?.phone?.includes(keyword)
        );
      })
      .slice(0, limit);
    return teachers.filter((user) => user) as TeacherUser[];
  }

  /**
   * 激活/停用教师
   */
  async toggleTeacherStatus(
    teacherId: string,
    currentUserId?: string,
  ): Promise<TeacherUser> {
    const teacher = await this.userRepository.findById(teacherId);
    if (!teacher || !(teacher instanceof TeacherUser)) {
      throw new EntityNotFoundException('Teacher', teacherId);
    }

    const wasActive = teacher.isUserActive();
    if (wasActive) {
      teacher.deactivate();
    } else {
      teacher.activate();
    }

    const auditContext = {
      userId: currentUserId,
      action: AuditAction.UPDATE,
      metadata: {
        userType: 'teacher',
        statusChange: wasActive ? 'deactivated' : 'activated',
      },
    };

    return (await this.userRepository.save(
      teacher,
      auditContext,
    )) as TeacherUser;
  }

  /**
   * 批量删除教师
   */
  async batchDeleteTeachers(teacherIds: string[]): Promise<void> {
    for (const teacherId of teacherIds) {
      await this.deleteTeacher(teacherId);
    }
  }

  /**
   * 获取教师统计信息
   */
  async getTeacherStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    recentRegistrations: number;
  }> {
    // 临时实现：手动统计
    const allTeachers = await this.userRepository.findByUserType(
      UserTypeEnum.TEACHER,
    );
    const activeTeachers = allTeachers.filter((teacher) =>
      teacher.isUserActive(),
    );
    const stats = {
      totalUsers: allTeachers.length,
      activeUsers: activeTeachers.length,
      recentRegistrations: 0, // 暂时设为0
    };

    // 这里需要根据实际需求过滤教师相关的统计
    // 暂时返回基础统计，后续可以扩展
    return {
      total: stats.totalUsers,
      active: stats.activeUsers,
      inactive: stats.totalUsers - stats.activeUsers,
      recentRegistrations: stats.recentRegistrations,
    };
  }
}

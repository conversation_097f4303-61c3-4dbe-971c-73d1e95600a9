import {
  Controller,
  Post,
  Get,
  Body,
  Headers,
  HttpStatus,
  UseInterceptors,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiHeader,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FastifyRequest } from 'fastify';
import { AuthApplicationService } from '../../application/services/auth-application.service';
import {
  LoginRequestDto,
  RefreshTokenRequestDto,
  CheckPermissionRequestDto,
  CheckRoleRequestDto,
  LogoutRequestDto,
} from '../../application/dto/auth.dto';
import { ResponseInterceptor } from '@/shared/infrastructure/interceptors/response.interceptor';

/**
 * 认证控制器
 */
@ApiTags('认证管理')
@Controller('auth')
@UseInterceptors(ResponseInterceptor)
export class AuthController {
  constructor(
    private readonly authApplicationService: AuthApplicationService,
  ) {}

  /**
   * 用户登录
   */
  @Post('login')
  @ApiOperation({
    summary: '用户登录',
    description: '使用手机号和密码进行用户登录，返回JWT令牌',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '登录成功',
    schema: {
      type: 'object',
      properties: {
        access_token: { type: 'string', description: '访问令牌' },
        refresh_token: { type: 'string', description: '刷新令牌' },
        expires_in: { type: 'number', description: '令牌过期时间（秒）' },
        token_type: {
          type: 'string',
          description: '令牌类型',
          example: 'Bearer',
        },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', description: '用户ID' },
            name: { type: 'string', description: '用户姓名' },
            phone: { type: 'string', description: '手机号' },
            userType: { type: 'string', description: '用户类型' },
            avatar: { type: 'string', description: '头像URL' },
            roles: {
              type: 'array',
              items: { type: 'string' },
              description: '用户角色',
            },
            permissions: {
              type: 'array',
              items: { type: 'string' },
              description: '用户权限',
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '登录失败，用户名或密码错误',
  })
  async login(
    @Body() loginDto: LoginRequestDto,
    @Req() request: FastifyRequest,
  ) {
    const ipAddress = request.ip;
    const userAgent = request.headers['user-agent'];

    return await this.authApplicationService.login(
      loginDto,
      ipAddress,
      userAgent,
    );
  }

  /**
   * 刷新访问令牌
   */
  @Post('refresh')
  @ApiOperation({
    summary: '刷新访问令牌',
    description: '使用刷新令牌获取新的访问令牌',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '令牌刷新成功',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '刷新令牌无效或已过期',
  })
  async refreshToken(@Body() refreshDto: RefreshTokenRequestDto) {
    return await this.authApplicationService.refreshToken(refreshDto);
  }

  /**
   * 验证访问令牌
   */
  @Post('validate')
  @ApiOperation({
    summary: '验证访问令牌',
    description: '验证访问令牌的有效性',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Bearer token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '令牌验证结果',
    schema: {
      type: 'object',
      properties: {
        valid: { type: 'boolean', description: '令牌是否有效' },
        user: {
          type: 'object',
          properties: {
            id: { type: 'string', description: '用户ID' },
            name: { type: 'string', description: '用户姓名' },
            userType: { type: 'string', description: '用户类型' },
            roles: {
              type: 'array',
              items: { type: 'string' },
              description: '用户角色',
            },
            permissions: {
              type: 'array',
              items: { type: 'string' },
              description: '用户权限',
            },
          },
        },
        expires_in: { type: 'number', description: '令牌剩余有效时间（秒）' },
      },
    },
  })
  async validateToken(@Headers('authorization') authHeader: string) {
    if (!authHeader) {
      throw new UnauthorizedException('缺少授权头');
    }

    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      throw new UnauthorizedException('无效的授权头格式');
    }

    return await this.authApplicationService.validateToken(token);
  }

  /**
   * 获取当前用户信息
   */
  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '获取当前用户信息',
    description: '根据访问令牌获取当前登录用户的详细信息',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Bearer token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取用户信息成功',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权或令牌无效',
  })
  async getCurrentUser(@Headers('authorization') authHeader: string) {
    if (!authHeader) {
      throw new UnauthorizedException('缺少授权头');
    }

    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      throw new UnauthorizedException('无效的授权头格式');
    }

    return await this.authApplicationService.getCurrentUser(token);
  }

  /**
   * 用户登出
   */
  @Post('logout')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '用户登出',
    description: '撤销访问令牌和刷新令牌，用户登出',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Bearer token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '登出成功',
  })
  async logout(
    @Headers('authorization') authHeader: string,
    @Body() body?: LogoutRequestDto,
  ) {
    if (!authHeader) {
      throw new UnauthorizedException('缺少授权头');
    }

    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      throw new UnauthorizedException('无效的授权头格式');
    }

    await this.authApplicationService.logout(token, body?.refresh_token);

    return { message: '登出成功' };
  }

  /**
   * 检查用户权限
   */
  @Post('check-permission')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '检查用户权限',
    description: '检查当前用户是否具有指定权限',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Bearer token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '权限检查结果',
    schema: {
      type: 'object',
      properties: {
        hasPermission: { type: 'boolean', description: '是否具有权限' },
      },
    },
  })
  async checkPermission(
    @Headers('authorization') authHeader: string,
    @Body() body: CheckPermissionRequestDto,
  ) {
    if (!authHeader) {
      throw new UnauthorizedException('缺少授权头');
    }

    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      throw new UnauthorizedException('无效的授权头格式');
    }

    const hasPermission = await this.authApplicationService.checkPermission(
      token,
      body.permission,
    );

    return { hasPermission };
  }

  /**
   * 检查用户角色
   */
  @Post('check-role')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '检查用户角色',
    description: '检查当前用户是否具有指定角色',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'Bearer token',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色检查结果',
    schema: {
      type: 'object',
      properties: {
        hasRole: { type: 'boolean', description: '是否具有角色' },
      },
    },
  })
  async checkRole(
    @Headers('authorization') authHeader: string,
    @Body() body: CheckRoleRequestDto,
  ) {
    if (!authHeader) {
      throw new UnauthorizedException('缺少授权头');
    }

    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      throw new UnauthorizedException('无效的授权头格式');
    }

    const hasRole = await this.authApplicationService.checkRole(
      token,
      body.role,
    );

    return { hasRole };
  }

  /**
   * 从请求头中提取令牌
   */
  private extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }
}

import { AggregateRoot } from '../base/aggregate-root';

/**
 * 审计操作类型
 */
export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  RESTORE = 'RESTORE',
}

/**
 * 审计上下文接口
 */
export interface AuditContext {
  userId?: string;
  action: AuditAction;
  metadata?: Record<string, any>;
}

/**
 * 基础仓储接口
 */
export interface BaseRepository<T extends AggregateRoot> {
  /**
   * 根据ID查找聚合
   */
  findById(id: string): Promise<T | null>;

  /**
   * 保存聚合
   */
  save(aggregate: T, auditContext?: AuditContext): Promise<T>;

  /**
   * 删除聚合
   */
  delete(id: string, auditContext?: AuditContext): Promise<void>;

  /**
   * 软删除聚合
   */
  softDelete(id: string, auditContext?: AuditContext): Promise<void>;

  /**
   * 恢复删除的聚合
   */
  restore(id: string, auditContext?: AuditContext): Promise<void>;

  /**
   * 检查聚合是否存在
   */
  exists(id: string): Promise<boolean>;
}

/**
 * 查询仓储接口
 * 用于复杂查询操作
 */
export interface QueryRepository<T> {
  /**
   * 分页查询
   */
  findWithPagination(
    criteria: any,
    page: number,
    pageSize: number,
  ): Promise<{
    data: T[];
    total: number;
    page: number;
    pageSize: number;
  }>;

  /**
   * 根据条件查找
   */
  findByCriteria(criteria: any): Promise<T[]>;

  /**
   * 计数
   */
  count(criteria: any): Promise<number>;
}

/**
 * 工作单元接口
 */
export interface UnitOfWork {
  /**
   * 开始事务
   */
  begin(): Promise<void>;

  /**
   * 提交事务
   */
  commit(): Promise<void>;

  /**
   * 回滚事务
   */
  rollback(): Promise<void>;

  /**
   * 注册新增
   */
  registerNew<T extends AggregateRoot>(aggregate: T): void;

  /**
   * 注册修改
   */
  registerDirty<T extends AggregateRoot>(aggregate: T): void;

  /**
   * 注册删除
   */
  registerDeleted<T extends AggregateRoot>(aggregate: T): void;
}

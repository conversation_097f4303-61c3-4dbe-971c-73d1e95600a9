import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * 基础异常响应接口
 */
export interface BaseExceptionResponse {
  errCode: number;
  message: string;
  data: any;
  timestamp: string;
  path?: string;
  method?: string;
  stack?: string;
}

/**
 * 基础异常类
 * 所有自定义异常的基类
 */
export abstract class BaseException extends HttpException {
  public readonly errorCode: string;
  public readonly details: any;
  public readonly timestamp: string;

  constructor(
    message: string,
    errorCode: string,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    details?: any,
  ) {
    const response: BaseExceptionResponse = {
      errCode: statusCode,
      message,
      data: details || null,
      timestamp: new Date().toISOString(),
    };

    super(response, statusCode);

    this.errorCode = errorCode;
    this.details = details;
    this.timestamp = response.timestamp;
    this.name = this.constructor.name;

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * 获取异常响应
   */
  getResponse(): BaseExceptionResponse {
    return super.getResponse() as BaseExceptionResponse;
  }

  /**
   * 转换为JSON
   */
  toJSON(): BaseExceptionResponse {
    return {
      errCode: this.getStatus(),
      message: this.message,
      data: this.details,
      timestamp: this.timestamp,
    };
  }

  /**
   * 是否为客户端错误
   */
  isClientError(): boolean {
    const status = this.getStatus();
    return status >= 400 && status < 500;
  }

  /**
   * 是否为服务器错误
   */
  isServerError(): boolean {
    const status = this.getStatus();
    return status >= 500;
  }

  /**
   * 获取错误级别
   */
  getLogLevel(): 'error' | 'warn' | 'info' {
    if (this.isServerError()) return 'error';
    if (this.isClientError()) return 'warn';
    return 'info';
  }
}

/**
 * 业务异常类
 * 用于业务逻辑相关的异常
 */
export class BusinessException extends BaseException {
  constructor(
    message: string,
    errorCode: string = 'BUSINESS_ERROR',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.UNPROCESSABLE_ENTITY, details);
  }
}

/**
 * 验证异常类
 * 用于数据验证相关的异常
 */
export class ValidationException extends BaseException {
  constructor(
    message: string,
    validationErrors?: Record<string, string[]>,
    errorCode: string = 'VALIDATION_ERROR',
  ) {
    super(message, errorCode, HttpStatus.BAD_REQUEST, validationErrors);
  }
}

/**
 * 认证异常类
 * 用于身份认证相关的异常
 */
export class AuthenticationException extends BaseException {
  constructor(
    message: string = '认证失败',
    errorCode: string = 'AUTHENTICATION_ERROR',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.UNAUTHORIZED, details);
  }
}

/**
 * 授权异常类
 * 用于权限授权相关的异常
 */
export class AuthorizationException extends BaseException {
  constructor(
    message: string = '权限不足',
    errorCode: string = 'AUTHORIZATION_ERROR',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.FORBIDDEN, details);
  }
}

/**
 * 资源不存在异常类
 */
export class ResourceNotFoundException extends BaseException {
  constructor(
    resource: string,
    identifier?: string,
    errorCode: string = 'RESOURCE_NOT_FOUND',
  ) {
    const message = identifier
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`;

    super(message, errorCode, HttpStatus.NOT_FOUND, { resource, identifier });
  }
}

/**
 * 资源冲突异常类
 */
export class ResourceConflictException extends BaseException {
  constructor(
    message: string,
    errorCode: string = 'RESOURCE_CONFLICT',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.CONFLICT, details);
  }
}

/**
 * 外部服务异常类
 */
export class ExternalServiceException extends BaseException {
  constructor(
    serviceName: string,
    message: string,
    errorCode: string = 'EXTERNAL_SERVICE_ERROR',
    details?: any,
  ) {
    super(
      `External service '${serviceName}' error: ${message}`,
      errorCode,
      HttpStatus.BAD_GATEWAY,
      { serviceName, ...details },
    );
  }
}

/**
 * 限流异常类
 */
export class RateLimitException extends BaseException {
  constructor(
    message: string = '请求过于频繁，请稍后再试',
    errorCode: string = 'RATE_LIMIT_EXCEEDED',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.TOO_MANY_REQUESTS, details);
  }
}

/**
 * 系统维护异常类
 */
export class MaintenanceException extends BaseException {
  constructor(
    message: string = '系统正在维护中，请稍后再试',
    errorCode: string = 'SYSTEM_MAINTENANCE',
    details?: any,
  ) {
    super(message, errorCode, HttpStatus.SERVICE_UNAVAILABLE, details);
  }
}

/**
 * 异常工厂类
 * 提供创建各种异常的便捷方法
 */
export class ExceptionFactory {
  /**
   * 创建业务异常
   */
  static business(message: string, details?: any): BusinessException {
    return new BusinessException(message, 'BUSINESS_ERROR', details);
  }

  /**
   * 创建验证异常
   */
  static validation(message: string, errors?: Record<string, string[]>): ValidationException {
    return new ValidationException(message, errors);
  }

  /**
   * 创建认证异常
   */
  static authentication(message?: string): AuthenticationException {
    return new AuthenticationException(message);
  }

  /**
   * 创建授权异常
   */
  static authorization(message?: string): AuthorizationException {
    return new AuthorizationException(message);
  }

  /**
   * 创建资源不存在异常
   */
  static notFound(resource: string, identifier?: string): ResourceNotFoundException {
    return new ResourceNotFoundException(resource, identifier);
  }

  /**
   * 创建资源冲突异常
   */
  static conflict(message: string, details?: any): ResourceConflictException {
    return new ResourceConflictException(message, 'RESOURCE_CONFLICT', details);
  }

  /**
   * 创建外部服务异常
   */
  static externalService(serviceName: string, message: string, details?: any): ExternalServiceException {
    return new ExternalServiceException(serviceName, message, 'EXTERNAL_SERVICE_ERROR', details);
  }

  /**
   * 创建限流异常
   */
  static rateLimit(message?: string): RateLimitException {
    return new RateLimitException(message);
  }

  /**
   * 创建系统维护异常
   */
  static maintenance(message?: string): MaintenanceException {
    return new MaintenanceException(message);
  }
}

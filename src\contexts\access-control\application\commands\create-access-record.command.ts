import { BaseCommand } from '@/shared/application/cqrs/command';
import { AccessType } from '../../domain/aggregates/access-record.aggregate';

/**
 * 创建门禁记录命令
 */
export class CreateAccessRecordCommand extends BaseCommand {
  constructor(
    public readonly userId: string,
    public readonly accessType: AccessType,
    public readonly accessTime: Date,
    public readonly locationName: string,
    public readonly locationDescription?: string,
    public readonly locationBuilding?: string,
    public readonly locationFloor?: string,
    public readonly deviceId?: string,
    public readonly verificationMethod?: string,
    public readonly confidenceScore?: number,
    public readonly temperature?: number,
  ) {
    super();
  }
}

/**
 * 更新门禁记录验证信息命令
 */
export class UpdateAccessRecordVerificationCommand extends BaseCommand {
  constructor(
    public readonly recordId: string,
    public readonly verificationMethod: string,
    public readonly confidenceScore?: number,
    public readonly temperature?: number,
  ) {
    super();
  }
}

/**
 * 标记门禁记录为异常命令
 */
export class MarkAccessRecordAbnormalCommand extends BaseCommand {
  constructor(
    public readonly recordId: string,
    public readonly reason: string,
  ) {
    super();
  }
}

/**
 * 标记门禁记录为可疑命令
 */
export class MarkAccessRecordSuspiciousCommand extends BaseCommand {
  constructor(
    public readonly recordId: string,
    public readonly reason: string,
  ) {
    super();
  }
}

/**
 * 批量创建门禁记录命令
 */
export class BatchCreateAccessRecordsCommand extends BaseCommand {
  constructor(
    public readonly records: Array<{
      userId: string;
      accessType: AccessType;
      accessTime: Date;
      locationName: string;
      locationDescription?: string;
      locationBuilding?: string;
      locationFloor?: string;
      deviceId?: string;
      verificationMethod?: string;
      confidenceScore?: number;
      temperature?: number;
    }>,
  ) {
    super();
  }
}

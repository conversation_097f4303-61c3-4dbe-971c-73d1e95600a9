/**
 * 缓存装饰器
 * 重新导出缓存拦截器中的装饰器，提供统一的导入入口
 */

export {
  Cache,
  CacheByFirstParam,
  CacheByParams,
  DynamicCache,
  ClearCache,
  UserCache,
  TenantCache,
  StatisticsCache,
  CacheOptions,
} from '../cache/cache.interceptor';

// 导出缓存相关的元数据键
export {
  CACHE_KEY_METADATA,
  CACHE_TTL_METADATA,
  CACHE_PARAMS_METADATA,
  CACHE_KEY_GENERATOR_METADATA,
  CLEAR_CACHE_KEYS_METADATA,
} from '../cache/cache.interceptor';

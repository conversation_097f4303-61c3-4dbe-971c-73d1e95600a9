import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { VisitorApplicationService } from '../../application/services/visitor-application.service';
import { VisitorUser } from '../../domain/aggregates/user.aggregate';
import { ResponseInterceptor } from '@/shared/infrastructure/interceptors/response.interceptor';
import { GlobalExceptionFilter } from '@/shared/infrastructure/filters/global-exception.filter';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';

/**
 * 访客管理控制器
 */
@ApiTags('访客管理')
@Controller('visitors')
@UseInterceptors(ResponseInterceptor)
export class VisitorController {
  constructor(
    private readonly visitorApplicationService: VisitorApplicationService,
  ) {}

  /**
   * 创建访客
   */
  @Post()
  @ApiOperation({
    summary: '创建访客',
    description: '创建新的访客用户',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '访客创建成功',
    type: VisitorUser,
  })
  async createVisitor(
    @Body()
    createVisitorDto: {
      name: string;
      phone: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      visitPurpose?: string;
      visitStartTime?: Date;
      visitEndTime?: Date;
      hostName?: string;
      hostPhone?: string;
      company?: string;
    },
  ): Promise<VisitorUser> {
    return await this.visitorApplicationService.createVisitor(createVisitorDto);
  }

  /**
   * 获取访客列表
   */
  @Get()
  @ApiOperation({
    summary: '获取访客列表',
    description: '分页获取访客列表，支持筛选和排序',
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({ name: 'pageSize', required: false, description: '每页数量', example: 10 })
  @ApiQuery({ name: 'name', required: false, description: '访客姓名' })
  @ApiQuery({ name: 'phone', required: false, description: '手机号' })
  @ApiQuery({ name: 'isActive', required: false, description: '是否激活' })
  @ApiQuery({ name: 'isApproved', required: false, description: '是否已审批' })
  @ApiQuery({ name: 'company', required: false, description: '所属公司' })
  @ApiQuery({ name: 'sortBy', required: false, description: '排序字段', example: 'createdAt' })
  @ApiQuery({ name: 'sortOrder', required: false, description: '排序方向', enum: ['ASC', 'DESC'] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取访客列表成功',
    type: PaginatedResponseDto<VisitorUser>,
  })
  async getVisitors(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('name') name?: string,
    @Query('phone') phone?: string,
    @Query('isActive') isActive?: boolean,
    @Query('isApproved') isApproved?: boolean,
    @Query('company') company?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResponseDto<VisitorUser>> {
    return await this.visitorApplicationService.getVisitors({
      page,
      pageSize,
      name,
      phone,
      isActive,
      isApproved,
      company,
      sortBy,
      sortOrder,
    });
  }

  /**
   * 获取待审批访客列表
   */
  @Get('pending')
  @ApiOperation({
    summary: '获取待审批访客列表',
    description: '获取所有待审批的访客',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取待审批访客列表成功',
    type: [VisitorUser],
  })
  async getPendingVisitors(): Promise<VisitorUser[]> {
    return await this.visitorApplicationService.getPendingVisitors();
  }

  /**
   * 获取当前有效访客列表
   */
  @Get('valid')
  @ApiOperation({
    summary: '获取当前有效访客列表',
    description: '获取当前时间段内有效的访客',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取有效访客列表成功',
    type: [VisitorUser],
  })
  async getValidVisitors(): Promise<VisitorUser[]> {
    return await this.visitorApplicationService.getValidVisitors();
  }

  /**
   * 搜索访客
   */
  @Get('search')
  @ApiOperation({
    summary: '搜索访客',
    description: '根据关键词搜索访客',
  })
  @ApiQuery({ name: 'keyword', required: true, description: '搜索关键词' })
  @ApiQuery({ name: 'limit', required: false, description: '返回数量限制', example: 20 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索访客成功',
    type: [VisitorUser],
  })
  async searchVisitors(
    @Query('keyword') keyword: string,
    @Query('limit') limit?: number,
  ): Promise<VisitorUser[]> {
    return await this.visitorApplicationService.searchVisitors(keyword, limit);
  }

  /**
   * 获取访客详情
   */
  @Get(':id')
  @ApiOperation({
    summary: '获取访客详情',
    description: '根据ID获取访客详细信息',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取访客详情成功',
    type: VisitorUser,
  })
  async getVisitor(@Param('id') id: string): Promise<VisitorUser> {
    return await this.visitorApplicationService.getVisitor(id);
  }

  /**
   * 更新访客信息
   */
  @Put(':id')
  @ApiOperation({
    summary: '更新访客信息',
    description: '更新指定访客的信息',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '访客信息更新成功',
    type: VisitorUser,
  })
  async updateVisitor(
    @Param('id') id: string,
    @Body()
    updateVisitorDto: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      visitPurpose?: string;
      visitStartTime?: Date;
      visitEndTime?: Date;
      hostName?: string;
      hostPhone?: string;
      company?: string;
    },
  ): Promise<VisitorUser> {
    return await this.visitorApplicationService.updateVisitor(id, updateVisitorDto);
  }

  /**
   * 审批访客
   */
  @Put(':id/approve')
  @ApiOperation({
    summary: '审批访客',
    description: '审批通过访客申请',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '访客审批成功',
    type: VisitorUser,
  })
  async approveVisitor(@Param('id') id: string): Promise<VisitorUser> {
    return await this.visitorApplicationService.approveVisitor(id);
  }

  /**
   * 拒绝访客
   */
  @Put(':id/reject')
  @ApiOperation({
    summary: '拒绝访客',
    description: '拒绝访客申请',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '访客拒绝成功',
    type: VisitorUser,
  })
  async rejectVisitor(@Param('id') id: string): Promise<VisitorUser> {
    return await this.visitorApplicationService.rejectVisitor(id);
  }

  /**
   * 批量审批访客
   */
  @Put('batch-approve')
  @ApiOperation({
    summary: '批量审批访客',
    description: '批量审批多个访客申请',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量审批访客成功',
    type: [VisitorUser],
  })
  async batchApproveVisitors(
    @Body() approveDto: { visitorIds: string[] },
  ): Promise<VisitorUser[]> {
    return await this.visitorApplicationService.batchApproveVisitors(approveDto.visitorIds);
  }

  /**
   * 切换访客状态
   */
  @Put(':id/toggle-status')
  @ApiOperation({
    summary: '切换访客状态',
    description: '激活或停用访客账户',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '访客状态切换成功',
    type: VisitorUser,
  })
  async toggleVisitorStatus(@Param('id') id: string): Promise<VisitorUser> {
    return await this.visitorApplicationService.toggleVisitorStatus(id);
  }

  /**
   * 删除访客
   */
  @Delete(':id')
  @ApiOperation({
    summary: '删除访客',
    description: '删除指定的访客',
  })
  @ApiParam({ name: 'id', description: '访客ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '访客删除成功',
  })
  async deleteVisitor(@Param('id') id: string): Promise<void> {
    return await this.visitorApplicationService.deleteVisitor(id);
  }

  /**
   * 批量删除访客
   */
  @Delete('batch')
  @ApiOperation({
    summary: '批量删除访客',
    description: '批量删除多个访客',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量删除访客成功',
  })
  async batchDeleteVisitors(
    @Body() deleteDto: { visitorIds: string[] },
  ): Promise<void> {
    return await this.visitorApplicationService.batchDeleteVisitors(deleteDto.visitorIds);
  }
}

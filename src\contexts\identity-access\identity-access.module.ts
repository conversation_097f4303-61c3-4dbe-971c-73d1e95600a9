import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

// 聚合根
import {
  User,
  StudentUser,
  TeacherUser,
  ParentUser,
  VisitorUser,
} from './domain/aggregates/user.aggregate';

// 仓储实现
import { TypeOrmUserRepository } from './infrastructure/repositories/typeorm-user.repository';

// JWT服务和守卫
import { JwtService } from './infrastructure/services/jwt.service';
import { JWT_SERVICE } from './domain/services/jwt.service.interface';
import { JwtAuthGuard } from './infrastructure/guards/jwt-auth.guard';
import {
  PermissionsGuard,
  RolesGuard,
} from './infrastructure/guards/permissions.guard';

// 多租户相关
import { TenantContext } from '@/shared/domain/context/tenant.context';
import { TenantInterceptor } from '@/shared/infrastructure/interceptors/tenant.interceptor';
import {
  TenantGuard,
  TenantResourceGuard,
} from '@/shared/infrastructure/guards/tenant.guard';

// 应用服务
import { StudentApplicationService } from './application/services/student-application.service';
import { TeacherApplicationService } from './application/services/teacher-application.service';
import { ParentApplicationService } from './application/services/parent-application.service';
import { VisitorApplicationService } from './application/services/visitor-application.service';
import { AuthApplicationService } from './application/services/auth-application.service';

// 命令处理器
import {
  CreateStudentHandler,
  UpdateStudentHandler,
  DeleteStudentHandler,
} from './application/handlers/student-command.handler';
import { CommandHandlerRegistry } from './application/handlers/command-handler.registry';

// 开发者相关
import { DeveloperAccountService } from './infrastructure/services/developer-account.service';
import { DataInitializationService } from './infrastructure/services/data-initialization.service';
import { LoginRestrictionService } from './infrastructure/services/login-restriction.service';
import { DeveloperController } from './interface/rest/developer.controller';
import { DevSetupController } from './interface/rest/dev-setup.controller';
import { DeveloperGuard, ApiKeyGuard, EnvironmentGuard } from './infrastructure/guards/developer.guard';

// 注意：命令和查询处理器已移除，使用CQRS总线进行处理

// 领域服务
import { UserDomainService } from './domain/services/user-domain.service';
import { AuthDomainService } from './domain/services/auth-domain.service';

// 控制器
import { UserController } from './interface/rest/user.controller';
import { TeacherController } from './interface/rest/teacher.controller';
import { ParentController } from './interface/rest/parent.controller';
import { VisitorController } from './interface/rest/visitor.controller';
import { AuthController } from './interface/rest/auth.controller';

// 共享模块
import { SharedModule } from '@/shared/shared.module';

// 仓储接口令牌
import {
  UserRepository,
  UserQueryRepository,
} from './domain/repositories/user.repository';

// 依赖注入令牌
import { USER_REPOSITORY, USER_QUERY_REPOSITORY } from './domain/tokens';

@Module({
  imports: [
    // TypeORM实体注册
    TypeOrmModule.forFeature([
      User,
      StudentUser,
      TeacherUser,
      ParentUser,
      VisitorUser,
    ]),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret:
          configService.get<string>('JWT_ACCESS_SECRET') || 'access-secret',
        signOptions: {
          expiresIn:
            configService.get<string>('JWT_ACCESS_EXPIRES_IN') || '24h',
        },
      }),
      inject: [ConfigService],
    }),

    // 缓存模块
    CacheModule.register({
      ttl: 300, // 5分钟
      max: 1000, // 最大缓存项数
    }),

    // 共享模块
    SharedModule,
  ],

  controllers: [
    UserController,
    TeacherController,
    ParentController,
    VisitorController,
    AuthController,
    DeveloperController,
    DevSetupController,
  ],

  providers: [
    // 仓储实现
    {
      provide: USER_REPOSITORY,
      useClass: TypeOrmUserRepository,
    },
    {
      provide: USER_QUERY_REPOSITORY,
      useClass: TypeOrmUserRepository,
    },
    TypeOrmUserRepository,

    // 领域服务
    UserDomainService,
    AuthDomainService,

    // JWT服务
    {
      provide: JWT_SERVICE,
      useClass: JwtService,
    },

    // 守卫
    JwtAuthGuard,
    PermissionsGuard,
    RolesGuard,
    TenantGuard,
    TenantResourceGuard,
    DeveloperGuard,
    ApiKeyGuard,
    EnvironmentGuard,

    // 多租户服务
    TenantContext,
    TenantInterceptor,

    // 应用服务
    StudentApplicationService,
    TeacherApplicationService,
    ParentApplicationService,
    VisitorApplicationService,
    AuthApplicationService,

    // 命令处理器
    CreateStudentHandler,
    UpdateStudentHandler,
    DeleteStudentHandler,

    // 处理器注册服务
    CommandHandlerRegistry,

    // 开发者服务
    DeveloperAccountService,
    DataInitializationService,
    LoginRestrictionService,
  ],

  exports: [
    // 导出应用服务供其他模块使用
    StudentApplicationService,
    UserDomainService,

    // 导出仓储供其他模块使用
    USER_REPOSITORY,
    USER_QUERY_REPOSITORY,
    TypeOrmUserRepository,
  ],
})
export class IdentityAccessModule {
  constructor() {
    console.log('IdentityAccessModule initialized');
  }
}

import { BaseRepository, QueryRepository } from '@/shared/domain/repositories/base-repository';
import { AccessRecord, AccessType, AccessRecordStatus } from '../aggregates/access-record.aggregate';

/**
 * 门禁记录仓储接口
 */
export interface AccessRecordRepository extends BaseRepository<AccessRecord> {
  /**
   * 根据用户ID查找门禁记录
   */
  findByUserId(userId: string): Promise<AccessRecord[]>;

  /**
   * 根据用户ID和时间范围查找门禁记录
   */
  findByUserIdAndTimeRange(
    userId: string,
    startTime: Date,
    endTime: Date
  ): Promise<AccessRecord[]>;

  /**
   * 根据设备ID查找门禁记录
   */
  findByDeviceId(deviceId: string): Promise<AccessRecord[]>;

  /**
   * 根据位置查找门禁记录
   */
  findByLocation(locationName: string, building?: string): Promise<AccessRecord[]>;

  /**
   * 查找异常门禁记录
   */
  findAbnormalRecords(limit?: number): Promise<AccessRecord[]>;

  /**
   * 查找可疑门禁记录
   */
  findSuspiciousRecords(limit?: number): Promise<AccessRecord[]>;

  /**
   * 批量保存门禁记录
   */
  saveMany(records: AccessRecord[]): Promise<AccessRecord[]>;

  /**
   * 根据用户ID获取最后一条门禁记录
   */
  findLastRecordByUserId(userId: string): Promise<AccessRecord | null>;

  /**
   * 检查用户是否在校内（最后一条记录是进入且没有对应的离开记录）
   */
  isUserInCampus(userId: string): Promise<boolean>;
}

/**
 * 门禁记录查询仓储接口
 */
export interface AccessRecordQueryRepository extends QueryRepository<AccessRecord> {
  /**
   * 根据条件搜索门禁记录
   */
  searchRecords(criteria: {
    userId?: string;
    accessType?: AccessType;
    status?: AccessRecordStatus;
    locationName?: string;
    building?: string;
    deviceId?: string;
    startTime?: Date;
    endTime?: Date;
    verificationMethod?: string;
    minConfidenceScore?: number;
    maxTemperature?: number;
    minTemperature?: number;
  }): Promise<AccessRecord[]>;

  /**
   * 获取门禁统计信息
   */
  getAccessStatistics(
    startTime: Date,
    endTime: Date
  ): Promise<{
    totalRecords: number;
    enterRecords: number;
    leaveRecords: number;
    abnormalRecords: number;
    suspiciousRecords: number;
    uniqueUsers: number;
    averageTemperature: number;
    deviceUsage: Array<{ deviceId: string; count: number }>;
    locationUsage: Array<{ locationName: string; count: number }>;
    hourlyDistribution: Array<{ hour: number; count: number }>;
  }>;

  /**
   * 获取用户门禁统计
   */
  getUserAccessStatistics(
    userId: string,
    startTime: Date,
    endTime: Date
  ): Promise<{
    totalRecords: number;
    enterRecords: number;
    leaveRecords: number;
    abnormalRecords: number;
    averageTemperature: number;
    mostUsedLocation: string;
    firstAccessTime: Date | null;
    lastAccessTime: Date | null;
  }>;

  /**
   * 获取实时在校人数
   */
  getCurrentCampusPopulation(): Promise<{
    totalInCampus: number;
    studentCount: number;
    teacherCount: number;
    visitorCount: number;
    locationDistribution: Array<{ locationName: string; count: number }>;
  }>;

  /**
   * 查找长时间未离开的用户
   */
  findUsersStayingTooLong(hours: number): Promise<Array<{
    userId: string;
    lastEnterTime: Date;
    location: string;
    stayDuration: number; // 小时
  }>>;

  /**
   * 查找频繁进出的用户
   */
  findFrequentAccessUsers(
    startTime: Date,
    endTime: Date,
    minAccessCount: number
  ): Promise<Array<{
    userId: string;
    accessCount: number;
    enterCount: number;
    leaveCount: number;
  }>>;

  /**
   * 获取设备健康状态
   */
  getDeviceHealthStatus(): Promise<Array<{
    deviceId: string;
    lastActiveTime: Date;
    totalRecords: number;
    abnormalRecords: number;
    averageConfidenceScore: number;
    status: 'online' | 'offline' | 'warning';
  }>>;
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DeveloperAccountService } from './developer-account.service';
import { TenantContext } from '@/shared/domain/context/tenant.context';

/**
 * 数据初始化服务
 * 用于初始化系统基础数据和测试数据
 */
@Injectable()
export class DataInitializationService {
  private readonly logger = new Logger(DataInitializationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly developerAccountService: DeveloperAccountService,
  ) {}

  /**
   * 初始化所有基础数据
   */
  async initializeAllData(): Promise<{
    developer: boolean;
    tenants: boolean;
    permissions: boolean;
    testData: boolean;
  }> {
    this.logger.log('Starting data initialization...');

    const results = {
      developer: false,
      tenants: false,
      permissions: false,
      testData: false,
    };

    try {
      // 1. 初始化开发者账号
      results.developer = await this.initializeDeveloperAccount();

      // 2. 初始化租户数据
      results.tenants = await this.initializeTenants();

      // 3. 初始化权限数据
      results.permissions = await this.initializePermissions();

      // 4. 初始化测试数据（仅开发环境）
      if (this.isDevelopmentEnvironment()) {
        results.testData = await this.initializeTestData();
      }

      this.logger.log('Data initialization completed', results);
      return results;
    } catch (error) {
      this.logger.error('Data initialization failed', error);
      throw error;
    }
  }

  /**
   * 初始化开发者账号
   */
  async initializeDeveloperAccount(): Promise<boolean> {
    try {
      await this.developerAccountService.ensureDeveloperAccountExists();
      this.logger.log('Developer account initialized');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize developer account', error);
      return false;
    }
  }

  /**
   * 初始化租户数据
   */
  async initializeTenants(): Promise<boolean> {
    try {
      // 确保系统租户存在
      const systemTenantId = TenantContext.getDefaultTenantId();
      this.logger.log(`System tenant initialized: ${systemTenantId}`);

      // 这里可以添加其他默认租户的创建逻辑
      // 例如：演示租户、测试租户等

      return true;
    } catch (error) {
      this.logger.error('Failed to initialize tenants', error);
      return false;
    }
  }

  /**
   * 初始化权限数据
   */
  async initializePermissions(): Promise<boolean> {
    try {
      // 这里可以添加默认权限和角色的创建逻辑
      const defaultPermissions = this.getDefaultPermissions();
      const defaultRoles = this.getDefaultRoles();

      this.logger.log('Permissions and roles initialized', {
        permissions: defaultPermissions.length,
        roles: defaultRoles.length,
      });

      return true;
    } catch (error) {
      this.logger.error('Failed to initialize permissions', error);
      return false;
    }
  }

  /**
   * 初始化测试数据
   */
  async initializeTestData(): Promise<boolean> {
    if (!this.isDevelopmentEnvironment()) {
      this.logger.warn('Test data initialization skipped - not in development environment');
      return false;
    }

    try {
      // 创建测试租户
      await this.createTestTenant();

      // 创建测试用户
      await this.createTestUsers();

      // 创建测试班级和年级
      await this.createTestClassesAndGrades();

      this.logger.log('Test data initialized');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize test data', error);
      return false;
    }
  }

  /**
   * 创建测试租户
   */
  private async createTestTenant(): Promise<void> {
    // 这里可以添加创建测试租户的逻辑
    this.logger.debug('Test tenant creation logic would go here');
  }

  /**
   * 创建测试用户
   */
  private async createTestUsers(): Promise<void> {
    // 这里可以添加创建测试用户的逻辑
    const testUsers = [
      {
        name: '测试学生',
        phone: '13800000001',
        userType: 'student',
      },
      {
        name: '测试教师',
        phone: '13800000002',
        userType: 'teacher',
      },
      {
        name: '测试家长',
        phone: '13800000003',
        userType: 'parent',
      },
    ];

    this.logger.debug('Test users creation logic would go here', {
      count: testUsers.length,
    });
  }

  /**
   * 创建测试班级和年级
   */
  private async createTestClassesAndGrades(): Promise<void> {
    // 这里可以添加创建测试班级和年级的逻辑
    const testGrades = ['一年级', '二年级', '三年级'];
    const testClasses = ['1班', '2班', '3班'];

    this.logger.debug('Test classes and grades creation logic would go here', {
      grades: testGrades.length,
      classes: testClasses.length,
    });
  }

  /**
   * 获取默认权限列表
   */
  private getDefaultPermissions(): string[] {
    return [
      // 用户管理权限
      'user:create',
      'user:read',
      'user:update',
      'user:delete',

      // 学生管理权限
      'student:create',
      'student:read',
      'student:update',
      'student:delete',

      // 教师管理权限
      'teacher:create',
      'teacher:read',
      'teacher:update',
      'teacher:delete',

      // 家长管理权限
      'parent:create',
      'parent:read',
      'parent:update',
      'parent:delete',

      // 开发者权限
      'developer:create',
      'developer:read',
      'developer:update',
      'developer:delete',

      // 系统管理权限
      'system:read',
      'system:update',
      'system:manage',

      // 数据初始化权限
      'data:initialize',
      'data:reset',
    ];
  }

  /**
   * 获取默认角色列表
   */
  private getDefaultRoles(): Array<{ name: string; permissions: string[] }> {
    return [
      {
        name: 'admin',
        permissions: this.getDefaultPermissions(),
      },
      {
        name: 'teacher',
        permissions: [
          'user:read',
          'student:read',
          'student:update',
          'parent:read',
        ],
      },
      {
        name: 'parent',
        permissions: [
          'user:read',
          'student:read',
        ],
      },
      {
        name: 'developer',
        permissions: this.getDefaultPermissions(),
      },
    ];
  }

  /**
   * 检查是否为开发环境
   */
  private isDevelopmentEnvironment(): boolean {
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    return nodeEnv === 'development' || nodeEnv === 'staging';
  }

  /**
   * 重置所有数据（危险操作，仅开发环境）
   */
  async resetAllData(): Promise<boolean> {
    if (!this.isDevelopmentEnvironment()) {
      throw new Error('Data reset is only allowed in development environment');
    }

    this.logger.warn('Starting data reset - this will delete all data!');

    try {
      // 这里可以添加数据重置逻辑
      // 注意：这是危险操作，需要谨慎处理

      this.logger.warn('Data reset completed');
      return true;
    } catch (error) {
      this.logger.error('Data reset failed', error);
      throw error;
    }
  }

  /**
   * 获取初始化状态
   */
  async getInitializationStatus(): Promise<{
    developer: boolean;
    tenants: boolean;
    permissions: boolean;
    testData: boolean;
    environment: string;
  }> {
    return {
      developer: await this.checkDeveloperAccountExists(),
      tenants: await this.checkTenantsExist(),
      permissions: await this.checkPermissionsExist(),
      testData: await this.checkTestDataExists(),
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }

  private async checkDeveloperAccountExists(): Promise<boolean> {
    try {
      const devPhone = this.configService.get<string>('DEV_ACCOUNT_PHONE', '***********');
      const info = await this.developerAccountService.getDeveloperInfo(devPhone);
      return !!info;
    } catch {
      return false;
    }
  }

  private async checkTenantsExist(): Promise<boolean> {
    // 这里可以添加检查租户是否存在的逻辑
    return true;
  }

  private async checkPermissionsExist(): Promise<boolean> {
    // 这里可以添加检查权限是否存在的逻辑
    return true;
  }

  private async checkTestDataExists(): Promise<boolean> {
    // 这里可以添加检查测试数据是否存在的逻辑
    return this.isDevelopmentEnvironment();
  }
}

import { Injectable, LoggerService, Scope, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import pino from 'pino';
import { PinoLoggerConfig } from './pino-logger.config';

/**
 * NestJS Pino 日志服务
 * 实现 NestJS LoggerService 接口
 */
@Injectable({ scope: Scope.TRANSIENT })
export class PinoLoggerService implements LoggerService {
  private readonly logger: pino.Logger;
  private context?: string;

  constructor(
    private readonly pinoConfig: PinoLoggerConfig,
    private readonly configService?: ConfigService,
  ) {
    this.logger = this.pinoConfig.createLogger();
  }

  /**
   * 设置日志上下文
   */
  setContext(context: string): void {
    this.context = context;
  }

  /**
   * 记录普通日志
   */
  log(message: any, context?: string): void {
    const logContext = context || this.context || 'Application';

    if (typeof message === 'object') {
      this.logger.info({ context: logContext, ...message });
    } else {
      this.logger.info({ context: logContext }, message);
    }
  }

  /**
   * 记录错误日志
   */
  error(message: any, trace?: string, context?: string): void {
    const logContext = context || this.context || 'Application';

    if (typeof message === 'object') {
      this.logger.error({
        context: logContext,
        trace,
        ...message,
      });
    } else {
      this.logger.error({
        context: logContext,
        trace,
      }, message);
    }
  }

  /**
   * 记录警告日志
   */
  warn(message: any, context?: string): void;
  warn(message: string, data?: any, context?: string): void;
  warn(message: any, contextOrData?: string | any, context?: string): void {
    let logContext: string;
    let data: any;

    if (typeof contextOrData === 'string') {
      logContext = contextOrData || this.context || 'Application';
      data = undefined;
    } else {
      logContext = context || this.context || 'Application';
      data = contextOrData;
    }

    if (typeof message === 'object') {
      this.logger.warn({ context: logContext, ...message });
    } else if (data) {
      this.logger.warn({ context: logContext, ...data }, message);
    } else {
      this.logger.warn({ context: logContext }, message);
    }
  }

  /**
   * 记录调试日志
   */
  debug(message: any, context?: string): void;
  debug(message: string, data?: any, context?: string): void;
  debug(message: any, contextOrData?: string | any, context?: string): void {
    let logContext: string;
    let data: any;

    if (typeof contextOrData === 'string') {
      logContext = contextOrData || this.context || 'Application';
      data = undefined;
    } else {
      logContext = context || this.context || 'Application';
      data = contextOrData;
    }

    if (typeof message === 'object') {
      this.logger.debug({ context: logContext, ...message });
    } else if (data) {
      this.logger.debug({ context: logContext, ...data }, message);
    } else {
      this.logger.debug({ context: logContext }, message);
    }
  }

  /**
   * 记录详细日志
   */
  verbose(message: any, context?: string): void {
    const logContext = context || this.context || 'Application';

    if (typeof message === 'object') {
      this.logger.trace({ context: logContext, ...message });
    } else {
      this.logger.trace({ context: logContext }, message);
    }
  }

  /**
   * 记录致命错误日志
   */
  fatal(message: any, context?: string): void {
    const logContext = context || this.context || 'Application';

    if (typeof message === 'object') {
      this.logger.fatal({ context: logContext, ...message });
    } else {
      this.logger.fatal({ context: logContext }, message);
    }
  }

  /**
   * 记录 HTTP 请求日志
   */
  logHttpRequest(req: any, res: any, responseTime: number): void {
    const { method, url, headers, ip } = req;
    const { statusCode } = res;

    this.logger.info({
      context: 'HTTP',
      method,
      url,
      statusCode,
      responseTime: `${responseTime}ms`,
      ip,
      userAgent: headers['user-agent'],
      contentLength: res.get?.('content-length') || res.getHeader?.('content-length') || 0,
    }, `${method} ${url} ${statusCode} - ${responseTime}ms`);
  }

  /**
   * 记录数据库查询日志
   */
  logDatabaseQuery(query: string, parameters: any[], duration: number): void {
    this.logger.debug({
      context: 'Database',
      query,
      parameters,
      duration: `${duration}ms`,
    }, `Database query executed in ${duration}ms`);
  }

  /**
   * 记录业务操作日志
   */
  logBusinessOperation(
    operation: string,
    userId?: string,
    tenantId?: string,
    metadata?: Record<string, any>,
  ): void {
    this.logger.info({
      context: 'Business',
      operation,
      userId,
      tenantId,
      ...metadata,
    }, `Business operation: ${operation}`);
  }

  /**
   * 记录安全事件日志
   */
  logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: Record<string, any>,
  ): void {
    const logMethod = severity === 'critical' ? 'fatal' :
                     severity === 'high' ? 'error' :
                     severity === 'medium' ? 'warn' : 'info';

    this.logger[logMethod]({
      context: 'Security',
      event,
      severity,
      ...details,
    }, `Security event: ${event} (${severity})`);
  }

  /**
   * 记录性能指标日志
   */
  logPerformanceMetric(
    metric: string,
    value: number,
    unit: string,
    tags?: Record<string, string>,
  ): void {
    this.logger.info({
      context: 'Performance',
      metric,
      value,
      unit,
      tags,
    }, `Performance metric: ${metric} = ${value}${unit}`);
  }

  /**
   * 记录审计日志
   */
  logAudit(
    action: string,
    userId: string,
    resourceType: string,
    resourceId: string,
    changes?: Record<string, any>,
  ): void {
    this.logger.info({
      context: 'Audit',
      action,
      userId,
      resourceType,
      resourceId,
      changes,
      timestamp: new Date().toISOString(),
    }, `Audit: ${action} on ${resourceType}:${resourceId} by user:${userId}`);
  }

  /**
   * 创建子日志器
   */
  child(bindings: Record<string, any>): PinoLoggerService {
    const childLogger = new PinoLoggerService(this.pinoConfig, this.configService);
    (childLogger as any).logger = this.logger.child(bindings);
    return childLogger;
  }

  /**
   * 获取原始 Pino 日志器实例
   */
  getPinoInstance(): pino.Logger {
    return this.logger;
  }

  /**
   * 刷新日志缓冲区
   */
  flush(): void {
    this.logger.flush();
  }
}

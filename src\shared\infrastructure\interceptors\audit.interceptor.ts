import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request } from 'express';
import { BaseEntity } from '../../domain/base/base-entity';

/**
 * 审计拦截器
 * 自动填充创建者和更新者信息
 */
@Injectable()
export class AuditInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const user = (request as any).user;
    const userId = user?.id;

    return next.handle().pipe(
      tap((data) => {
        // 处理返回的数据，如果是实体对象则设置审计信息
        if (data && userId) {
          this.setAuditFields(data, userId, request.method);
        }
      }),
    );
  }

  /**
   * 设置审计字段
   */
  private setAuditFields(data: any, userId: string, method: string): void {
    if (!data) return;

    // 处理单个实体
    if (data instanceof BaseEntity) {
      this.setEntityAuditFields(data, userId, method);
      return;
    }

    // 处理数组
    if (Array.isArray(data)) {
      data.forEach((item) => {
        if (item instanceof BaseEntity) {
          this.setEntityAuditFields(item, userId, method);
        }
      });
      return;
    }

    // 处理对象中的实体属性
    if (typeof data === 'object') {
      Object.values(data).forEach((value) => {
        if (value instanceof BaseEntity) {
          this.setEntityAuditFields(value, userId, method);
        } else if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item instanceof BaseEntity) {
              this.setEntityAuditFields(item, userId, method);
            }
          });
        }
      });
    }
  }

  /**
   * 为单个实体设置审计字段
   */
  private setEntityAuditFields(
    entity: BaseEntity,
    userId: string,
    method: string,
  ): void {
    switch (method) {
      case 'POST':
        // 创建操作
        if (!entity.getCreatedBy()) {
          entity.setCreatedBy(userId);
        }
        break;
      case 'PUT':
      case 'PATCH':
        // 更新操作
        entity.setUpdatedBy(userId);
        break;
      case 'DELETE':
        // 删除操作（软删除）
        if (entity.isDeleted) {
          entity.softDelete(userId);
        }
        break;
    }
  }
}

/**
 * 审计装饰器
 * 用于标记需要审计的方法
 */
import { SetMetadata } from '@nestjs/common';

export const AUDIT_KEY = 'audit';
export const Audit = (enabled: boolean = true) =>
  SetMetadata(AUDIT_KEY, enabled);

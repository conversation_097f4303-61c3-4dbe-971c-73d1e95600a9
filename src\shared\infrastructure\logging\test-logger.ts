import { PinoLoggerConfig } from './pino-logger.config';
import { PinoLoggerService } from './pino-logger.service';

/**
 * 测试日志系统
 */
async function testLogger() {
  console.log('🚀 开始测试 Pino 日志系统...\n');

  try {
    // 创建日志配置和服务
    const config = new PinoLoggerConfig();
    const logger = new PinoLoggerService(config);
    
    logger.setContext('TestLogger');

    console.log('✅ 日志服务创建成功');

    // 测试基础日志功能
    console.log('\n📝 测试基础日志功能:');
    logger.log('这是一条普通日志信息');
    logger.debug('这是调试信息', { userId: '123', action: 'test' }, 'TestLogger');
    logger.warn('这是警告信息', { warning: 'test warning' }, 'TestLogger');
    logger.error('这是错误信息', 'Error stack trace here');

    // 测试业务日志
    console.log('\n💼 测试业务日志:');
    logger.logBusinessOperation(
      'user_login',
      'user_123',
      'tenant_456',
      {
        ip: '*************',
        userAgent: 'Test Agent',
        loginMethod: 'password',
      }
    );

    // 测试安全日志
    console.log('\n🔒 测试安全日志:');
    logger.logSecurityEvent(
      'login_failed',
      'medium',
      {
        userId: 'user_123',
        ip: '*************',
        reason: 'invalid_password',
        attemptCount: 3,
      }
    );

    // 测试性能日志
    console.log('\n⚡ 测试性能日志:');
    logger.logPerformanceMetric(
      'api_response_time',
      1250,
      'ms',
      {
        endpoint: '/api/test',
        method: 'GET',
        statusCode: '200',
      }
    );

    // 测试审计日志
    console.log('\n📋 测试审计日志:');
    logger.logAudit(
      'update',
      'admin_123',
      'user',
      'user_456',
      {
        before: { name: '张三', phone: '138****1234' },
        after: { name: '张三丰', phone: '139****5678' },
      }
    );

    // 测试子日志器
    console.log('\n👶 测试子日志器:');
    const childLogger = logger.child({
      userId: 'user_123',
      sessionId: 'session_456',
    });
    childLogger.log('子日志器测试消息');

    // 显示配置信息
    console.log('\n⚙️ 日志配置信息:');
    const loggerInfo = config.getLoggerInfo();
    console.log('- 日志级别:', loggerInfo.level);
    console.log('- 运行环境:', loggerInfo.environment);
    console.log('- 日志目录:', loggerInfo.logDirectory);
    console.log('- 文件格式:', loggerInfo.filePattern);

    console.log('\n🎉 日志系统测试完成！');
    console.log('📁 请检查 logs/ 目录下的日志文件');

  } catch (error) {
    console.error('❌ 日志系统测试失败:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testLogger();
}

export { testLogger };

import { Injectable, Inject, Logger } from '@nestjs/common';
import { Command<PERSON>andler } from '@/shared/application/cqrs/command';
import {
  CreateStudentCommand,
  UpdateStudentCommand,
  DeleteStudentCommand,
} from '../commands/create-student.command';
import { StudentUser } from '../../domain/aggregates/user.aggregate';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import { UserDomainService } from '../../domain/services/user-domain.service';
import { UserProfile } from '../../domain/value-objects/user-profile';
import { UserTypeEnum } from '../../domain/value-objects/user-type';
import {
  BusinessRuleException,
  EntityNotFoundException,
} from '@/shared/application/exceptions/domain-exception';
import {
  AuditContext,
  AuditAction,
} from '@/shared/domain/repositories/base-repository';
import { TenantContext } from '@/shared/domain/context/tenant.context';

/**
 * 创建学生命令处理器
 */
@Injectable()
export class CreateStudentHandler
  implements CommandHandler<CreateStudentCommand, StudentUser>
{
  private readonly logger = new Logger(CreateStudentHandler.name);

  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async handle(command: CreateStudentCommand): Promise<StudentUser> {
    this.logger.debug(`Handling CreateStudentCommand: ${command.commandId}`);

    try {
      // 1. 验证命令参数
      this.validateCommand(command);

      // 2. 创建用户档案
      const profile = new UserProfile({
        name: command.name,
        phone: command.phone,
        gender: command.gender,
        avatar: command.avatar,
      });

      // 3. 业务规则验证
      await this.userDomainService.validatePhoneUniqueness(profile.phone);

      // 4. 创建学生聚合
      const student = new StudentUser(
        profile,
        command.classId,
        command.gradeId,
      );

      // 设置入学和毕业时间
      if (command.enrollmentTime) {
        student.setEnrollmentTime(new Date(command.enrollmentTime));
      }
      if (command.graduationTime) {
        student.setGraduationTime(new Date(command.graduationTime));
      }

      // 5. 保存到数据库
      const auditContext: AuditContext = {
        userId: TenantContext.getCurrentUserId(),
        action: AuditAction.CREATE,
        metadata: {
          commandId: command.commandId,
          userType: UserTypeEnum.STUDENT,
        },
      };

      const savedStudent = await this.userRepository.save(
        student,
        auditContext,
      );

      this.logger.log(`Student created successfully: ${savedStudent.id}`);
      return savedStudent as StudentUser;
    } catch (error) {
      this.logger.error(`Failed to create student: ${error.message}`, {
        commandId: command.commandId,
        error: error.stack,
      });
      throw error;
    }
  }

  private validateCommand(command: CreateStudentCommand): void {
    if (!command.name?.trim()) {
      throw new BusinessRuleException('学生姓名不能为空');
    }

    if (!command.phone?.trim()) {
      throw new BusinessRuleException('手机号不能为空');
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(command.phone)) {
      throw new BusinessRuleException('手机号格式不正确');
    }
  }
}

/**
 * 更新学生命令处理器
 */
@Injectable()
export class UpdateStudentHandler
  implements CommandHandler<UpdateStudentCommand, StudentUser>
{
  private readonly logger = new Logger(UpdateStudentHandler.name);

  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async handle(command: UpdateStudentCommand): Promise<StudentUser> {
    this.logger.debug(`Handling UpdateStudentCommand: ${command.commandId}`);

    try {
      // 1. 查找现有学生
      const existingStudent = await this.userRepository.findById(command.id);
      if (!existingStudent || !(existingStudent instanceof StudentUser)) {
        throw new EntityNotFoundException('学生', command.id);
      }

      // 2. 验证更新权限
      await this.validateUpdatePermission(existingStudent, command);

      // 3. 更新学生信息
      if (
        command.name ||
        command.phone ||
        command.gender !== undefined ||
        command.avatar
      ) {
        const currentProfile = existingStudent.getProfile();
        if (!currentProfile) {
          throw new BusinessRuleException('无法获取学生档案信息');
        }

        const updatedProfile = new UserProfile({
          name: command.name || currentProfile.name,
          phone: command.phone || currentProfile.phone,
          gender:
            command.gender !== undefined
              ? command.gender
              : currentProfile.gender,
          avatar: command.avatar || currentProfile.avatar,
        });
        existingStudent.updateProfile(updatedProfile);
      }

      if (command.classId || command.gradeId) {
        existingStudent.updateClassInfo(command.classId, command.gradeId);
      }

      // 4. 保存更新
      const auditContext: AuditContext = {
        userId: TenantContext.getCurrentUserId(),
        action: AuditAction.UPDATE,
        metadata: {
          commandId: command.commandId,
          changes: this.getChanges(command),
        },
      };

      const updatedStudent = await this.userRepository.save(
        existingStudent,
        auditContext,
      );

      this.logger.log(`Student updated successfully: ${updatedStudent.id}`);
      return updatedStudent as StudentUser;
    } catch (error) {
      this.logger.error(`Failed to update student: ${error.message}`, {
        commandId: command.commandId,
        studentId: command.id,
        error: error.stack,
      });
      throw error;
    }
  }

  private async validateUpdatePermission(
    student: StudentUser,
    command: UpdateStudentCommand,
  ): Promise<void> {
    // 这里可以添加更新权限验证逻辑
    // 例如：只有管理员或班主任可以更新学生信息
  }

  private getChanges(command: UpdateStudentCommand): Record<string, any> {
    const changes: Record<string, any> = {};

    if (command.name) changes.name = command.name;
    if (command.phone) changes.phone = command.phone;
    if (command.gender !== undefined) changes.gender = command.gender;
    if (command.avatar) changes.avatar = command.avatar;
    if (command.classId) changes.classId = command.classId;
    if (command.gradeId) changes.gradeId = command.gradeId;

    return changes;
  }
}

/**
 * 删除学生命令处理器
 */
@Injectable()
export class DeleteStudentHandler
  implements CommandHandler<DeleteStudentCommand, void>
{
  private readonly logger = new Logger(DeleteStudentHandler.name);

  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async handle(command: DeleteStudentCommand): Promise<void> {
    this.logger.debug(`Handling DeleteStudentCommand: ${command.commandId}`);

    try {
      // 1. 查找学生
      const student = await this.userRepository.findById(command.id);
      if (!student || !(student instanceof StudentUser)) {
        throw new EntityNotFoundException('学生', command.id);
      }

      // 2. 验证删除权限
      await this.validateDeletePermission(student);

      // 3. 业务规则验证
      const canDelete = await this.userDomainService.canDeleteUser(command.id);
      if (!canDelete) {
        throw new BusinessRuleException('该学生存在关联数据，无法删除');
      }

      // 4. 执行软删除
      const auditContext: AuditContext = {
        userId: TenantContext.getCurrentUserId(),
        action: AuditAction.DELETE,
        metadata: {
          commandId: command.commandId,
          studentName: student.getProfile()?.name || 'Unknown',
        },
      };

      await this.userRepository.softDelete(command.id, auditContext);

      this.logger.log(`Student deleted successfully: ${command.id}`);
    } catch (error) {
      this.logger.error(`Failed to delete student: ${error.message}`, {
        commandId: command.commandId,
        studentId: command.id,
        error: error.stack,
      });
      throw error;
    }
  }

  private async validateDeletePermission(student: StudentUser): Promise<void> {
    // 这里可以添加删除权限验证逻辑
    // 例如：只有管理员可以删除学生
  }
}

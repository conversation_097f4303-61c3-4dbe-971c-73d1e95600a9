import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { SimpleCommandBus } from '@/shared/infrastructure/cqrs/simple-command-bus';
import { SimpleQueryBus } from '@/shared/infrastructure/cqrs/simple-query-bus';

// 命令处理器
import {
  CreateStudentHandler,
  UpdateStudentHandler,
  DeleteStudentHandler,
} from './student-command.handler';

// 命令类型
import {
  CreateStudentCommand,
  UpdateStudentCommand,
  DeleteStudentCommand,
} from '../commands/create-student.command';

/**
 * 命令和查询处理器注册服务
 * 在模块初始化时自动注册所有处理器
 */
@Injectable()
export class CommandHandlerRegistry implements OnModuleInit {
  private readonly logger = new Logger(CommandHandlerRegistry.name);

  constructor(
    private readonly commandBus: SimpleCommandBus,
    private readonly queryBus: SimpleQueryBus,
    
    // 注入所有命令处理器
    private readonly createStudentHandler: CreateStudentHandler,
    private readonly updateStudentHandler: UpdateStudentHandler,
    private readonly deleteStudentHandler: DeleteStudentHandler,
  ) {}

  /**
   * 模块初始化时注册所有处理器
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Registering command and query handlers...');

    try {
      // 注册学生相关命令处理器
      this.registerStudentCommandHandlers();

      // 注册查询处理器（如果有的话）
      // this.registerQueryHandlers();

      this.logger.log('All handlers registered successfully');
    } catch (error) {
      this.logger.error('Failed to register handlers', error);
      throw error;
    }
  }

  /**
   * 注册学生相关命令处理器
   */
  private registerStudentCommandHandlers(): void {
    // 注册创建学生命令处理器
    this.commandBus.register(CreateStudentCommand, this.createStudentHandler);
    this.logger.debug('Registered CreateStudentCommand handler');

    // 注册更新学生命令处理器
    this.commandBus.register(UpdateStudentCommand, this.updateStudentHandler);
    this.logger.debug('Registered UpdateStudentCommand handler');

    // 注册删除学生命令处理器
    this.commandBus.register(DeleteStudentCommand, this.deleteStudentHandler);
    this.logger.debug('Registered DeleteStudentCommand handler');
  }

  /**
   * 注册查询处理器
   */
  private registerQueryHandlers(): void {
    // TODO: 当有查询处理器时在这里注册
    // this.queryBus.register(GetStudentQuery, this.getStudentHandler);
  }

  /**
   * 获取已注册的命令列表
   */
  getRegisteredCommands(): string[] {
    return this.commandBus.getRegisteredCommands();
  }

  /**
   * 获取已注册的查询列表
   */
  getRegisteredQueries(): string[] {
    return this.queryBus.getRegisteredQueries();
  }

  /**
   * 检查命令是否已注册
   */
  isCommandRegistered(commandName: string): boolean {
    return this.commandBus.isCommandRegistered(commandName);
  }

  /**
   * 检查查询是否已注册
   */
  isQueryRegistered(queryName: string): boolean {
    return this.queryBus.isQueryRegistered(queryName);
  }
}

/**
 * 处理器注册装饰器
 * 用于自动注册命令和查询处理器
 */
export function AutoRegisterHandler() {
  return function (target: any) {
    // 这里可以添加自动注册逻辑
    // 例如通过反射获取处理器信息并自动注册
    Reflect.defineMetadata('auto-register-handler', true, target);
    return target;
  };
}

/**
 * 处理器信息接口
 */
export interface HandlerInfo {
  name: string;
  type: 'command' | 'query';
  commandType?: new (...args: any[]) => any;
  queryType?: new (...args: any[]) => any;
  handler: any;
}

/**
 * 处理器管理器
 * 提供处理器的统一管理功能
 */
@Injectable()
export class HandlerManager {
  private readonly logger = new Logger(HandlerManager.name);
  private readonly handlers = new Map<string, HandlerInfo>();

  /**
   * 注册处理器信息
   */
  registerHandler(info: HandlerInfo): void {
    this.handlers.set(info.name, info);
    this.logger.debug(`Registered handler info: ${info.name} (${info.type})`);
  }

  /**
   * 获取处理器信息
   */
  getHandler(name: string): HandlerInfo | undefined {
    return this.handlers.get(name);
  }

  /**
   * 获取所有处理器信息
   */
  getAllHandlers(): HandlerInfo[] {
    return Array.from(this.handlers.values());
  }

  /**
   * 获取命令处理器信息
   */
  getCommandHandlers(): HandlerInfo[] {
    return this.getAllHandlers().filter(h => h.type === 'command');
  }

  /**
   * 获取查询处理器信息
   */
  getQueryHandlers(): HandlerInfo[] {
    return this.getAllHandlers().filter(h => h.type === 'query');
  }

  /**
   * 清除所有处理器信息
   */
  clear(): void {
    this.handlers.clear();
    this.logger.debug('All handler info cleared');
  }
}

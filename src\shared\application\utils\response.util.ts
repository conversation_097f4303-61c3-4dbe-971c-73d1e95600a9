/**
 * 统一API响应接口
 */
export interface UnifiedApiResponse<T = any> {
  errCode: number;
  message: string;
  data: T;
  timestamp?: string;
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 分页响应数据接口
 */
export interface PaginatedData<T> {
  list: T[];
  pagination: PaginationInfo;
}

/**
 * 统一响应工具类
 * 提供标准化的API响应格式
 */
export class ResponseUtil {
  /**
   * 成功响应
   */
  static success<T>(data: T, message = 'success'): UnifiedApiResponse<T> {
    return {
      errCode: 0,
      message,
      data,
      timestamp: this.formatTimestamp(new Date()),
    };
  }

  /**
   * 错误响应
   */
  static error(message: string, errCode = 500): UnifiedApiResponse<null> {
    return {
      errCode,
      message,
      data: null,
      timestamp: this.formatTimestamp(new Date()),
    };
  }

  /**
   * 分页响应
   */
  static pagination<T>(
    data: T[],
    total: number,
    page: number,
    pageSize: number,
    message = 'success',
  ): UnifiedApiResponse<PaginatedData<T>> {
    return {
      errCode: 0,
      message,
      data: {
        list: data,
        pagination: {
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      },
      timestamp: this.formatTimestamp(new Date()),
    };
  }

  /**
   * 创建成功响应
   */
  static created<T>(data: T, message = '创建成功'): UnifiedApiResponse<T> {
    return this.success(data, message);
  }

  /**
   * 更新成功响应
   */
  static updated<T>(data: T, message = '更新成功'): UnifiedApiResponse<T> {
    return this.success(data, message);
  }

  /**
   * 删除成功响应
   */
  static deleted(message = '删除成功'): UnifiedApiResponse<null> {
    return this.success(null, message);
  }

  /**
   * 无内容响应
   */
  static noContent(message = '操作成功'): UnifiedApiResponse<null> {
    return this.success(null, message);
  }

  /**
   * 验证错误响应
   */
  static validationError(message = '参数验证失败', details?: any): UnifiedApiResponse<any> {
    return {
      errCode: 400,
      message,
      data: details || null,
      timestamp: this.formatTimestamp(new Date()),
    };
  }

  /**
   * 未授权响应
   */
  static unauthorized(message = '未授权访问'): UnifiedApiResponse<null> {
    return this.error(message, 401);
  }

  /**
   * 权限不足响应
   */
  static forbidden(message = '权限不足'): UnifiedApiResponse<null> {
    return this.error(message, 403);
  }

  /**
   * 资源不存在响应
   */
  static notFound(message = '资源不存在'): UnifiedApiResponse<null> {
    return this.error(message, 404);
  }

  /**
   * 资源冲突响应
   */
  static conflict(message = '资源冲突'): UnifiedApiResponse<null> {
    return this.error(message, 409);
  }

  /**
   * 乐观锁冲突响应
   */
  static optimisticLockError(message = '数据已被其他用户修改，请刷新后重试'): UnifiedApiResponse<null> {
    return this.error(message, 409);
  }

  /**
   * 租户权限错误响应
   */
  static tenantAccessDenied(message = '租户访问权限不足'): UnifiedApiResponse<null> {
    return this.error(message, 403);
  }

  /**
   * 服务器错误响应
   */
  static internalServerError(message = '服务器内部错误'): UnifiedApiResponse<null> {
    return this.error(message, 500);
  }

  /**
   * 业务规则错误响应
   */
  static businessRuleError(message: string): UnifiedApiResponse<null> {
    return this.error(message, 422);
  }

  /**
   * 格式化时间戳
   */
  private static formatTimestamp(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 创建分页信息
   */
  static createPaginationInfo(
    total: number,
    page: number,
    pageSize: number,
  ): PaginationInfo {
    return {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 验证分页参数
   */
  static validatePaginationParams(page: number, pageSize: number): { page: number; pageSize: number } {
    const validPage = Math.max(1, Math.floor(page) || 1);
    const validPageSize = Math.min(100, Math.max(1, Math.floor(pageSize) || 10));

    return { page: validPage, pageSize: validPageSize };
  }

  /**
   * 计算分页偏移量
   */
  static calculateOffset(page: number, pageSize: number): number {
    return (page - 1) * pageSize;
  }
}

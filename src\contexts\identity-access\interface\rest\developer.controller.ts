import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DeveloperAccountService } from '../../infrastructure/services/developer-account.service';
import { JwtAuthGuard } from '../../infrastructure/guards/jwt-auth.guard';
import { RequirePermissions, RequireRoles } from '../../infrastructure/guards/permissions.guard';
import { ResponseUtil } from '@/shared/application/utils/response.util';

/**
 * 开发者账号管理控制器
 * 仅在开发和测试环境可用
 */
@ApiTags('开发者管理')
@Controller('developers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeveloperController {
  constructor(private readonly developerAccountService: DeveloperAccountService) {}

  /**
   * 创建开发者账号
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequireRoles('admin')
  @RequirePermissions('developer:create')
  @ApiOperation({ summary: '创建开发者账号' })
  @ApiResponse({ status: 201, description: '开发者账号创建成功' })
  async createDeveloperAccount() {
    const developer = await this.developerAccountService.createDeveloperAccount();
    return ResponseUtil.success(
      {
        id: developer.id,
        name: developer.getProfile()?.name,
        phone: developer.getProfile()?.phone,
        accessLevel: developer.getAccessLevel(),
        allowedEnvironments: developer.getAllowedEnvironments(),
        apiKey: developer.getApiKey(),
      },
      '开发者账号创建成功',
    );
  }

  /**
   * 获取开发者账号信息
   */
  @Get(':phone')
  @RequireRoles('admin', 'developer')
  @RequirePermissions('developer:read')
  @ApiOperation({ summary: '获取开发者账号信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDeveloperInfo(@Param('phone') phone: string) {
    const info = await this.developerAccountService.getDeveloperInfo(phone);

    if (!info) {
      return ResponseUtil.error('开发者账号不存在', 404);
    }

    return ResponseUtil.success(info, '获取开发者信息成功');
  }

  /**
   * 列出所有开发者账号
   */
  @Get()
  @RequireRoles('admin')
  @RequirePermissions('developer:read')
  @ApiOperation({ summary: '列出所有开发者账号' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async listDeveloperAccounts() {
    const developers = await this.developerAccountService.listDeveloperAccounts();

    const result = developers.map(dev => {
      const profile = dev.getProfile();
      return {
        id: dev.id,
        name: profile?.name,
        phone: profile?.phone,
        accessLevel: dev.getAccessLevel(),
        allowedEnvironments: dev.getAllowedEnvironments(),
        loginStats: dev.getLoginStats(),
        createdAt: dev.createdAt,
        updatedAt: dev.updatedAt,
      };
    });

    return ResponseUtil.success(result, '获取开发者列表成功');
  }

  /**
   * 重置开发者密码
   */
  @Put(':phone/password')
  @RequireRoles('admin')
  @RequirePermissions('developer:update')
  @ApiOperation({ summary: '重置开发者密码' })
  @ApiResponse({ status: 200, description: '密码重置成功' })
  async resetPassword(
    @Param('phone') phone: string,
    @Body() body: { newPassword: string },
  ) {
    await this.developerAccountService.resetDeveloperPassword(phone, body.newPassword);
    return ResponseUtil.success(null, '密码重置成功');
  }

  /**
   * 重新生成API密钥
   */
  @Put(':phone/api-key')
  @RequireRoles('admin', 'developer')
  @RequirePermissions('developer:update')
  @ApiOperation({ summary: '重新生成API密钥' })
  @ApiResponse({ status: 200, description: 'API密钥重新生成成功' })
  async regenerateApiKey(@Param('phone') phone: string) {
    const newApiKey = await this.developerAccountService.regenerateApiKey(phone);
    return ResponseUtil.success(
      { apiKey: newApiKey },
      'API密钥重新生成成功',
    );
  }

  /**
   * 删除开发者账号
   */
  @Delete(':phone')
  @RequireRoles('admin')
  @RequirePermissions('developer:delete')
  @ApiOperation({ summary: '删除开发者账号' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async deleteDeveloperAccount(@Param('phone') phone: string) {
    await this.developerAccountService.deleteDeveloperAccount(phone);
    return ResponseUtil.success(null, '开发者账号删除成功');
  }

  /**
   * 确保开发者账号存在（开发环境专用）
   */
  @Post('ensure')
  @RequireRoles('admin')
  @RequirePermissions('developer:create')
  @ApiOperation({ summary: '确保开发者账号存在' })
  @ApiResponse({ status: 200, description: '操作成功' })
  async ensureDeveloperAccount() {
    const developer = await this.developerAccountService.ensureDeveloperAccountExists();
    const profile = developer.getProfile();

    return ResponseUtil.success(
      {
        id: developer.id,
        name: profile?.name,
        phone: profile?.phone,
        accessLevel: developer.getAccessLevel(),
        apiKey: developer.getApiKey()?.substring(0, 8) + '...',
        message: '开发者账号已确保存在',
      },
      '操作成功',
    );
  }

  /**
   * 获取开发者账号配置信息
   */
  @Get('config/info')
  @RequireRoles('admin')
  @RequirePermissions('developer:read')
  @ApiOperation({ summary: '获取开发者账号配置信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDeveloperConfig() {
    // 返回配置信息（不包含敏感数据）
    return ResponseUtil.success(
      {
        defaultPhone: '***********',
        defaultName: '系统开发者',
        defaultAccessLevel: 'admin',
        defaultEnvironments: ['development', 'staging', 'production'],
        note: '开发者账号用于系统测试和初始化数据',
      },
      '获取配置信息成功',
    );
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TypeOrmBaseRepository } from '@/shared/infrastructure/repositories/typeorm-base.repository';
import { User, StudentUser } from '../../domain/aggregates/user.aggregate';
import {
  UserRepository,
  UserQueryRepository,
} from '../../domain/repositories/user.repository';
import { UserTypeEnum } from '../../domain/value-objects/user-type';

/**
 * TypeORM用户仓储实现
 */
@Injectable()
export class TypeOrmUserRepository
  extends TypeOrmBaseRepository<User>
  implements UserRepository, UserQueryRepository
{
  constructor(
    @InjectRepository(User)
    repository: Repository<User>,
    @InjectRepository(StudentUser)
    private readonly studentRepository: Repository<StudentUser>,
  ) {
    super(repository, 'UserRepository');
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<User | null> {
    const startTime = Date.now();
    try {
      const user = await this.repository
        .createQueryBuilder('user')
        .where('user.phone = :phone', { phone })
        .getOne();

      this.logPerformance('findByPhone', startTime);
      return user;
    } catch (error) {
      this.logger.error(`Error finding user by phone: ${phone}`, error);
      throw error;
    }
  }

  /**
   * 根据用户类型查找用户
   */
  async findByUserType(userType: UserTypeEnum): Promise<User[]> {
    const startTime = Date.now();
    try {
      const users = await this.repository
        .createQueryBuilder('user')
        .where('user.userType = :userType', { userType })
        .orderBy('user.createdAt', 'DESC')
        .getMany();

      this.logPerformance('findByUserType', startTime);
      return users;
    } catch (error) {
      this.logger.error(`Error finding users by type: ${userType}`, error);
      throw error;
    }
  }

  /**
   * 根据证件号查找用户
   */
  async findByCardNo(cardNo: string): Promise<User | null> {
    const startTime = Date.now();
    try {
      const user = await this.repository
        .createQueryBuilder('user')
        .where('user.cardNo = :cardNo', { cardNo })
        .getOne();

      this.logPerformance('findByCardNo', startTime);
      return user;
    } catch (error) {
      this.logger.error(`Error finding user by cardNo: ${cardNo}`, error);
      throw error;
    }
  }

  /**
   * 检查手机号是否存在
   */
  async existsByPhone(phone: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      const count = await this.repository
        .createQueryBuilder('user')
        .where('user.phone = :phone', { phone })
        .getCount();

      this.logPerformance('existsByPhone', startTime);
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking phone existence: ${phone}`, error);
      throw error;
    }
  }

  /**
   * 检查证件号是否存在
   */
  async existsByCardNo(cardNo: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      const count = await this.repository
        .createQueryBuilder('user')
        .where('user.cardNo = :cardNo', { cardNo })
        .getCount();

      this.logPerformance('existsByCardNo', startTime);
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking cardNo existence: ${cardNo}`, error);
      throw error;
    }
  }

  /**
   * 查找激活的用户
   */
  async findActiveUsers(): Promise<User[]> {
    const startTime = Date.now();
    try {
      const users = await this.repository
        .createQueryBuilder('user')
        .where('user.isActive = :isActive', { isActive: true })
        .orderBy('user.createdAt', 'DESC')
        .getMany();

      this.logPerformance('findActiveUsers', startTime);
      return users;
    } catch (error) {
      this.logger.error(`Error finding active users`, error);
      throw error;
    }
  }

  /**
   * 根据条件搜索用户
   */
  async searchUsers(criteria: {
    name?: string;
    phone?: string;
    userType?: UserTypeEnum;
    isActive?: boolean;
    classId?: string;
    gradeId?: string;
  }): Promise<User[]> {
    const startTime = Date.now();
    try {
      const queryBuilder = this.repository.createQueryBuilder('user');

      // 基础条件
      if (criteria.name) {
        queryBuilder.andWhere('user.name LIKE :name', {
          name: `%${criteria.name}%`,
        });
      }

      if (criteria.phone) {
        queryBuilder.andWhere('user.phone LIKE :phone', {
          phone: `%${criteria.phone}%`,
        });
      }

      if (criteria.userType) {
        queryBuilder.andWhere('user.userType = :userType', {
          userType: criteria.userType,
        });
      }

      if (criteria.isActive !== undefined) {
        queryBuilder.andWhere('user.isActive = :isActive', {
          isActive: criteria.isActive,
        });
      }

      // 学生特有条件
      if (criteria.classId || criteria.gradeId) {
        queryBuilder.andWhere('user.userType = :studentType', {
          studentType: UserTypeEnum.STUDENT,
        });

        if (criteria.classId) {
          queryBuilder.andWhere('user.classId = :classId', {
            classId: criteria.classId,
          });
        }

        if (criteria.gradeId) {
          queryBuilder.andWhere('user.gradeId = :gradeId', {
            gradeId: criteria.gradeId,
          });
        }
      }

      const users = await queryBuilder
        .orderBy('user.createdAt', 'DESC')
        .getMany();

      this.logPerformance('searchUsers', startTime);
      return users;
    } catch (error) {
      this.logger.error(`Error searching users`, error);
      throw error;
    }
  }

  /**
   * 根据年龄范围查找用户
   */
  async findByAgeRange(minAge: number, maxAge: number): Promise<User[]> {
    const startTime = Date.now();
    try {
      const users = await this.repository
        .createQueryBuilder('user')
        .where('user.age >= :minAge AND user.age <= :maxAge', {
          minAge,
          maxAge,
        })
        .getMany();

      this.logPerformance('findByAgeRange', startTime);
      return users;
    } catch (error) {
      this.logger.error(
        `Error finding users by age range: ${minAge}-${maxAge}`,
        error,
      );
      throw error;
    }
  }

  /**
   * 查找最近注册的用户
   */
  async findRecentlyRegistered(days: number, limit?: number): Promise<User[]> {
    const startTime = Date.now();
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const query = this.repository
        .createQueryBuilder('user')
        .where('user.createdAt >= :cutoffDate', { cutoffDate })
        .orderBy('user.createdAt', 'DESC');

      if (limit) {
        query.limit(limit);
      }

      const users = await query.getMany();
      this.logPerformance('findRecentlyRegistered', startTime);
      return users;
    } catch (error) {
      this.logger.error(
        `Error finding recently registered users: ${days} days`,
        error,
      );
      throw error;
    }
  }

  /**
   * 根据复杂条件查询用户
   */
  async findByCriteria(criteria: {
    names?: string[];
    phones?: string[];
    userTypes?: UserTypeEnum[];
    isActive?: boolean;
    ageRange?: { min: number; max: number };
    registrationDateRange?: { start: Date; end: Date };
    classIds?: string[];
    gradeIds?: string[];
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<User[]> {
    const startTime = Date.now();
    try {
      const query = this.repository.createQueryBuilder('user');

      if (criteria.names && criteria.names.length > 0) {
        query.andWhere('user.name IN (:...names)', { names: criteria.names });
      }

      if (criteria.phones && criteria.phones.length > 0) {
        query.andWhere('user.phone IN (:...phones)', {
          phones: criteria.phones,
        });
      }

      if (criteria.userTypes && criteria.userTypes.length > 0) {
        query.andWhere('user.userType IN (:...userTypes)', {
          userTypes: criteria.userTypes,
        });
      }

      if (criteria.isActive !== undefined) {
        query.andWhere('user.isActive = :isActive', {
          isActive: criteria.isActive,
        });
      }

      if (criteria.ageRange) {
        query.andWhere('user.age >= :minAge AND user.age <= :maxAge', {
          minAge: criteria.ageRange.min,
          maxAge: criteria.ageRange.max,
        });
      }

      if (criteria.registrationDateRange) {
        query.andWhere(
          'user.createdAt >= :startDate AND user.createdAt <= :endDate',
          {
            startDate: criteria.registrationDateRange.start,
            endDate: criteria.registrationDateRange.end,
          },
        );
      }

      if (criteria.sortBy) {
        query.orderBy(`user.${criteria.sortBy}`, criteria.sortOrder || 'ASC');
      }

      if (criteria.offset) {
        query.offset(criteria.offset);
      }

      if (criteria.limit) {
        query.limit(criteria.limit);
      }

      const users = await query.getMany();
      this.logPerformance('findByCriteria', startTime);
      return users;
    } catch (error) {
      this.logger.error('Error finding users by criteria', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStatistics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    studentCount: number;
    teacherCount: number;
    parentCount: number;
    visitorCount: number;
    recentRegistrations: number;
  }> {
    const startTime = Date.now();
    try {
      const [
        totalUsers,
        activeUsers,
        studentCount,
        teacherCount,
        parentCount,
        visitorCount,
      ] = await Promise.all([
        this.repository.count(),
        this.repository
          .createQueryBuilder('user')
          .where('user.isActive = :isActive', { isActive: true })
          .getCount(),
        this.repository
          .createQueryBuilder('user')
          .where('user.userType = :userType', {
            userType: UserTypeEnum.STUDENT,
          })
          .getCount(),
        this.repository
          .createQueryBuilder('user')
          .where('user.userType = :userType', {
            userType: UserTypeEnum.TEACHER,
          })
          .getCount(),
        this.repository
          .createQueryBuilder('user')
          .where('user.userType = :userType', { userType: UserTypeEnum.PARENT })
          .getCount(),
        this.repository
          .createQueryBuilder('user')
          .where('user.userType = :userType', {
            userType: UserTypeEnum.VISITOR,
          })
          .getCount(),
      ]);

      // 最近7天注册用户数
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentRegistrations = await this.repository
        .createQueryBuilder('user')
        .where('user.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
        .getCount();

      this.logPerformance('getUserStatistics', startTime);

      return {
        totalUsers,
        activeUsers,
        studentCount,
        teacherCount,
        parentCount,
        visitorCount,
        recentRegistrations,
      };
    } catch (error) {
      this.logger.error(`Error getting user statistics`, error);
      throw error;
    }
  }

  /**
   * 分页查询用户
   */
  async findUsersWithPagination(
    criteria: {
      name?: string;
      phone?: string;
      userType?: UserTypeEnum;
      isActive?: boolean;
      classId?: string;
      gradeId?: string;
    },
    page: number = 1,
    pageSize: number = 10,
    sortBy: string = 'createdAt',
    sortOrder: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{
    data: User[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const startTime = Date.now();
    try {
      const queryBuilder = this.repository.createQueryBuilder('user');

      // 应用搜索条件
      if (criteria.name) {
        queryBuilder.andWhere('user.name LIKE :name', {
          name: `%${criteria.name}%`,
        });
      }

      if (criteria.phone) {
        queryBuilder.andWhere('user.phone LIKE :phone', {
          phone: `%${criteria.phone}%`,
        });
      }

      if (criteria.userType) {
        queryBuilder.andWhere('user.userType = :userType', {
          userType: criteria.userType,
        });
      }

      if (criteria.isActive !== undefined) {
        queryBuilder.andWhere('user.isActive = :isActive', {
          isActive: criteria.isActive,
        });
      }

      if (criteria.classId || criteria.gradeId) {
        queryBuilder.andWhere('user.userType = :studentType', {
          studentType: UserTypeEnum.STUDENT,
        });

        if (criteria.classId) {
          queryBuilder.andWhere('user.classId = :classId', {
            classId: criteria.classId,
          });
        }

        if (criteria.gradeId) {
          queryBuilder.andWhere('user.gradeId = :gradeId', {
            gradeId: criteria.gradeId,
          });
        }
      }

      // 分页和排序
      const skip = (page - 1) * pageSize;
      queryBuilder
        .orderBy(`user.${sortBy}`, sortOrder)
        .skip(skip)
        .take(pageSize);

      const [data, total] = await queryBuilder.getManyAndCount();
      const totalPages = Math.ceil(total / pageSize);

      this.logPerformance('findUsersWithPagination', startTime);

      return {
        data,
        total,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error in paginated user query`, error);
      throw error;
    }
  }

  /**
   * 根据班级ID查找学生
   */
  async findStudentsByClass(classId: string): Promise<StudentUser[]> {
    const startTime = Date.now();
    try {
      const students = await this.studentRepository
        .createQueryBuilder('student')
        .where('student.classId = :classId', { classId })
        .orderBy('student.name', 'ASC')
        .getMany();

      this.logPerformance('findStudentsByClass', startTime);
      return students;
    } catch (error) {
      this.logger.error(`Error finding students by class: ${classId}`, error);
      throw error;
    }
  }

  /**
   * 根据年级ID查找学生
   */
  async findStudentsByGrade(gradeId: string): Promise<StudentUser[]> {
    const startTime = Date.now();
    try {
      const students = await this.studentRepository
        .createQueryBuilder('student')
        .where('student.gradeId = :gradeId', { gradeId })
        .orderBy('student.name', 'ASC')
        .getMany();

      this.logPerformance('findStudentsByGrade', startTime);
      return students;
    } catch (error) {
      this.logger.error(`Error finding students by grade: ${gradeId}`, error);
      throw error;
    }
  }

  /**
   * 批量更新用户状态
   */
  async batchUpdateUserStatus(
    userIds: string[],
    isActive: boolean,
  ): Promise<void> {
    const startTime = Date.now();
    try {
      // 批量更新用户状态
      for (const userId of userIds) {
        const user = await this.repository.findOne({
          where: { id: userId } as any,
        });
        if (user) {
          if (isActive) {
            user.activate();
          } else {
            user.deactivate();
          }
          await this.repository.save(user);
        }
      }
      this.logPerformance('batchUpdateUserStatus', startTime);
    } catch (error) {
      this.logger.error(`Error batch updating user status`, error);
      throw error;
    }
  }

  /**
   * 根据条件统计用户数量
   */
  async countByCriteria(criteria: {
    names?: string[];
    phones?: string[];
    userTypes?: string[];
    isActive?: boolean;
    ageRange?: { min: number; max: number };
    registrationDateRange?: { start: Date; end: Date };
    classIds?: string[];
    gradeIds?: string[];
  }): Promise<number> {
    const startTime = Date.now();
    try {
      const query = this.repository.createQueryBuilder('user');

      if (criteria.names && criteria.names.length > 0) {
        query.andWhere('user.name IN (:...names)', { names: criteria.names });
      }

      if (criteria.phones && criteria.phones.length > 0) {
        query.andWhere('user.phone IN (:...phones)', {
          phones: criteria.phones,
        });
      }

      if (criteria.userTypes && criteria.userTypes.length > 0) {
        query.andWhere('user.userType IN (:...userTypes)', {
          userTypes: criteria.userTypes,
        });
      }

      if (criteria.isActive !== undefined) {
        query.andWhere('user.isActive = :isActive', {
          isActive: criteria.isActive,
        });
      }

      if (criteria.ageRange) {
        query.andWhere('user.age >= :minAge AND user.age <= :maxAge', {
          minAge: criteria.ageRange.min,
          maxAge: criteria.ageRange.max,
        });
      }

      if (criteria.registrationDateRange) {
        query.andWhere(
          'user.createdAt >= :startDate AND user.createdAt <= :endDate',
          {
            startDate: criteria.registrationDateRange.start,
            endDate: criteria.registrationDateRange.end,
          },
        );
      }

      if (criteria.classIds && criteria.classIds.length > 0) {
        query.andWhere('user.classId IN (:...classIds)', {
          classIds: criteria.classIds,
        });
      }

      if (criteria.gradeIds && criteria.gradeIds.length > 0) {
        query.andWhere('user.gradeId IN (:...gradeIds)', {
          gradeIds: criteria.gradeIds,
        });
      }

      const count = await query.getCount();
      this.logPerformance('countByCriteria', startTime);
      return count;
    } catch (error) {
      this.logger.error('Error counting users by criteria', error);
      throw error;
    }
  }

  /**
   * 根据关键词搜索用户
   */
  async searchByKeyword(
    keyword: string,
    userTypes?: string[],
    limit?: number,
  ): Promise<User[]> {
    const startTime = Date.now();
    try {
      const query = this.repository.createQueryBuilder('user');

      // 搜索条件：姓名或手机号包含关键词
      query.where('(user.name LIKE :keyword OR user.phone LIKE :keyword)', {
        keyword: `%${keyword}%`,
      });

      // 用户类型过滤
      if (userTypes && userTypes.length > 0) {
        query.andWhere('user.userType IN (:...userTypes)', { userTypes });
      }

      // 限制数量
      if (limit) {
        query.limit(limit);
      }

      // 按相关性排序（姓名匹配优先）
      query
        .orderBy(
          'CASE WHEN user.name LIKE :exactKeyword THEN 1 ELSE 2 END',
          'ASC',
        )
        .addOrderBy('user.name', 'ASC')
        .setParameter('exactKeyword', `%${keyword}%`);

      const users = await query.getMany();
      this.logPerformance('searchByKeyword', startTime);
      return users;
    } catch (error) {
      this.logger.error(`Error searching users by keyword: ${keyword}`, error);
      throw error;
    }
  }

  /**
   * 获取实体名称
   */
  protected getEntityName(): string {
    return '用户';
  }
}

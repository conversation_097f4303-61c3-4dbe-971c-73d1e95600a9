import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from '../base.entity';

/**
 * 家长用户实体
 */
@Entity('user_parents')
export class UserParent extends BaseEntity {
  @Column({ unique: true, comment: '用户名' })
  username: string;

  @Column({ comment: '密码哈希' })
  passwordHash: string;

  @Column({ comment: '真实姓名' })
  realName: string;

  @Column({ nullable: true, comment: '邮箱' })
  email?: string;

  @Column({ nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ nullable: true, comment: '身份证号' })
  idCard?: string;

  @Column({ nullable: true, comment: '工作单位' })
  workplace?: string;

  @Column({ nullable: true, comment: '职业' })
  occupation?: string;

  @Column({ nullable: true, comment: '与学生关系' })
  relationship?: string;

  @Column({ default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({ nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ nullable: true, comment: '头像URL' })
  avatarUrl?: string;

  /**
   * 验证密码
   */
  validatePassword(password: string): boolean {
    // 这里应该使用实际的密码验证逻辑
    // 例如使用 bcrypt 比较密码
    return true; // 临时实现
  }

  /**
   * 更新最后登录时间
   */
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    return this.realName || this.username;
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTypeColumn1704067200000 implements MigrationInterface {
  name = 'UpdateUserTypeColumn1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 检查表是否存在
    const tableExists = await queryRunner.hasTable('users');
    
    if (tableExists) {
      // 检查列是否存在
      const columnExists = await queryRunner.hasColumn('users', 'user_type');
      
      if (columnExists) {
        // 修改 user_type 列类型从 ENUM 到 VARCHAR
        await queryRunner.query(`
          ALTER TABLE \`users\` 
          MODIFY COLUMN \`user_type\` VARCHAR(20) NOT NULL
        `);
        
        console.log('✅ user_type column updated to VARCHAR(20)');
      } else {
        // 如果列不存在，创建它
        await queryRunner.query(`
          ALTER TABLE \`users\` 
          ADD COLUMN \`user_type\` VARCHAR(20) NOT NULL DEFAULT 'student'
        `);
        
        console.log('✅ user_type column created as VARCHAR(20)');
      }
    } else {
      console.log('⚠️ users table does not exist, skipping migration');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 检查表是否存在
    const tableExists = await queryRunner.hasTable('users');
    
    if (tableExists) {
      // 检查列是否存在
      const columnExists = await queryRunner.hasColumn('users', 'user_type');
      
      if (columnExists) {
        // 回滚：将 user_type 列改回 ENUM 类型
        await queryRunner.query(`
          ALTER TABLE \`users\` 
          MODIFY COLUMN \`user_type\` ENUM('student', 'teacher', 'parent', 'visitor', 'developer') NOT NULL
        `);
        
        console.log('✅ user_type column reverted to ENUM');
      }
    }
  }
}

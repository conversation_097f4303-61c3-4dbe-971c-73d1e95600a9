import { Injectable, Inject } from '@nestjs/common';
import { VisitorUser } from '../../domain/aggregates/user.aggregate';
import { UserProfile } from '../../domain/value-objects/user-profile';
import { UserDomainService } from '../../domain/services/user-domain.service';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import {
  EntityNotFoundException,
  BusinessRuleException,
} from '@/shared/application/exceptions/domain-exception';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';
import { UserTypeEnum } from '../../domain/value-objects/user-type';

/**
 * 访客应用服务
 */
@Injectable()
export class VisitorApplicationService {
  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  /**
   * 创建访客
   */
  async createVisitor(visitorData: {
    name: string;
    phone: string;
    gender?: number;
    avatar?: string;
    age?: number;
    birthday?: Date;
    cardNo?: string;
    cardType?: string;
    visitPurpose?: string;
    visitStartTime?: Date;
    visitEndTime?: Date;
    hostName?: string;
    hostPhone?: string;
    company?: string;
  }): Promise<VisitorUser> {
    // 验证手机号是否已存在
    await this.userDomainService.validatePhoneUniqueness(visitorData.phone);

    // 创建用户档案
    const profile = new UserProfile({
      name: visitorData.name,
      phone: visitorData.phone,
      gender: visitorData.gender,
      avatar: visitorData.avatar,
      age: visitorData.age,
      birthday: visitorData.birthday,
      cardNo: visitorData.cardNo,
      cardType: visitorData.cardType,
    });

    // 创建访客用户
    const visitor = new VisitorUser(profile, visitorData.visitPurpose);

    // 设置访问时间
    if (visitorData.visitStartTime && visitorData.visitEndTime) {
      visitor.setVisitTime(
        visitorData.visitStartTime,
        visitorData.visitEndTime,
      );
    }

    // 设置接待人信息
    if (visitorData.hostName && visitorData.hostPhone) {
      visitor.setHost(visitorData.hostName, visitorData.hostPhone);
    }

    // 设置公司信息
    if (visitorData.company) {
      visitor.setCompany(visitorData.company);
    }

    // 保存到数据库
    return (await this.userRepository.save(visitor)) as VisitorUser;
  }

  /**
   * 更新访客信息
   */
  async updateVisitor(
    visitorId: string,
    updateData: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      visitPurpose?: string;
      visitStartTime?: Date;
      visitEndTime?: Date;
      hostName?: string;
      hostPhone?: string;
      company?: string;
    },
  ): Promise<VisitorUser> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    // 如果更新了基本信息，需要验证和更新档案
    if (
      updateData.name ||
      updateData.phone ||
      updateData.gender ||
      updateData.avatar ||
      updateData.age ||
      updateData.birthday ||
      updateData.cardNo ||
      updateData.cardType
    ) {
      const currentProfile = visitor.getProfile();
      if (!currentProfile) {
        throw new BusinessRuleException('访客档案数据不完整');
      }

      const newProfile = new UserProfile({
        name: updateData.name || currentProfile.name,
        phone: updateData.phone || currentProfile.phone,
        gender:
          updateData.gender !== undefined
            ? updateData.gender
            : currentProfile.gender,
        avatar: updateData.avatar || currentProfile.avatar,
        age: updateData.age !== undefined ? updateData.age : currentProfile.age,
        birthday: updateData.birthday || currentProfile.birthday,
        cardNo: updateData.cardNo || currentProfile.cardNo,
        cardType: updateData.cardType || currentProfile.cardType,
      });

      // 验证更新
      await this.userDomainService.validateProfileUpdate(visitorId, newProfile);
      visitor.updateProfile(newProfile);
    }

    // 更新访问时间
    if (updateData.visitStartTime && updateData.visitEndTime) {
      visitor.setVisitTime(updateData.visitStartTime, updateData.visitEndTime);
    }

    // 更新接待人信息
    if (updateData.hostName && updateData.hostPhone) {
      visitor.setHost(updateData.hostName, updateData.hostPhone);
    }

    // 更新公司信息
    if (updateData.company !== undefined) {
      visitor.setCompany(updateData.company);
    }

    return (await this.userRepository.save(visitor)) as VisitorUser;
  }

  /**
   * 审批访客
   */
  async approveVisitor(visitorId: string): Promise<VisitorUser> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    visitor.approve();
    return (await this.userRepository.save(visitor)) as VisitorUser;
  }

  /**
   * 拒绝访客
   */
  async rejectVisitor(visitorId: string): Promise<VisitorUser> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    visitor.reject();
    return (await this.userRepository.save(visitor)) as VisitorUser;
  }

  /**
   * 删除访客
   */
  async deleteVisitor(visitorId: string): Promise<void> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    await this.userRepository.delete(visitorId);
  }

  /**
   * 获取访客详情
   */
  async getVisitor(visitorId: string): Promise<VisitorUser> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    return visitor;
  }

  /**
   * 获取访客列表
   */
  async getVisitors(params: {
    page?: number;
    pageSize?: number;
    name?: string;
    phone?: string;
    isActive?: boolean;
    isApproved?: boolean;
    company?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<PaginatedResponseDto<VisitorUser>> {
    const {
      page = 1,
      pageSize = 10,
      name,
      phone,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = params;

    const criteria = {
      userTypes: ['visitor'],
      names: name ? [name] : undefined,
      phones: phone ? [phone] : undefined,
      isActive,
      limit: pageSize,
      offset: (page - 1) * pageSize,
      sortBy,
      sortOrder,
    };

    // 临时实现：使用现有方法
    const allVisitors = await this.userRepository.findByUserType(
      UserTypeEnum.VISITOR,
    );
    const visitors = allVisitors.slice((page - 1) * pageSize, page * pageSize);
    const total = allVisitors.length;

    let filteredVisitors = visitors.filter((user) => user) as VisitorUser[];

    // 根据审批状态过滤
    if (params.isApproved !== undefined) {
      filteredVisitors = filteredVisitors.filter(
        (visitor) => visitor.getApprovalStatus() === params.isApproved,
      );
    }

    // 根据公司过滤
    if (params.company) {
      filteredVisitors = filteredVisitors.filter(
        (visitor) => visitor.getCompany()?.includes(params.company!) || false,
      );
    }

    return new PaginatedResponseDto(filteredVisitors, total, page, pageSize);
  }

  /**
   * 获取待审批访客列表
   */
  async getPendingVisitors(): Promise<VisitorUser[]> {
    const allVisitors = await this.userRepository.findByUserType(
      UserTypeEnum.VISITOR,
    );
    return allVisitors
      .filter((user) => user)
      .filter(
        (visitor) => !(visitor as VisitorUser).getApprovalStatus(),
      ) as VisitorUser[];
  }

  /**
   * 获取当前有效访客列表
   */
  async getValidVisitors(): Promise<VisitorUser[]> {
    const allVisitors = await this.userRepository.findByUserType(
      UserTypeEnum.VISITOR,
    );
    return allVisitors
      .filter((user) => user)
      .filter((visitor) =>
        (visitor as VisitorUser).isVisitValid(),
      ) as VisitorUser[];
  }

  /**
   * 搜索访客
   */
  async searchVisitors(
    keyword: string,
    limit: number = 20,
  ): Promise<VisitorUser[]> {
    // 临时实现：使用现有方法进行简单搜索
    const allVisitors = await this.userRepository.findByUserType(
      UserTypeEnum.VISITOR,
    );
    const visitors = allVisitors
      .filter((visitor) => {
        const profile = visitor.getProfile();
        return (
          profile?.name?.includes(keyword) || profile?.phone?.includes(keyword)
        );
      })
      .slice(0, limit);
    return visitors.filter((user) => user) as VisitorUser[];
  }

  /**
   * 激活/停用访客
   */
  async toggleVisitorStatus(visitorId: string): Promise<VisitorUser> {
    const visitor = await this.userRepository.findById(visitorId);
    if (!visitor || !(visitor instanceof VisitorUser)) {
      throw new EntityNotFoundException('Visitor', visitorId);
    }

    if (visitor.isUserActive()) {
      visitor.deactivate();
    } else {
      visitor.activate();
    }

    return (await this.userRepository.save(visitor)) as VisitorUser;
  }

  /**
   * 批量删除访客
   */
  async batchDeleteVisitors(visitorIds: string[]): Promise<void> {
    for (const visitorId of visitorIds) {
      await this.deleteVisitor(visitorId);
    }
  }

  /**
   * 批量审批访客
   */
  async batchApproveVisitors(visitorIds: string[]): Promise<VisitorUser[]> {
    const results: VisitorUser[] = [];
    for (const visitorId of visitorIds) {
      const visitor = await this.approveVisitor(visitorId);
      results.push(visitor);
    }
    return results;
  }
}

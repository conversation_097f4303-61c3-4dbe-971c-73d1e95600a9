import { ValueObject } from '@/shared/domain/base/value-object';
import { ValidationException } from '@/shared/application/exceptions/domain-exception';

/**
 * 门禁位置值对象
 */
export interface AccessLocationProps {
  name: string;
  description?: string;
  building?: string;
  floor?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export class AccessLocation extends ValueObject<AccessLocationProps> {
  constructor(props: AccessLocationProps) {
    AccessLocation.validate(props);
    super(props);
  }

  private static validate(props: AccessLocationProps): void {
    const errors: Record<string, string[]> = {};

    // 验证位置名称
    if (!props.name || props.name.trim().length === 0) {
      errors.name = ['位置名称不能为空'];
    } else if (props.name.length > 100) {
      errors.name = ['位置名称长度不能超过100个字符'];
    }

    // 验证坐标
    if (props.coordinates) {
      const { latitude, longitude } = props.coordinates;
      if (latitude < -90 || latitude > 90) {
        errors.coordinates = ['纬度必须在-90到90之间'];
      }
      if (longitude < -180 || longitude > 180) {
        errors.coordinates = [...(errors.coordinates || []), '经度必须在-180到180之间'];
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new ValidationException(errors);
    }
  }

  get name(): string {
    return this._value.name;
  }

  get description(): string | undefined {
    return this._value.description;
  }

  get building(): string | undefined {
    return this._value.building;
  }

  get floor(): string | undefined {
    return this._value.floor;
  }

  get coordinates(): { latitude: number; longitude: number } | undefined {
    return this._value.coordinates;
  }

  /**
   * 获取完整位置描述
   */
  getFullDescription(): string {
    const parts = [this._value.name];
    
    if (this._value.building) {
      parts.unshift(this._value.building);
    }
    
    if (this._value.floor) {
      parts.splice(-1, 0, this._value.floor);
    }

    return parts.join(' - ');
  }

  /**
   * 检查是否在指定建筑内
   */
  isInBuilding(building: string): boolean {
    return this._value.building === building;
  }

  /**
   * 检查是否在指定楼层
   */
  isOnFloor(floor: string): boolean {
    return this._value.floor === floor;
  }

  /**
   * 计算与另一个位置的距离（如果都有坐标）
   */
  distanceTo(other: AccessLocation): number | null {
    if (!this._value.coordinates || !other.coordinates) {
      return null;
    }

    const R = 6371; // 地球半径（公里）
    const dLat = this.toRadians(other.coordinates.latitude - this._value.coordinates.latitude);
    const dLon = this.toRadians(other.coordinates.longitude - this._value.coordinates.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(this._value.coordinates.latitude)) * 
      Math.cos(this.toRadians(other.coordinates.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c * 1000; // 返回米
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 创建主要入口位置
   */
  static mainEntrance(building?: string): AccessLocation {
    return new AccessLocation({
      name: '主入口',
      description: '校园主要入口',
      building: building || '主楼',
      floor: '1楼'
    });
  }

  /**
   * 创建侧门位置
   */
  static sideEntrance(building: string, description?: string): AccessLocation {
    return new AccessLocation({
      name: '侧门',
      description: description || '侧门入口',
      building,
      floor: '1楼'
    });
  }
}

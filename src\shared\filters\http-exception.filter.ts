import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import {
  BaseException,
  BaseExceptionResponse,
} from '../application/exceptions/base.exception';

// HTTP 异常响应类型定义
interface HttpExceptionResponse {
  message?: string | string[];
  error?: string;
  errors?: Array<{
    field: string;
    message: string;
    value?: unknown;
  }>;
  statusCode?: number;
  [key: string]: unknown;
}

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<FastifyReply>();
    const request = ctx.getRequest<FastifyRequest>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errCode = 500;
    let message = '内部服务器错误';
    let data: Record<string, unknown> | undefined = undefined;

    // 处理自定义异常
    if (exception instanceof BaseException) {
      status = exception.getStatus();
      const exceptionResponse =
        exception.getResponse() as BaseExceptionResponse;
      errCode = exceptionResponse.errCode;
      message = exceptionResponse.message;
      data = exceptionResponse.data;
    }
    // 处理 HTTP 异常
    else if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (
        typeof exceptionResponse === 'object' &&
        exceptionResponse !== null
      ) {
        const responseObj = exceptionResponse as HttpExceptionResponse;
        message =
          (typeof responseObj.message === 'string'
            ? responseObj.message
            : '') ||
          responseObj.error ||
          '请求处理失败';

        // 如果是验证错误，提取详细信息
        if (responseObj.errors && Array.isArray(responseObj.errors)) {
          data = {
            validationErrors: responseObj.errors,
            suggestion: '请检查输入数据格式',
          };
          errCode = 400;
        }
      }
    }
    // 处理其他异常
    else {
      this.logger.error('未处理的异常:', exception);
      message = '系统内部错误';
      data = {
        suggestion: '请联系技术支持',
        timestamp: this.formatTimestamp(new Date()),
      };
    }

    // 记录错误日志
    this.logger.error(
      `异常处理: ${request.method} ${request.url} - ${status} ${message}`,
      exception instanceof Error ? exception.stack : undefined,
    );

    // 返回统一的 ApiResponse 格式
    const errorResponse = {
      errCode,
      message,
      data,
      timestamp: this.formatTimestamp(new Date()),
      path: request.url,
    };

    // 使用 Fastify 的 send 方法而不是 json 方法
    response.status(status).send(errorResponse);
  }

  private formatTimestamp(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}

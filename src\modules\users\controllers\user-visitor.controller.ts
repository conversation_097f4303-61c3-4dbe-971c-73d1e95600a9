import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { UserVisitorService } from '../services/user-visitor.service';
import {
  CreateUserVisitorDto,
  UpdateUserVisitorDto,
  QueryUserVisitorDto,
  UserVisitorResponseDto,
} from '../dto/user-visitor.dto';

@ApiTags('访客管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('user-visitors')
export class UserVisitorController {
  constructor(private readonly userVisitorService: UserVisitorService) {}

  @Post()
  @ApiOperation({ summary: '创建访客' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: UserVisitorResponseDto,
  })
  async create(
    @Body() createDto: CreateUserVisitorDto,
  ): Promise<UserVisitorResponseDto> {
    return this.userVisitorService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: '分页查询访客' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserVisitorResponseDto' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' },
      },
    },
  })
  async findMany(@Query() query: QueryUserVisitorDto) {
    return this.userVisitorService.findMany(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询访客' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: UserVisitorResponseDto,
  })
  async findById(@Param('id') id: string): Promise<UserVisitorResponseDto> {
    return this.userVisitorService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新访客信息' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UserVisitorResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserVisitorDto,
  ): Promise<UserVisitorResponseDto> {
    return this.userVisitorService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除访客' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.userVisitorService.delete(id);
  }

  @Delete('batch/:ids')
  @ApiOperation({ summary: '批量删除访客' })
  @ApiResponse({
    status: 200,
    description: '批量删除成功',
  })
  async batchDelete(@Param('ids') ids: string): Promise<void> {
    const idArray = ids.split(',');
    return this.userVisitorService.batchDelete(idArray);
  }
}

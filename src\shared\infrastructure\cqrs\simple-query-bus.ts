import { Injectable, Logger } from '@nestjs/common';
import { 
  Query, 
  QueryBus, 
  QueryHandler, 
  QueryResult,
  PaginatedQueryResult 
} from '@/shared/application/cqrs/query';

/**
 * 简单查询总线实现
 */
@Injectable()
export class SimpleQueryBus implements QueryBus {
  private readonly logger = new Logger(SimpleQueryBus.name);
  private readonly handlers = new Map<string, QueryHandler<any, any>>();
  private readonly cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  /**
   * 执行查询
   */
  async execute<TQuery extends Query, TResult = any>(
    query: TQuery
  ): Promise<TResult> {
    const queryName = query.constructor.name;
    this.logger.debug(`Executing query: ${queryName}`, { 
      queryId: query.queryId,
      timestamp: query.timestamp 
    });

    try {
      // 1. 检查缓存
      const cachedResult = this.getCachedResult<TResult>(query);
      if (cachedResult !== null) {
        this.logger.debug(`Query result returned from cache: ${queryName}`, {
          queryId: query.queryId
        });
        return cachedResult;
      }

      // 2. 获取处理器
      const handler = this.getHandler<TQuery, TResult>(queryName);

      // 3. 执行查询
      const startTime = Date.now();
      const result = await handler.handle(query);
      const executionTime = Date.now() - startTime;

      // 4. 缓存结果（如果适用）
      this.cacheResult(query, result);

      this.logger.debug(`Query executed successfully: ${queryName}`, {
        queryId: query.queryId,
        executionTime: `${executionTime}ms`
      });

      return result;
    } catch (error) {
      this.logger.error(`Query execution failed: ${queryName}`, {
        queryId: query.queryId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * 注册查询处理器
   */
  register<TQuery extends Query, TResult = any>(
    queryType: new (...args: any[]) => TQuery,
    handler: QueryHandler<TQuery, TResult>
  ): void {
    const queryName = queryType.name;
    
    if (this.handlers.has(queryName)) {
      throw new Error(`Query handler for ${queryName} is already registered`);
    }

    this.handlers.set(queryName, handler);
    this.logger.debug(`Registered query handler: ${queryName}`);
  }

  /**
   * 批量注册处理器
   */
  registerHandlers(handlers: Array<{
    queryType: new (...args: any[]) => Query;
    handler: QueryHandler<any, any>;
  }>): void {
    handlers.forEach(({ queryType, handler }) => {
      this.register(queryType, handler);
    });
  }

  /**
   * 获取所有已注册的查询类型
   */
  getRegisteredQueries(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * 检查查询是否已注册
   */
  isQueryRegistered(queryName: string): boolean {
    return this.handlers.has(queryName);
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
    this.logger.debug(`Cache cleared${pattern ? ` for pattern: ${pattern}` : ''}`);
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; timestamp: number; ttl: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, value]) => ({
      key,
      timestamp: value.timestamp,
      ttl: value.ttl
    }));

    return {
      size: this.cache.size,
      hitRate: 0, // 简化实现，实际应该跟踪命中率
      entries
    };
  }

  /**
   * 获取查询处理器
   */
  private getHandler<TQuery extends Query, TResult>(
    queryName: string
  ): QueryHandler<TQuery, TResult> {
    const handler = this.handlers.get(queryName);
    
    if (!handler) {
      throw new Error(`No handler registered for query: ${queryName}`);
    }

    return handler;
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult<TResult>(query: Query): TResult | null {
    const cacheKey = this.generateCacheKey(query);
    const cached = this.cache.get(cacheKey);

    if (!cached) {
      return null;
    }

    // 检查是否过期
    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(cacheKey);
      return null;
    }

    return cached.data;
  }

  /**
   * 缓存结果
   */
  private cacheResult<TResult>(query: Query, result: TResult): void {
    // 只缓存成功的查询结果，且不缓存分页查询的大结果集
    if (this.shouldCache(query, result)) {
      const cacheKey = this.generateCacheKey(query);
      const ttl = this.getCacheTTL(query);

      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now(),
        ttl
      });
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(query: Query): string {
    const queryName = query.constructor.name;
    const queryData = JSON.stringify(query);
    return `${queryName}:${Buffer.from(queryData).toString('base64')}`;
  }

  /**
   * 判断是否应该缓存
   */
  private shouldCache(query: Query, result: any): boolean {
    // 简化的缓存策略
    // 实际项目中可以根据查询类型、结果大小等因素决定
    
    // 不缓存空结果
    if (!result) return false;

    // 不缓存大型结果集
    if (Array.isArray(result) && result.length > 100) return false;

    // 不缓存分页查询的大结果
    if (result instanceof PaginatedQueryResult && result.items.length > 50) return false;

    return true;
  }

  /**
   * 获取缓存TTL
   */
  private getCacheTTL(query: Query): number {
    // 简化的TTL策略，实际项目中可以根据查询类型设置不同的TTL
    const queryName = query.constructor.name;

    // 统计类查询缓存时间较长
    if (queryName.includes('Statistics') || queryName.includes('Count')) {
      return 5 * 60 * 1000; // 5分钟
    }

    // 列表查询缓存时间中等
    if (queryName.includes('List') || queryName.includes('Search')) {
      return 2 * 60 * 1000; // 2分钟
    }

    // 详情查询缓存时间较短
    return 30 * 1000; // 30秒
  }
}

/**
 * 查询缓存装饰器
 */
export function QueryCache(ttl: number = 60000) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const cache = new Map<string, { data: any; timestamp: number }>();

    descriptor.value = async function (...args: any[]) {
      const cacheKey = JSON.stringify(args);
      const cached = cache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < ttl) {
        Logger.debug(`Query cache hit: ${propertyKey}`);
        return cached.data;
      }

      const result = await originalMethod.apply(this, args);
      cache.set(cacheKey, { data: result, timestamp: Date.now() });

      Logger.debug(`Query cache miss: ${propertyKey}`);
      return result;
    };

    return descriptor;
  };
}

/**
 * 查询性能监控装饰器
 */
export function QueryPerformance() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      try {
        const result = await originalMethod.apply(this, args);
        const executionTime = Date.now() - startTime;
        
        if (executionTime > 1000) { // 超过1秒的查询记录警告
          Logger.warn(`Slow query detected: ${propertyKey}`, {
            executionTime: `${executionTime}ms`,
            args: args.length
          });
        }
        
        return result;
      } catch (error) {
        const executionTime = Date.now() - startTime;
        Logger.error(`Query failed: ${propertyKey}`, {
          executionTime: `${executionTime}ms`,
          error: error.message
        });
        throw error;
      }
    };

    return descriptor;
  };
}

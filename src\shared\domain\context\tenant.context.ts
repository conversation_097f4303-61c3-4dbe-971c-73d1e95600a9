import { AsyncLocalStorage } from 'async_hooks';
import { Injectable, Scope } from '@nestjs/common';

/**
 * 租户信息接口
 */
export interface TenantInfo {
  id: string;
  name: string;
  code: string;
  status: 'active' | 'inactive' | 'suspended';
  config?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 租户上下文接口
 */
export interface TenantContextData {
  tenantId: string;
  tenantInfo?: TenantInfo;
  userId?: string;
  permissions?: string[];
  metadata?: Record<string, any>;
}

/**
 * 租户上下文管理器
 * 使用 AsyncLocalStorage 在异步调用链中保持租户信息
 */
@Injectable({ scope: Scope.DEFAULT })
export class TenantContext {
  private static readonly storage = new AsyncLocalStorage<TenantContextData>();

  /**
   * 设置当前租户上下文
   */
  static run<T>(context: TenantContextData, callback: () => T): T {
    return this.storage.run(context, callback);
  }

  /**
   * 获取当前租户ID
   */
  static getCurrentTenantId(): string | undefined {
    const context = this.storage.getStore();
    return context?.tenantId;
  }

  /**
   * 获取当前租户信息
   */
  static getCurrentTenantInfo(): TenantInfo | undefined {
    const context = this.storage.getStore();
    return context?.tenantInfo;
  }

  /**
   * 获取当前用户ID
   */
  static getCurrentUserId(): string | undefined {
    const context = this.storage.getStore();
    return context?.userId;
  }

  /**
   * 获取当前用户权限
   */
  static getCurrentUserPermissions(): string[] {
    const context = this.storage.getStore();
    return context?.permissions || [];
  }

  /**
   * 获取完整的上下文数据
   */
  static getCurrentContext(): TenantContextData | undefined {
    return this.storage.getStore();
  }

  /**
   * 检查是否有租户上下文
   */
  static hasContext(): boolean {
    return this.storage.getStore() !== undefined;
  }

  /**
   * 检查当前租户是否匹配
   */
  static isTenantMatch(tenantId: string): boolean {
    const currentTenantId = this.getCurrentTenantId();
    return currentTenantId === tenantId;
  }

  /**
   * 验证租户权限
   */
  static validateTenantAccess(requiredTenantId: string): void {
    const currentTenantId = this.getCurrentTenantId();

    if (!currentTenantId) {
      throw new Error('No tenant context found');
    }

    if (currentTenantId !== requiredTenantId) {
      throw new Error(
        `Tenant access denied: required ${requiredTenantId}, current ${currentTenantId}`,
      );
    }
  }

  /**
   * 创建租户上下文数据
   */
  static createContext(
    tenantId: string,
    options?: {
      tenantInfo?: TenantInfo;
      userId?: string;
      permissions?: string[];
      metadata?: Record<string, any>;
    },
  ): TenantContextData {
    return {
      tenantId,
      tenantInfo: options?.tenantInfo,
      userId: options?.userId,
      permissions: options?.permissions,
      metadata: options?.metadata,
    };
  }

  /**
   * 获取默认租户ID（用于系统级操作）
   */
  static getDefaultTenantId(): string {
    return 'system';
  }

  /**
   * 检查是否为系统租户
   */
  static isSystemTenant(): boolean {
    const currentTenantId = this.getCurrentTenantId();
    return currentTenantId === this.getDefaultTenantId();
  }

  /**
   * 在系统租户上下文中执行操作
   */
  static runAsSystem<T>(callback: () => T): T {
    const systemContext = this.createContext(this.getDefaultTenantId());
    return this.run(systemContext, callback);
  }

  /**
   * 切换租户上下文
   */
  static switchTenant<T>(tenantId: string, callback: () => T): T {
    const currentContext = this.getCurrentContext();
    const newContext = this.createContext(tenantId, {
      userId: currentContext?.userId,
      permissions: currentContext?.permissions,
      metadata: currentContext?.metadata,
    });

    return this.run(newContext, callback);
  }

  /**
   * 从JWT载荷中提取租户信息
   */
  static extractFromJwtPayload(payload: any): TenantContextData {
    return this.createContext(payload.tenantId || this.getDefaultTenantId(), {
      userId: payload.sub || payload.userId,
      permissions: payload.permissions || [],
      metadata: {
        userType: payload.userType,
        roles: payload.roles,
      },
    });
  }
}

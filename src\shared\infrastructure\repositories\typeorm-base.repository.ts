import { Repository, FindOptionsWhere, FindManyOptions, DeepPartial } from 'typeorm';
import { Logger, Inject, Optional } from '@nestjs/common';
import { AggregateRoot } from '@/shared/domain/base/aggregate-root';
import { BaseRepository, AuditContext, AuditAction } from '@/shared/domain/repositories/base-repository';
import { EntityNotFoundException } from '@/shared/application/exceptions/domain-exception';
import { AuditService } from '@/shared/application/services/audit.service';
import { BaseEntity } from '@/shared/domain/base/base-entity';
import { TenantContext } from '@/shared/domain/context/tenant.context';

/**
 * TypeORM基础仓储实现
 */
export abstract class TypeOrmBaseRepository<T extends AggregateRoot & BaseEntity> implements BaseRepository<T> {
  protected readonly logger: Logger;

  constructor(
    protected readonly repository: Repository<T>,
    loggerContext?: string,
    @Optional() protected readonly auditService?: AuditService,
  ) {
    this.logger = new Logger(loggerContext || this.constructor.name);
  }

  /**
   * 根据ID查找聚合
   */
  async findById(id: string): Promise<T | null> {
    try {
      const whereCondition = this.buildTenantAwareWhereCondition({ id } as any);

      const entity = await this.repository.findOne({
        where: whereCondition,
      });

      if (entity) {
        this.logger.debug(`Entity found by ID: ${id}`);
      }

      return entity;
    } catch (error) {
      this.logger.error(`Error finding entity by ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 保存聚合
   */
  async save(aggregate: T, auditContext?: AuditContext): Promise<T> {
    try {
      // 应用审计信息
      if (auditContext && this.auditService) {
        this.auditService.applyAudit(aggregate, auditContext);
      }

      const savedEntity = await this.repository.save(aggregate);

      // 记录审计日志
      if (auditContext && this.auditService) {
        await this.auditService.logAudit({
          ...auditContext,
          entityName: this.getEntityName(),
          entityId: savedEntity.id,
        });
      }

      this.logger.debug(`Entity saved: ${savedEntity.id}`);
      return savedEntity;
    } catch (error) {
      this.logger.error(`Error saving entity: ${aggregate.id}`, error);
      throw error;
    }
  }

  /**
   * 删除聚合
   */
  async delete(id: string, auditContext?: AuditContext): Promise<void> {
    try {
      const result = await this.repository.softDelete(id);

      if (result.affected === 0) {
        throw new EntityNotFoundException(this.getEntityName(), id);
      }

      // 记录审计日志
      if (auditContext && this.auditService) {
        await this.auditService.logAudit({
          ...auditContext,
          action: AuditAction.DELETE,
          entityName: this.getEntityName(),
          entityId: id,
        });
      }

      this.logger.debug(`Entity deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting entity: ${id}`, error);
      throw error;
    }
  }

  /**
   * 软删除聚合
   */
  async softDelete(id: string, auditContext?: AuditContext): Promise<void> {
    try {
      const entity = await this.findById(id);
      if (!entity) {
        throw new EntityNotFoundException(this.getEntityName(), id);
      }

      // 应用软删除审计
      if (auditContext && this.auditService) {
        const deleteContext: AuditContext = {
          ...auditContext,
          action: AuditAction.DELETE,
        };
        this.auditService.applyAudit(entity, deleteContext);
        await this.repository.save(entity);

        // 记录审计日志
        await this.auditService.logAudit({
          ...deleteContext,
          entityName: this.getEntityName(),
          entityId: id,
        });
      } else {
        // 如果没有审计服务，直接执行软删除
        entity.softDelete();
        await this.repository.save(entity);
      }

      this.logger.debug(`Entity soft deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Error soft deleting entity: ${id}`, error);
      throw error;
    }
  }

  /**
   * 恢复删除的聚合
   */
  async restore(id: string, auditContext?: AuditContext): Promise<void> {
    try {
      // 查找包括已删除的实体
      const entity = await this.repository.findOne({
        where: { id } as FindOptionsWhere<T>,
        withDeleted: true,
      });

      if (!entity) {
        throw new EntityNotFoundException(this.getEntityName(), id);
      }

      // 应用恢复审计
      if (auditContext && this.auditService) {
        const restoreContext: AuditContext = {
          ...auditContext,
          action: AuditAction.RESTORE,
        };
        this.auditService.applyAudit(entity, restoreContext);
        await this.repository.save(entity);

        // 记录审计日志
        await this.auditService.logAudit({
          ...restoreContext,
          entityName: this.getEntityName(),
          entityId: id,
        });
      } else {
        // 如果没有审计服务，直接执行恢复
        entity.restore();
        await this.repository.save(entity);
      }

      this.logger.debug(`Entity restored: ${id}`);
    } catch (error) {
      this.logger.error(`Error restoring entity: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查聚合是否存在
   */
  async exists(id: string): Promise<boolean> {
    try {
      const count = await this.repository.count({
        where: { id } as FindOptionsWhere<T>,
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking entity existence: ${id}`, error);
      throw error;
    }
  }

  /**
   * 批量保存聚合
   */
  async saveMany(aggregates: T[]): Promise<T[]> {
    if (aggregates.length === 0) {
      return [];
    }

    try {
      const savedEntities = await this.repository.save(aggregates);
      this.logger.debug(`Batch saved ${savedEntities.length} entities`);
      return savedEntities;
    } catch (error) {
      this.logger.error(`Error batch saving entities`, error);
      throw error;
    }
  }

  /**
   * 根据条件查找多个聚合
   */
  async findMany(options: FindManyOptions<T>): Promise<T[]> {
    try {
      const entities = await this.repository.find(options);
      this.logger.debug(`Found ${entities.length} entities`);
      return entities;
    } catch (error) {
      this.logger.error(`Error finding entities`, error);
      throw error;
    }
  }

  /**
   * 根据条件查找单个聚合
   */
  async findOne(options: FindManyOptions<T>): Promise<T | null> {
    try {
      const entity = await this.repository.findOne(options);
      return entity;
    } catch (error) {
      this.logger.error(`Error finding entity`, error);
      throw error;
    }
  }

  /**
   * 计数
   */
  async count(options?: FindManyOptions<T>): Promise<number> {
    try {
      const count = await this.repository.count(options);
      return count;
    } catch (error) {
      this.logger.error(`Error counting entities`, error);
      throw error;
    }
  }

  /**
   * 分页查询
   */
  async findWithPagination(
    options: FindManyOptions<T>,
    page: number = 1,
    pageSize: number = 10
  ): Promise<{
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * pageSize;
      const [data, total] = await this.repository.findAndCount({
        ...options,
        skip,
        take: pageSize,
      });

      const totalPages = Math.ceil(total / pageSize);

      this.logger.debug(`Paginated query: page ${page}, size ${pageSize}, total ${total}`);

      return {
        data,
        total,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error in paginated query`, error);
      throw error;
    }
  }



  /**
   * 批量软删除
   */
  async softDeleteMany(ids: string[]): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    try {
      const result = await this.repository.softDelete(ids);
      this.logger.debug(`Batch soft deleted ${result.affected} entities`);
    } catch (error) {
      this.logger.error(`Error batch soft deleting entities`, error);
      throw error;
    }
  }



  /**
   * 创建新实体（不保存）
   */
  create(entityData: DeepPartial<T>): T {
    return this.repository.create(entityData);
  }

  /**
   * 创建多个新实体（不保存）
   */
  createMany(entitiesData: DeepPartial<T>[]): T[] {
    return this.repository.create(entitiesData);
  }

  /**
   * 获取原始仓储
   */
  getRepository(): Repository<T> {
    return this.repository;
  }

  /**
   * 执行原始查询
   */
  async query(sql: string, parameters?: any[]): Promise<any> {
    try {
      const result = await this.repository.query(sql, parameters);
      this.logger.debug(`Raw query executed`, { sql: sql.substring(0, 100) });
      return result;
    } catch (error) {
      this.logger.error(`Error executing raw query`, error);
      throw error;
    }
  }

  /**
   * 开始事务
   */
  async transaction<R>(fn: (repository: Repository<T>) => Promise<R>): Promise<R> {
    return await this.repository.manager.transaction(async (manager) => {
      const transactionalRepository = manager.getRepository(this.repository.target);
      return await fn(transactionalRepository);
    });
  }

  /**
   * 获取实体名称（用于错误消息）
   */
  protected abstract getEntityName(): string;

  /**
   * 记录性能指标
   */
  protected logPerformance(operation: string, startTime: number): void {
    const duration = Date.now() - startTime;
    if (duration > 1000) { // 超过1秒的操作记录警告
      this.logger.warn(`Slow ${operation} operation detected`, {
        duration: `${duration}ms`,
        entity: this.getEntityName(),
      });
    } else {
      this.logger.debug(`${operation} operation completed`, {
        duration: `${duration}ms`,
        entity: this.getEntityName(),
      });
    }
  }

  /**
   * 构建查询条件
   */
  protected buildWhereConditions(criteria: Record<string, any>): FindOptionsWhere<T> {
    const where: FindOptionsWhere<T> = {};

    Object.keys(criteria).forEach(key => {
      const value = criteria[key];
      if (value !== undefined && value !== null) {
        (where as any)[key] = value;
      }
    });

    return where;
  }

  /**
   * 构建排序条件
   */
  protected buildOrderConditions(sortBy?: string, sortOrder?: 'ASC' | 'DESC'): Record<string, 'ASC' | 'DESC'> {
    if (!sortBy) {
      return { createdAt: 'DESC' } as any;
    }

    return { [sortBy]: sortOrder || 'DESC' } as any;
  }

  /**
   * 构建租户感知的查询条件
   */
  protected buildTenantAwareWhereCondition(baseCondition: FindOptionsWhere<T>): FindOptionsWhere<T> {
    const currentTenantId = TenantContext.getCurrentTenantId();

    if (!currentTenantId) {
      // 如果没有租户上下文，使用默认租户
      return {
        ...baseCondition,
        tenantId: TenantContext.getDefaultTenantId(),
      } as FindOptionsWhere<T>;
    }

    // 系统租户可以访问所有数据
    if (currentTenantId === TenantContext.getDefaultTenantId()) {
      return baseCondition;
    }

    // 普通租户只能访问自己的数据
    return {
      ...baseCondition,
      tenantId: currentTenantId,
    } as FindOptionsWhere<T>;
  }

  /**
   * 验证实体的租户权限
   */
  protected validateEntityTenantAccess(entity: T): void {
    const currentTenantId = TenantContext.getCurrentTenantId();

    if (!currentTenantId) {
      throw new Error('No tenant context found');
    }

    // 系统租户可以访问所有数据
    if (currentTenantId === TenantContext.getDefaultTenantId()) {
      return;
    }

    // 检查实体是否属于当前租户
    if (entity instanceof BaseEntity && !entity.belongsToTenant(currentTenantId)) {
      throw new Error(`Access denied: entity belongs to tenant ${entity.getTenantId()}, current tenant is ${currentTenantId}`);
    }
  }
}

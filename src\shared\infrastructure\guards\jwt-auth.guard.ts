/**
 * 共享JWT认证守卫
 * 重新导出身份访问上下文中的JWT认证守卫，供全局使用
 */

// 从身份访问上下文中导出JWT认证守卫
export { 
  JwtAuthGuard,
  Public,
  IS_PUBLIC_KEY,
  RequestUser,
} from '@/contexts/identity-access/infrastructure/guards/jwt-auth.guard';

// 从身份访问上下文中导出权限相关装饰器和守卫
export {
  PermissionsGuard,
  RolesGuard,
  RequirePermissions,
  RequireRoles,
  CurrentUser,
  PERMISSIONS_KEY,
  ROLES_KEY,
} from '@/contexts/identity-access/infrastructure/guards/permissions.guard';

// 多租户相关守卫已经在当前目录下
export {
  TenantGuard,
  TenantResourceGuard,
} from './tenant.guard';

// 多租户装饰器
export {
  TenantIsolation,
  CrossTenant,
  TenantAdmin,
  SuperAdmin,
  RequireTenant,
  AllowTenants,
  SystemOnly,
} from '../../domain/decorators/tenant.decorator';

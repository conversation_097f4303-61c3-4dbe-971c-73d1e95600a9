import { Injectable } from '@nestjs/common';
import { BaseEntity } from '../../domain/base/base-entity';
import {
  AuditAction,
  AuditContext as BaseAuditContext,
} from '../../domain/repositories/base-repository';

/**
 * 扩展的审计上下文
 */
export interface AuditContext extends BaseAuditContext {
  entityName?: string;
  entityId?: string;
  changes?: Record<string, any>;
}

/**
 * 审计服务
 * 负责处理实体的审计字段填充和审计日志记录
 */
@Injectable()
export class AuditService {
  /**
   * 应用审计信息到实体
   */
  applyAudit(entity: BaseEntity, context: AuditContext): void {
    if (!entity || !context.userId) {
      return;
    }

    switch (context.action) {
      case AuditAction.CREATE:
        this.applyCreateAudit(entity, context.userId);
        break;
      case AuditAction.UPDATE:
        this.applyUpdateAudit(entity, context.userId);
        break;
      case AuditAction.DELETE:
        this.applyDeleteAudit(entity, context.userId);
        break;
      case AuditAction.RESTORE:
        this.applyRestoreAudit(entity, context.userId);
        break;
    }
  }

  /**
   * 批量应用审计信息
   */
  applyBatchAudit(entities: BaseEntity[], context: AuditContext): void {
    entities.forEach((entity) => this.applyAudit(entity, context));
  }

  /**
   * 应用创建审计
   */
  private applyCreateAudit(entity: BaseEntity, userId: string): void {
    if (!entity.getCreatedBy()) {
      entity.setCreatedBy(userId);
    }
    // 创建时也设置更新者
    entity.setUpdatedBy(userId);
  }

  /**
   * 应用更新审计
   */
  private applyUpdateAudit(entity: BaseEntity, userId: string): void {
    entity.setUpdatedBy(userId);
  }

  /**
   * 应用删除审计
   */
  private applyDeleteAudit(entity: BaseEntity, userId: string): void {
    entity.softDelete(userId);
  }

  /**
   * 应用恢复审计
   */
  private applyRestoreAudit(entity: BaseEntity, userId: string): void {
    entity.restore();
    entity.setUpdatedBy(userId);
  }

  /**
   * 从请求中提取用户ID
   */
  extractUserIdFromRequest(request: any): string | undefined {
    // 从JWT认证中获取用户ID
    return request.user?.id;
  }

  /**
   * 创建审计上下文
   */
  createAuditContext(
    action: AuditAction,
    userId?: string,
    entityName?: string,
    entityId?: string,
    changes?: Record<string, any>,
    metadata?: Record<string, any>,
  ): AuditContext {
    return {
      userId,
      action,
      entityName,
      entityId,
      changes,
      metadata,
    };
  }

  /**
   * 记录审计日志（可扩展）
   */
  async logAudit(context: AuditContext): Promise<void> {
    // 这里可以实现审计日志的记录逻辑
    // 例如写入数据库、发送到日志服务等
    console.log('Audit Log:', {
      timestamp: new Date().toISOString(),
      userId: context.userId,
      action: context.action,
      entityName: context.entityName,
      entityId: context.entityId,
      changes: context.changes,
      metadata: context.metadata,
    });
  }

  /**
   * 比较实体变更
   */
  compareEntityChanges(
    oldEntity: BaseEntity,
    newEntity: BaseEntity,
  ): Record<string, any> {
    const changes: Record<string, any> = {};

    // 这里可以实现更复杂的变更比较逻辑
    // 暂时返回空对象，后续可以扩展

    return changes;
  }

  /**
   * 验证审计权限
   */
  validateAuditPermission(
    userId: string,
    action: AuditAction,
    entityName: string,
  ): boolean {
    // 这里可以实现审计权限验证逻辑
    // 例如检查用户是否有权限对特定实体执行特定操作

    // 暂时返回true，后续可以根据业务需求实现
    return true;
  }
}

/**
 * 审计装饰器工厂
 */
export function WithAudit(action: AuditAction) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
  ) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 这里可以在方法执行前后添加审计逻辑
      const result = await method.apply(this, args);

      // 如果需要，可以在这里记录审计日志

      return result;
    };
  };
}

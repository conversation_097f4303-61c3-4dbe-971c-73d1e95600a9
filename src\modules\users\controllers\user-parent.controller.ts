import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { UserParentService } from '../services/user-parent.service';
import {
  CreateUserParentDto,
  UpdateUserParentDto,
  QueryUserParentDto,
  UserParentResponseDto,
} from '../dto/user-parent.dto';

@ApiTags('家长管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('user-parents')
export class UserParentController {
  constructor(private readonly userParentService: UserParentService) {}

  @Post()
  @ApiOperation({ summary: '创建家长' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: UserParentResponseDto,
  })
  async create(
    @Body() createDto: CreateUserParentDto,
  ): Promise<UserParentResponseDto> {
    return this.userParentService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: '分页查询家长' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserParentResponseDto' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' },
      },
    },
  })
  async findMany(@Query() query: QueryUserParentDto) {
    return this.userParentService.findMany(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询家长' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: UserParentResponseDto,
  })
  async findById(@Param('id') id: string): Promise<UserParentResponseDto> {
    return this.userParentService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新家长信息' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UserParentResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserParentDto,
  ): Promise<UserParentResponseDto> {
    return this.userParentService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除家长' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.userParentService.delete(id);
  }

  @Delete('batch/:ids')
  @ApiOperation({ summary: '批量删除家长' })
  @ApiResponse({
    status: 200,
    description: '批量删除成功',
  })
  async batchDelete(@Param('ids') ids: string): Promise<void> {
    const idArray = ids.split(',');
    return this.userParentService.batchDelete(idArray);
  }
}

import { DomainEvent } from '@/shared/domain/events/domain-event';
import { AccessLocation } from '../value-objects/access-location';
import { AccessTimeRange } from '../value-objects/access-time-range';

/**
 * 门禁申请批准事件
 */
export class AccessRequestApprovedEvent extends DomainEvent {
  constructor(
    public readonly requestId: string,
    public readonly userId: string,
    public readonly approverId: string,
    public readonly timeRange: AccessTimeRange,
    public readonly location: AccessLocation,
    public readonly remark?: string,
  ) {
    super(requestId);
  }

  getEventName(): string {
    return 'access-request.approved';
  }

  getEventData(): Record<string, any> {
    return {
      requestId: this.requestId,
      userId: this.userId,
      approverId: this.approverId,
      timeRange: {
        startTime: this.timeRange.startTime.toISOString(),
        endTime: this.timeRange.endTime.toISOString(),
        durationInMinutes: this.timeRange.getDurationInMinutes(),
      },
      location: {
        name: this.location.name,
        description: this.location.description,
        building: this.location.building,
        floor: this.location.floor,
        fullDescription: this.location.getFullDescription(),
      },
      remark: this.remark,
    };
  }
}

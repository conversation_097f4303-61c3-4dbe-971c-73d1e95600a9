import { DomainEvent } from '@/shared/domain/events/domain-event';
import { AccessRequestType } from '../aggregates/access-request.aggregate';
import { AccessLocation } from '../value-objects/access-location';
import { AccessTimeRange } from '../value-objects/access-time-range';

/**
 * 门禁申请创建事件
 */
export class AccessRequestCreatedEvent extends DomainEvent {
  constructor(
    public readonly requestId: string,
    public readonly userId: string,
    public readonly requestType: AccessRequestType,
    public readonly timeRange: AccessTimeRange,
    public readonly location: AccessLocation,
    public readonly reason: string,
  ) {
    super(requestId);
  }

  getEventName(): string {
    return 'access-request.created';
  }

  getEventData(): Record<string, any> {
    return {
      requestId: this.requestId,
      userId: this.userId,
      requestType: this.requestType,
      timeRange: {
        startTime: this.timeRange.startTime.toISOString(),
        endTime: this.timeRange.endTime.toISOString(),
        durationInMinutes: this.timeRange.getDurationInMinutes(),
      },
      location: {
        name: this.location.name,
        description: this.location.description,
        building: this.location.building,
        floor: this.location.floor,
        fullDescription: this.location.getFullDescription(),
      },
      reason: this.reason,
    };
  }
}

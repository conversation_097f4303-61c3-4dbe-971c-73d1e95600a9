import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { FastifyRequest, FastifyReply } from 'fastify';
import { PinoLoggerService } from '../logging/pino-logger.service';

/**
 * HTTP 请求日志中间件
 * 记录所有 HTTP 请求的详细信息
 */
@Injectable()
export class HttpLoggerMiddleware implements NestMiddleware {
  constructor(private readonly logger: PinoLoggerService) {
    this.logger.setContext('HttpLogger');
  }

  use(req: Request | FastifyRequest, res: Response | FastifyReply, next: NextFunction): void {
    const startTime = Date.now();

    // 获取请求信息
    const { method, url } = req;
    const userAgent = req.headers['user-agent'] || '';
    const ip = this.getClientIP(req);
    const contentLength = req.headers['content-length'] || '0';

    // 记录请求开始
    this.logger.debug({
      method,
      url,
      ip,
      userAgent,
      contentLength,
      headers: this.sanitizeHeaders(req.headers),
    }, `Incoming request: ${method} ${url}`);

    // 监听响应完成
    const originalEnd = this.getResponseEndMethod(res);
    const self = this;

    this.setResponseEndMethod(res, function(this: any, ...args: any[]) {
      const responseTime = Date.now() - startTime;
      const statusCode = this.statusCode || res.statusCode;
      const responseSize = this.get?.('content-length') || this.getHeader?.('content-length') || '0';

      // 记录响应完成
      self.logger.logHttpRequest(req, { statusCode }, responseTime);

      // 根据状态码和响应时间记录不同级别的日志
      if (statusCode >= 500) {
        self.logger.error({
          method,
          url,
          statusCode,
          responseTime: `${responseTime}ms`,
          ip,
          userAgent,
          responseSize,
        }, `Server error: ${method} ${url} ${statusCode} - ${responseTime}ms`);
      } else if (statusCode >= 400) {
        self.logger.warn({
          method,
          url,
          statusCode,
          responseTime: `${responseTime}ms`,
          ip,
          userAgent,
          responseSize,
        }, `Client error: ${method} ${url} ${statusCode} - ${responseTime}ms`);
      } else if (responseTime > 1000) {
        self.logger.warn({
          method,
          url,
          statusCode,
          responseTime: `${responseTime}ms`,
          ip,
          userAgent,
          responseSize,
        }, `Slow request: ${method} ${url} ${statusCode} - ${responseTime}ms`);
      } else {
        self.logger.debug({
          method,
          url,
          statusCode,
          responseTime: `${responseTime}ms`,
          ip,
          userAgent,
          responseSize,
        }, `Request completed: ${method} ${url} ${statusCode} - ${responseTime}ms`);
      }

      // 调用原始的 end 方法
      return originalEnd.apply(this, args);
    });

    next();
  }

  /**
   * 获取客户端 IP 地址
   */
  private getClientIP(req: Request | FastifyRequest): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
      (req.headers['x-real-ip'] as string) ||
      (req as any).connection?.remoteAddress ||
      (req as any).socket?.remoteAddress ||
      (req as any).ip ||
      'unknown'
    );
  }

  /**
   * 清理敏感的请求头信息
   */
  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
      'password',
    ];

    const sanitized = { ...headers };

    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  /**
   * 获取响应的 end 方法（兼容 Express 和 Fastify）
   */
  private getResponseEndMethod(res: Response | FastifyReply): Function {
    if ('end' in res) {
      // Express Response
      return (res as Response).end;
    } else {
      // Fastify Reply
      return (res as FastifyReply).send;
    }
  }

  /**
   * 设置响应的 end 方法（兼容 Express 和 Fastify）
   */
  private setResponseEndMethod(res: Response | FastifyReply, newMethod: Function): void {
    if ('end' in res) {
      // Express Response
      (res as any).end = newMethod;
    } else {
      // Fastify Reply
      (res as any).send = newMethod;
    }
  }
}

/**
 * HTTP 请求日志拦截器
 * 作为拦截器使用的版本
 */
@Injectable()
export class HttpLoggerInterceptor {
  constructor(private readonly logger: PinoLoggerService) {
    this.logger.setContext('HttpInterceptor');
  }

  intercept(context: any, next: any): any {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    return next.handle().pipe(
      // 可以在这里添加 RxJS 操作符来处理响应
      // tap(() => {
      //   const responseTime = Date.now() - startTime;
      //   this.logger.logHttpRequest(request, response, responseTime);
      // })
    );
  }
}

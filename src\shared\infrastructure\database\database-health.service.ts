import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

/**
 * 数据库健康检查服务
 */
@Injectable()
export class DatabaseHealthService {
  private readonly logger = new Logger(DatabaseHealthService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 检查数据库连接状态
   */
  async checkConnection(): Promise<{
    isConnected: boolean;
    error?: string;
    details?: any;
  }> {
    try {
      if (!this.dataSource.isInitialized) {
        return {
          isConnected: false,
          error: 'DataSource is not initialized',
        };
      }

      // 执行简单查询测试连接
      await this.dataSource.query('SELECT 1');

      this.logger.log('Database connection is healthy');
      return {
        isConnected: true,
        details: {
          database: this.dataSource.options.database,
          host: (this.dataSource.options as any).host,
          port: (this.dataSource.options as any).port,
        },
      };
    } catch (error) {
      this.logger.error('Database connection failed', error);
      return {
        isConnected: false,
        error: error.message,
        details: {
          code: error.code,
          errno: error.errno,
          sqlState: error.sqlState,
        },
      };
    }
  }

  /**
   * 获取数据库信息
   */
  async getDatabaseInfo(): Promise<any> {
    try {
      const result = await this.dataSource.query(`
        SELECT
          VERSION() as version,
          DATABASE() as current_database,
          USER() as current_user,
          NOW() as current_time
      `);
      return result[0];
    } catch (error) {
      this.logger.error('Failed to get database info', error);
      throw error;
    }
  }

  /**
   * 检查表是否存在
   */
  async checkTables(): Promise<string[]> {
    try {
      const result = await this.dataSource.query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
      `);
      return result.map((row: any) => row.TABLE_NAME);
    } catch (error) {
      this.logger.error('Failed to check tables', error);
      return [];
    }
  }
}

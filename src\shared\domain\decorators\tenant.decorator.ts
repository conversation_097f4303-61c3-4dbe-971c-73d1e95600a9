import { SetMetadata } from '@nestjs/common';
import { TenantContext } from '../context/tenant.context';

/**
 * 租户装饰器 - 用于方法级别的租户验证
 */
export function RequireTenant(tenantId?: string) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
  ) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      if (tenantId) {
        TenantContext.validateTenantAccess(tenantId);
      } else if (!TenantContext.hasContext()) {
        throw new Error('Tenant context is required');
      }

      return method.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 多租户装饰器 - 允许访问多个租户
 */
export function AllowTenants(...tenantIds: string[]) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor,
  ) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const currentTenantId = TenantContext.getCurrentTenantId();

      if (!currentTenantId) {
        throw new Error('Tenant context is required');
      }

      if (!tenantIds.includes(currentTenantId)) {
        throw new Error(
          `Tenant access denied: ${currentTenantId} not in allowed list`,
        );
      }

      return method.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 系统级操作装饰器 - 只允许系统租户访问
 */
export function SystemOnly() {
  return RequireTenant(TenantContext.getDefaultTenantId());
}

/**
 * 租户隔离装饰器 - 用于控制器或方法级别
 */
export const TENANT_ISOLATION_KEY = 'tenant_isolation';
export const TenantIsolation = (enabled: boolean = true) =>
  SetMetadata(TENANT_ISOLATION_KEY, enabled);

/**
 * 跨租户访问装饰器 - 允许跨租户访问（需要特殊权限）
 */
export const CROSS_TENANT_KEY = 'cross_tenant';
export const CrossTenant = (enabled: boolean = true) =>
  SetMetadata(CROSS_TENANT_KEY, enabled);

/**
 * 租户管理员装饰器 - 只允许租户管理员访问
 */
export const TENANT_ADMIN_KEY = 'tenant_admin';
export const TenantAdmin = () => SetMetadata(TENANT_ADMIN_KEY, true);

/**
 * 超级管理员装饰器 - 只允许超级管理员访问
 */
export const SUPER_ADMIN_KEY = 'super_admin';
export const SuperAdmin = () => SetMetadata(SUPER_ADMIN_KEY, true);

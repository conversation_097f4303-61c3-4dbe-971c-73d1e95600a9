import { BaseCommand } from '@/shared/application/cqrs/command';
import { AccessRequestType } from '../../domain/aggregates/access-request.aggregate';

/**
 * 创建门禁申请命令
 */
export class CreateAccessRequestCommand extends BaseCommand {
  constructor(
    public readonly userId: string,
    public readonly requestType: AccessRequestType,
    public readonly plannedStartTime: Date,
    public readonly plannedEndTime: Date,
    public readonly locationName: string,
    public readonly reason: string,
    public readonly locationDescription?: string,
    public readonly locationBuilding?: string,
    public readonly locationFloor?: string,
    public readonly deviceId?: string,
  ) {
    super();
  }
}

/**
 * 批准门禁申请命令
 */
export class ApproveAccessRequestCommand extends BaseCommand {
  constructor(
    public readonly requestId: string,
    public readonly approverId: string,
    public readonly remark?: string,
    public readonly validUntil?: Date,
  ) {
    super();
  }
}

/**
 * 拒绝门禁申请命令
 */
export class RejectAccessRequestCommand extends BaseCommand {
  constructor(
    public readonly requestId: string,
    public readonly approverId: string,
    public readonly remark: string,
  ) {
    super();
  }
}

/**
 * 取消门禁申请命令
 */
export class CancelAccessRequestCommand extends BaseCommand {
  constructor(
    public readonly requestId: string,
    public readonly userId: string,
  ) {
    super();
  }
}

/**
 * 批量处理门禁申请命令
 */
export class BatchProcessAccessRequestsCommand extends BaseCommand {
  constructor(
    public readonly requestIds: string[],
    public readonly approverId: string,
    public readonly action: 'approve' | 'reject',
    public readonly remark?: string,
  ) {
    super();
  }
}

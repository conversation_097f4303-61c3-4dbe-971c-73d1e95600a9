import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DeveloperUser } from '../../domain/aggregates/user.aggregate';
import { UserProfile } from '../../domain/value-objects/user-profile';
import { UserTypeEnum } from '../../domain/value-objects/user-type';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import { Inject } from '@nestjs/common';
import { AuthDomainService } from '../../domain/services/auth-domain.service';
import { TenantContext } from '@/shared/domain/context/tenant.context';
import { AuditAction } from '@/shared/domain/repositories/base-repository';
import * as crypto from 'crypto';

/**
 * 开发者账号管理服务
 * 负责创建和管理开发者账号
 */
@Injectable()
export class DeveloperAccountService implements OnModuleInit {
  private readonly logger = new Logger(DeveloperAccountService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly authDomainService: AuthDomainService,
  ) {}

  /**
   * 模块初始化时自动创建开发者账号
   */
  async onModuleInit(): Promise<void> {
    const shouldCreateDevAccount = this.configService.get<boolean>('CREATE_DEV_ACCOUNT', false);

    if (shouldCreateDevAccount && this.isDevelopmentEnvironment()) {
      await this.ensureDeveloperAccountExists();
    }
  }

  /**
   * 确保开发者账号存在
   */
  async ensureDeveloperAccountExists(): Promise<DeveloperUser> {
    const devPhone = this.configService.get<string>('DEV_ACCOUNT_PHONE', '***********');

    try {
      // 检查开发者账号是否已存在
      const existingDev = await this.userRepository.findByPhone(devPhone);

      if (existingDev && existingDev instanceof DeveloperUser) {
        this.logger.log(`Developer account already exists: ${devPhone}`);
        return existingDev;
      }

      // 创建新的开发者账号
      const developer = await this.createDeveloperAccount();
      this.logger.log(`Developer account created successfully: ${developer.getProfile()?.phone}`);

      return developer;
    } catch (error) {
      this.logger.error('Failed to ensure developer account exists', error);
      throw error;
    }
  }

  /**
   * 创建开发者账号
   */
  async createDeveloperAccount(): Promise<DeveloperUser> {
    const devConfig = this.getDeveloperAccountConfig();

    // 创建用户档案
    const profile = new UserProfile({
      name: devConfig.name,
      phone: devConfig.phone,
      gender: 1,
      avatar: devConfig.avatar,
    });

    // 创建开发者用户
    const developer = new DeveloperUser(
      UserTypeEnum.DEVELOPER,
      profile,
      devConfig.accessLevel,
      devConfig.allowedEnvironments,
    );

    // 设置密码
    const hashedPassword = await this.authDomainService.hashUserPassword(devConfig.password);
    developer.setPassword(hashedPassword);

    // 生成API密钥
    const apiKey = this.generateApiKey();
    developer.setApiKey(apiKey);

    // 设置为系统租户
    const systemTenantId = TenantContext.getDefaultTenantId();

    // 保存开发者账号
    const auditContext = {
      userId: 'system',
      action: AuditAction.CREATE,
      metadata: {
        userType: UserTypeEnum.DEVELOPER,
        createdBy: 'system-initialization',
        apiKey: apiKey.substring(0, 8) + '...',
      },
    };

    const savedDeveloper = await TenantContext.switchTenant(systemTenantId, async () => {
      return await this.userRepository.save(developer, auditContext);
    });

    this.logger.log('Developer account created with API key', {
      phone: devConfig.phone,
      apiKey: apiKey.substring(0, 8) + '...',
      accessLevel: devConfig.accessLevel,
    });

    return savedDeveloper as DeveloperUser;
  }

  /**
   * 获取开发者账号配置
   */
  private getDeveloperAccountConfig() {
    return {
      name: this.configService.get<string>('DEV_ACCOUNT_NAME', '系统开发者'),
      phone: this.configService.get<string>('DEV_ACCOUNT_PHONE', '***********'),
      password: this.configService.get<string>('DEV_ACCOUNT_PASSWORD', 'Dev@123456'),
      avatar: this.configService.get<string>('DEV_ACCOUNT_AVATAR', ''),
      accessLevel: this.configService.get<'read' | 'write' | 'admin'>('DEV_ACCESS_LEVEL', 'admin'),
      allowedEnvironments: this.configService.get<string>('DEV_ALLOWED_ENVS', 'development,staging,production').split(','),
    };
  }

  /**
   * 生成API密钥
   */
  private generateApiKey(): string {
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(16).toString('hex');
    return `dev_${timestamp}_${random}`;
  }

  /**
   * 检查是否为开发环境
   */
  private isDevelopmentEnvironment(): boolean {
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    return nodeEnv === 'development' || nodeEnv === 'staging';
  }

  /**
   * 重置开发者账号密码
   */
  async resetDeveloperPassword(phone: string, newPassword: string): Promise<void> {
    const developer = await this.userRepository.findByPhone(phone);

    if (!developer || !(developer instanceof DeveloperUser)) {
      throw new Error('Developer account not found');
    }

    const hashedPassword = await this.authDomainService.hashUserPassword(newPassword);
    developer.setPassword(hashedPassword);

    const auditContext = {
      userId: 'system',
      action: AuditAction.UPDATE,
      metadata: {
        operation: 'password-reset',
        targetUser: developer.id,
      },
    };

    await this.userRepository.save(developer, auditContext);
    this.logger.log(`Developer password reset: ${phone}`);
  }

  /**
   * 重新生成API密钥
   */
  async regenerateApiKey(phone: string): Promise<string> {
    const developer = await this.userRepository.findByPhone(phone);

    if (!developer || !(developer instanceof DeveloperUser)) {
      throw new Error('Developer account not found');
    }

    const newApiKey = this.generateApiKey();
    developer.setApiKey(newApiKey);

    const auditContext = {
      userId: 'system',
      action: AuditAction.UPDATE,
      metadata: {
        operation: 'api-key-regeneration',
        targetUser: developer.id,
        newApiKey: newApiKey.substring(0, 8) + '...',
      },
    };

    await this.userRepository.save(developer, auditContext);

    this.logger.log('API key regenerated', {
      phone,
      newApiKey: newApiKey.substring(0, 8) + '...',
    });

    return newApiKey;
  }

  /**
   * 获取开发者账号信息
   */
  async getDeveloperInfo(phone: string): Promise<{
    id: string;
    name: string;
    phone: string;
    accessLevel: string;
    allowedEnvironments: string[];
    apiKey: string;
    loginStats: { lastLoginIp?: string; loginCount: number };
  } | null> {
    const developer = await this.userRepository.findByPhone(phone);

    if (!developer || !(developer instanceof DeveloperUser)) {
      return null;
    }

    const profile = developer.getProfile();
    if (!profile) return null;

    return {
      id: developer.id,
      name: profile.name,
      phone: profile.phone,
      accessLevel: developer.getAccessLevel(),
      allowedEnvironments: developer.getAllowedEnvironments(),
      apiKey: developer.getApiKey() || '',
      loginStats: developer.getLoginStats(),
    };
  }

  /**
   * 列出所有开发者账号
   */
  async listDeveloperAccounts(): Promise<DeveloperUser[]> {
    const developers = await this.userRepository.findByUserType(UserTypeEnum.DEVELOPER);
    return developers.filter(user => user instanceof DeveloperUser) as DeveloperUser[];
  }

  /**
   * 删除开发者账号
   */
  async deleteDeveloperAccount(phone: string): Promise<void> {
    const developer = await this.userRepository.findByPhone(phone);

    if (!developer || !(developer instanceof DeveloperUser)) {
      throw new Error('Developer account not found');
    }

    const auditContext = {
      userId: 'system',
      action: AuditAction.DELETE,
      metadata: {
        operation: 'developer-account-deletion',
        targetUser: developer.id,
        phone,
      },
    };

    await this.userRepository.softDelete(developer.id, auditContext);
    this.logger.log(`Developer account deleted: ${phone}`);
  }
}

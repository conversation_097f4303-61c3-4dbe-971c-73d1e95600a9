import { Injectable, Inject } from '@nestjs/common';
import { AuthDomainService } from '../../domain/services/auth-domain.service';
import {
  IJwtService,
  JWT_SERVICE,
} from '../../domain/services/jwt.service.interface';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import { JwtToken } from '../../domain/value-objects/jwt-token';
import {
  EntityNotFoundException,
  BusinessRuleException,
} from '@/shared/application/exceptions/domain-exception';
import {
  LoginRequestDto,
  LoginResponseDto,
  RefreshTokenRequestDto,
  TokenValidationResponseDto,
} from '../dto/auth.dto';

/**
 * 认证应用服务
 */
@Injectable()
export class AuthApplicationService {
  constructor(
    private readonly authDomainService: AuthDomainService,
    @Inject(JWT_SERVICE) private readonly jwtService: IJwtService,
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
  ) {}

  /**
   * 用户登录
   */
  async login(
    loginDto: LoginRequestDto,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<LoginResponseDto> {
    // 验证用户凭据
    const user = await this.authDomainService.validateUserCredentials(
      loginDto.phone,
      loginDto.password,
    );

    // 检查登录限制
    await this.authDomainService.checkLoginRestrictions(user);

    // 创建JWT载荷
    const jwtPayload = this.authDomainService.createJwtPayload(user);

    // 生成令牌
    const tokens = await this.jwtService.generateTokens(jwtPayload);

    // 记录登录日志
    await this.authDomainService.recordLoginLog(user, ipAddress, userAgent);

    // 构建响应
    const profile = user.getProfile();
    if (!profile) {
      throw new BusinessRuleException('用户档案信息不完整');
    }

    return {
      ...tokens.toResponseDto(),
      user: {
        id: user.id,
        name: profile.name,
        phone: profile.phone,
        userType: user.getUserTypeString(),
        avatar: profile.avatar,
        roles: jwtPayload.roles,
        permissions: jwtPayload.permissions,
      },
    };
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(
    refreshDto: RefreshTokenRequestDto,
  ): Promise<LoginResponseDto> {
    // 验证刷新令牌
    const { userId } = await this.jwtService.verifyRefreshToken(
      refreshDto.refresh_token,
    );

    // 验证刷新令牌是否有效
    const isValid = await this.authDomainService.validateRefreshToken(
      refreshDto.refresh_token,
      userId,
    );
    if (!isValid) {
      throw new BusinessRuleException('刷新令牌无效');
    }

    // 获取用户信息
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new EntityNotFoundException('User', userId);
    }

    // 检查用户状态
    if (!user.isUserActive()) {
      throw new BusinessRuleException('用户账户已被停用');
    }

    // 创建新的JWT载荷
    const jwtPayload = this.authDomainService.createJwtPayload(user);

    // 生成新的令牌
    const tokens = await this.jwtService.generateTokens(jwtPayload);

    // 撤销旧的刷新令牌
    await this.jwtService.revokeToken(refreshDto.refresh_token);

    // 构建响应
    const profile = user.getProfile();
    if (!profile) {
      throw new BusinessRuleException('用户档案信息不完整');
    }

    return {
      ...tokens.toResponseDto(),
      user: {
        id: user.id,
        name: profile.name,
        phone: profile.phone,
        userType: user.getUserTypeString(),
        avatar: profile.avatar,
        roles: jwtPayload.roles,
        permissions: jwtPayload.permissions,
      },
    };
  }

  /**
   * 验证访问令牌
   */
  async validateToken(token: string): Promise<TokenValidationResponseDto> {
    try {
      const payload = await this.jwtService.verifyAccessToken(token);

      // 获取令牌剩余时间
      const expiresIn = await this.jwtService.getTokenRemainingTime(token);

      return {
        valid: true,
        user: {
          id: payload.userId,
          name: payload.username,
          phone: '', // 从令牌中无法获取手机号，需要时可以查询数据库
          userType: payload.userType,
          roles: payload.roles,
          permissions: payload.permissions,
        },
        expires_in: expiresIn,
      };
    } catch (error) {
      return {
        valid: false,
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(accessToken: string, refreshToken?: string): Promise<void> {
    // 撤销访问令牌
    await this.jwtService.revokeToken(accessToken);

    // 撤销刷新令牌（如果提供）
    if (refreshToken) {
      await this.jwtService.revokeToken(refreshToken);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(token: string): Promise<{
    id: string;
    name: string;
    phone: string;
    userType: string;
    avatar?: string;
    roles: string[];
    permissions: string[];
  }> {
    const payload = await this.jwtService.verifyAccessToken(token);

    // 从数据库获取最新的用户信息
    const user = await this.userRepository.findById(payload.userId);
    if (!user) {
      throw new EntityNotFoundException('User', payload.userId);
    }

    const profile = user.getProfile();
    if (!profile) {
      throw new BusinessRuleException('用户档案信息不完整');
    }

    return {
      id: user.id,
      name: profile.name,
      phone: profile.phone,
      userType: user.getUserTypeString(),
      avatar: profile.avatar,
      roles: payload.roles,
      permissions: payload.permissions,
    };
  }

  /**
   * 检查用户权限
   */
  async checkPermission(token: string, permission: string): Promise<boolean> {
    try {
      const payload = await this.jwtService.verifyAccessToken(token);
      return payload.hasPermission(permission);
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查用户角色
   */
  async checkRole(token: string, role: string): Promise<boolean> {
    try {
      const payload = await this.jwtService.verifyAccessToken(token);
      return payload.hasRole(role);
    } catch (error) {
      return false;
    }
  }
}

import { JwtToken, JwtPayload } from '../value-objects/jwt-token';

/**
 * JWT服务接口
 */
export interface IJwtService {
  /**
   * 生成访问令牌
   */
  generateAccessToken(payload: JwtPayload): Promise<string>;

  /**
   * 生成刷新令牌
   */
  generateRefreshToken(userId: string): Promise<string>;

  /**
   * 生成完整的JWT令牌对象
   */
  generateTokens(payload: JwtPayload): Promise<JwtToken>;

  /**
   * 验证访问令牌
   */
  verifyAccessToken(token: string): Promise<JwtPayload>;

  /**
   * 验证刷新令牌
   */
  verifyRefreshToken(token: string): Promise<{ userId: string; iat: number; exp: number }>;

  /**
   * 刷新访问令牌
   */
  refreshAccessToken(refreshToken: string): Promise<JwtToken>;

  /**
   * 撤销令牌（加入黑名单）
   */
  revokeToken(token: string): Promise<void>;

  /**
   * 检查令牌是否被撤销
   */
  isTokenRevoked(token: string): Promise<boolean>;

  /**
   * 从请求头中提取令牌
   */
  extractTokenFromHeader(authHeader: string): string | null;

  /**
   * 获取令牌剩余有效时间（秒）
   */
  getTokenRemainingTime(token: string): Promise<number>;
}

/**
 * JWT服务令牌
 */
export const JWT_SERVICE = Symbol('JWT_SERVICE');

import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../base.entity';

export enum VisitorType {
  GUEST = 'guest',
  CONTRACTOR = 'contractor',
  VENDOR = 'vendor',
  PARENT_VISITOR = 'parent_visitor',
  OFFICIAL = 'official',
}

/**
 * 访客用户实体
 */
@Entity('user_visitors')
export class UserVisitor extends BaseEntity {
  @Column({ unique: true, comment: '用户名' })
  username: string;

  @Column({ comment: '密码哈希' })
  passwordHash: string;

  @Column({ comment: '真实姓名' })
  realName: string;

  @Column({ nullable: true, comment: '邮箱' })
  email?: string;

  @Column({ nullable: true, comment: '手机号' })
  phone?: string;

  @Column({ nullable: true, comment: '身份证号' })
  idCard?: string;

  @Column({
    type: 'enum',
    enum: VisitorType,
    default: VisitorType.GUEST,
    comment: '访客类型',
  })
  visitorType: VisitorType;

  @Column({ nullable: true, comment: '来访目的' })
  visitPurpose?: string;

  @Column({ nullable: true, comment: '所属公司/机构' })
  organization?: string;

  @Column({ nullable: true, comment: '职位' })
  position?: string;

  @Column({ nullable: true, comment: '联系人（校内）' })
  contactPerson?: string;

  @Column({ nullable: true, comment: '联系人电话' })
  contactPhone?: string;

  @Column({ default: true, comment: '是否激活' })
  isActive: boolean;

  @Column({ nullable: true, comment: '最后登录时间' })
  lastLoginAt?: Date;

  @Column({ nullable: true, comment: '头像URL' })
  avatarUrl?: string;

  @Column({ nullable: true, comment: '访问开始时间' })
  visitStartTime?: Date;

  @Column({ nullable: true, comment: '访问结束时间' })
  visitEndTime?: Date;

  @Column({ default: 'pending', comment: '访客状态' })
  status: string;

  @Column({ nullable: true, comment: '备注' })
  remarks?: string;

  /**
   * 验证密码
   */
  validatePassword(password: string): boolean {
    // 这里应该使用实际的密码验证逻辑
    // 例如使用 bcrypt 比较密码
    return true; // 临时实现
  }

  /**
   * 更新最后登录时间
   */
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    return this.realName || this.username;
  }

  /**
   * 检查访问是否有效
   */
  isVisitValid(): boolean {
    const now = new Date();
    
    if (this.visitStartTime && now < this.visitStartTime) {
      return false; // 访问还未开始
    }
    
    if (this.visitEndTime && now > this.visitEndTime) {
      return false; // 访问已结束
    }
    
    return this.status === 'approved';
  }

  /**
   * 获取访问剩余时间（小时）
   */
  getRemainingVisitHours(): number | null {
    if (!this.visitEndTime) return null;
    
    const now = new Date();
    const endTime = new Date(this.visitEndTime);
    
    if (now >= endTime) return 0;
    
    const diffMs = endTime.getTime() - now.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60));
  }
}

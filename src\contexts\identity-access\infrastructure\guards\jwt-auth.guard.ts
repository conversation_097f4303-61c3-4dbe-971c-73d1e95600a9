import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import {
  IJwtService,
  JWT_SERVICE,
} from '../../domain/services/jwt.service.interface';
import { JwtPayload } from '../../domain/value-objects/jwt-token';
import { TenantContext } from '@/shared/domain/context/tenant.context';

/**
 * JWT认证守卫
 */
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    @Inject(JWT_SERVICE) private readonly jwtService: IJwtService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否为公开路由
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromRequest(request);

    if (!token) {
      throw new UnauthorizedException('缺少访问令牌');
    }

    try {
      const payload = await this.jwtService.verifyAccessToken(token);

      // 将用户信息附加到请求对象
      (request as any).user = {
        id: payload.userId,
        username: payload.username,
        userType: payload.userType,
        tenantId: payload.tenantId,
        roles: payload.roles,
        permissions: payload.permissions,
      };

      // 设置租户上下文
      const tenantContext = TenantContext.createContext(payload.tenantId, {
        userId: payload.userId,
        permissions: payload.permissions,
        metadata: {
          userType: payload.userType,
          roles: payload.roles,
        },
      });

      // 在请求中存储租户上下文，供后续中间件使用
      (request as any).tenantContext = tenantContext;

      return true;
    } catch (error) {
      throw new UnauthorizedException('无效的访问令牌');
    }
  }

  /**
   * 从请求中提取令牌
   */
  private extractTokenFromRequest(request: Request): string | null {
    const authHeader = request.headers.authorization;
    return this.jwtService.extractTokenFromHeader(authHeader || '');
  }
}

/**
 * 公开路由装饰器
 */
import { SetMetadata } from '@nestjs/common';

export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

/**
 * 用户信息接口（用于请求对象）
 */
export interface RequestUser {
  id: string;
  username: string;
  userType: string;
  tenantId: string;
  roles: string[];
  permissions: string[];
}

/**
 * 扩展Request接口
 */
declare global {
  namespace Express {
    interface Request {
      user?: RequestUser;
    }
  }
}

import { Injectable } from '@nestjs/common';
import { PinoLoggerService } from './pino-logger.service';

/**
 * 日志使用示例
 * 展示如何在不同场景下使用 Pino 日志服务
 */
@Injectable()
export class LoggerUsageExamples {
  constructor(private readonly logger: PinoLoggerService) {
    this.logger.setContext('LoggerExamples');
  }

  /**
   * 基础日志记录示例
   */
  basicLoggingExamples(): void {
    // 普通信息日志
    this.logger.log('应用程序启动成功');

    // 调试日志
    this.logger.debug('调试信息：用户查询参数', { userId: '123', filters: { status: 'active' } }, 'LoggerExamples');

    // 警告日志
    this.logger.warn('检测到潜在问题：数据库连接缓慢', { connectionTime: 5000 }, 'LoggerExamples');

    // 错误日志
    this.logger.error('数据库连接失败', new Error('Connection timeout').stack);

    // 致命错误日志
    this.logger.fatal('系统无法启动：配置文件缺失');
  }

  /**
   * 业务操作日志示例
   */
  businessOperationExamples(): void {
    // 用户登录
    this.logger.logBusinessOperation(
      'user_login',
      'user_123',
      'tenant_456',
      {
        ip: '*************',
        userAgent: 'Mozilla/5.0...',
        loginMethod: 'password',
      }
    );

    // 数据修改
    this.logger.logBusinessOperation(
      'student_info_update',
      'teacher_789',
      'school_001',
      {
        studentId: 'student_456',
        changedFields: ['phone', 'address'],
        oldValues: { phone: '138****1234', address: '旧地址' },
        newValues: { phone: '139****5678', address: '新地址' },
      }
    );

    // 文件上传
    this.logger.logBusinessOperation(
      'file_upload',
      'user_123',
      'tenant_456',
      {
        fileName: 'student_photo.jpg',
        fileSize: 1024000,
        fileType: 'image/jpeg',
        uploadPath: '/uploads/photos/',
      }
    );
  }

  /**
   * 安全事件日志示例
   */
  securityEventExamples(): void {
    // 登录失败
    this.logger.logSecurityEvent(
      'login_failed',
      'medium',
      {
        userId: 'user_123',
        ip: '*************',
        reason: 'invalid_password',
        attemptCount: 3,
      }
    );

    // 可疑访问
    this.logger.logSecurityEvent(
      'suspicious_access',
      'high',
      {
        ip: '********',
        userAgent: 'Suspicious Bot',
        requestPath: '/admin/users',
        reason: 'unauthorized_access_attempt',
      }
    );

    // 权限提升
    this.logger.logSecurityEvent(
      'privilege_escalation',
      'critical',
      {
        userId: 'user_123',
        fromRole: 'student',
        toRole: 'admin',
        authorizedBy: 'admin_456',
        reason: 'temporary_access',
      }
    );
  }

  /**
   * 性能监控日志示例
   */
  performanceMonitoringExamples(): void {
    // API 响应时间
    this.logger.logPerformanceMetric(
      'api_response_time',
      1250,
      'ms',
      {
        endpoint: '/api/students',
        method: 'GET',
        statusCode: '200',
      }
    );

    // 数据库查询时间
    this.logger.logPerformanceMetric(
      'db_query_time',
      850,
      'ms',
      {
        query: 'SELECT * FROM students WHERE...',
        table: 'students',
        operation: 'select',
      }
    );

    // 内存使用情况
    this.logger.logPerformanceMetric(
      'memory_usage',
      75.5,
      '%',
      {
        total: '8GB',
        used: '6.04GB',
        free: '1.96GB',
      }
    );

    // 缓存命中率
    this.logger.logPerformanceMetric(
      'cache_hit_rate',
      92.5,
      '%',
      {
        cacheType: 'redis',
        totalRequests: '1000',
        hits: '925',
        misses: '75',
      }
    );
  }

  /**
   * 审计日志示例
   */
  auditLogExamples(): void {
    // 用户信息修改
    this.logger.logAudit(
      'update',
      'admin_123',
      'user',
      'user_456',
      {
        before: { name: '张三', phone: '138****1234' },
        after: { name: '张三丰', phone: '139****5678' },
        reason: '用户申请修改个人信息',
      }
    );

    // 权限分配
    this.logger.logAudit(
      'grant_permission',
      'admin_123',
      'role',
      'teacher_role',
      {
        permissions: ['read_student_info', 'update_grades'],
        grantedTo: 'user_789',
        expiresAt: '2024-12-31T23:59:59Z',
      }
    );

    // 数据删除
    this.logger.logAudit(
      'delete',
      'admin_123',
      'student',
      'student_456',
      {
        reason: '学生转学',
        backupLocation: '/backups/students/2024/student_456.json',
        approvedBy: 'principal_001',
      }
    );
  }

  /**
   * HTTP 请求日志示例
   */
  httpRequestExamples(): void {
    // 模拟 HTTP 请求对象
    const mockReq = {
      method: 'POST',
      url: '/api/students',
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'content-type': 'application/json',
        'authorization': 'Bearer eyJhbGciOiJIUzI1NiIs...',
      },
      ip: '*************',
    };

    const mockRes = {
      statusCode: 201,
      get: (header: string) => header === 'content-length' ? '1024' : undefined,
    };

    // 记录 HTTP 请求
    this.logger.logHttpRequest(mockReq, mockRes, 1250);
  }

  /**
   * 数据库查询日志示例
   */
  databaseQueryExamples(): void {
    // SELECT 查询
    this.logger.logDatabaseQuery(
      'SELECT * FROM students WHERE grade = ? AND status = ?',
      ['10', 'active'],
      850
    );

    // INSERT 查询
    this.logger.logDatabaseQuery(
      'INSERT INTO students (name, grade, class_id) VALUES (?, ?, ?)',
      ['张三', '10', 'class_001'],
      120
    );

    // 复杂查询
    this.logger.logDatabaseQuery(
      `SELECT s.name, s.grade, c.name as class_name, t.name as teacher_name
       FROM students s
       JOIN classes c ON s.class_id = c.id
       JOIN teachers t ON c.teacher_id = t.id
       WHERE s.status = ? AND c.grade = ?`,
      ['active', '10'],
      2100
    );
  }

  /**
   * 结构化日志示例
   */
  structuredLoggingExamples(): void {
    // 用户行为追踪
    this.logger.log({
      event: 'user_action',
      userId: 'user_123',
      tenantId: 'school_001',
      action: 'view_grades',
      resource: 'student_grades',
      resourceId: 'grade_456',
      timestamp: new Date().toISOString(),
      metadata: {
        subject: 'mathematics',
        semester: '2024-spring',
        viewType: 'detailed',
      },
    });

    // 系统状态监控
    this.logger.log({
      event: 'system_health_check',
      status: 'healthy',
      services: {
        database: 'connected',
        redis: 'connected',
        fileStorage: 'available',
      },
      metrics: {
        cpuUsage: 45.2,
        memoryUsage: 68.7,
        diskUsage: 23.1,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 错误处理日志示例
   */
  errorHandlingExamples(): void {
    try {
      // 模拟可能出错的操作
      throw new Error('数据库连接超时');
    } catch (error) {
      // 记录详细的错误信息
      this.logger.error({
        error: error.message,
        stack: error.stack,
        context: 'database_connection',
        operation: 'fetch_student_list',
        userId: 'user_123',
        tenantId: 'school_001',
        timestamp: new Date().toISOString(),
        additionalInfo: {
          connectionString: 'mysql://localhost:3306/smart_campus',
          timeout: 5000,
          retryCount: 3,
        },
      });
    }
  }

  /**
   * 子日志器使用示例
   */
  childLoggerExamples(): void {
    // 创建带有特定上下文的子日志器
    const userLogger = this.logger.child({
      userId: 'user_123',
      tenantId: 'school_001',
      sessionId: 'session_456',
    });

    // 使用子日志器记录日志，会自动包含上下文信息
    userLogger.log('用户开始浏览课程列表');
    userLogger.debug('加载课程数据', { courseCount: 25 }, 'UserLogger');
    userLogger.warn('课程加载缓慢', { loadTime: 3000 }, 'UserLogger');
  }
}

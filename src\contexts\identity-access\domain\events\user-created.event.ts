import { DomainEvent } from '@/shared/domain/events/domain-event';
import { UserTypeEnum } from '../value-objects/user-type';
import { UserProfile } from '../value-objects/user-profile';

/**
 * 用户创建事件
 */
export class UserCreatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly userType: UserTypeEnum,
    public readonly profile: UserProfile,
  ) {
    super(userId);
  }

  getEventName(): string {
    return 'user.created';
  }

  getEventData(): Record<string, any> {
    return {
      userId: this.userId,
      userType: this.userType,
      profile: {
        name: this.profile.name,
        phone: this.profile.phone,
        gender: this.profile.gender,
        avatar: this.profile.avatar,
        age: this.profile.age,
        birthday: this.profile.birthday,
        cardNo: this.profile.cardNo,
        cardType: this.profile.cardType,
      },
    };
  }
}

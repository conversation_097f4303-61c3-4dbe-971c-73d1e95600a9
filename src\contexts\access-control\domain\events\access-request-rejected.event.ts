import { DomainEvent } from '@/shared/domain/events/domain-event';

/**
 * 门禁申请拒绝事件
 */
export class AccessRequestRejectedEvent extends DomainEvent {
  constructor(
    public readonly requestId: string,
    public readonly userId: string,
    public readonly approverId: string,
    public readonly remark: string,
  ) {
    super(requestId);
  }

  getEventName(): string {
    return 'access-request.rejected';
  }

  getEventData(): Record<string, any> {
    return {
      requestId: this.requestId,
      userId: this.userId,
      approverId: this.approverId,
      remark: this.remark,
    };
  }
}

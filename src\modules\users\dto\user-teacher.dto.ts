import { IsString, IsOptional, IsEmail, IsPhoneNumber, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum TeacherType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  SUBSTITUTE = 'substitute',
}

/**
 * 创建教师用户 DTO
 */
export class CreateUserTeacherDto {
  @ApiProperty({ description: '用户名' })
  @IsString()
  username: string;

  @ApiProperty({ description: '密码' })
  @IsString()
  password: string;

  @ApiProperty({ description: '真实姓名' })
  @IsString()
  realName: string;

  @ApiPropertyOptional({ description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string;

  @ApiPropertyOptional({ description: '工号' })
  @IsOptional()
  @IsString()
  employeeNumber?: string;

  @ApiPropertyOptional({ description: '部门' })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiPropertyOptional({ description: '职位' })
  @IsOptional()
  @IsString()
  position?: string;

  @ApiPropertyOptional({ description: '教师类型', enum: TeacherType })
  @IsOptional()
  @IsEnum(TeacherType)
  teacherType?: TeacherType;

  @ApiPropertyOptional({ description: '专业领域' })
  @IsOptional()
  @IsString()
  specialization?: string;

  @ApiPropertyOptional({ description: '学历' })
  @IsOptional()
  @IsString()
  education?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  @IsOptional()
  @IsString()
  idCard?: string;
}

/**
 * 更新教师用户 DTO
 */
export class UpdateUserTeacherDto {
  @ApiPropertyOptional({ description: '真实姓名' })
  @IsOptional()
  @IsString()
  realName?: string;

  @ApiPropertyOptional({ description: '邮箱' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string;

  @ApiPropertyOptional({ description: '工号' })
  @IsOptional()
  @IsString()
  employeeNumber?: string;

  @ApiPropertyOptional({ description: '部门' })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiPropertyOptional({ description: '职位' })
  @IsOptional()
  @IsString()
  position?: string;

  @ApiPropertyOptional({ description: '教师类型', enum: TeacherType })
  @IsOptional()
  @IsEnum(TeacherType)
  teacherType?: TeacherType;

  @ApiPropertyOptional({ description: '专业领域' })
  @IsOptional()
  @IsString()
  specialization?: string;

  @ApiPropertyOptional({ description: '学历' })
  @IsOptional()
  @IsString()
  education?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  @IsOptional()
  @IsString()
  idCard?: string;
}

/**
 * 教师用户响应 DTO
 */
export class UserTeacherResponseDto {
  @ApiProperty({ description: '用户ID' })
  id: string;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiProperty({ description: '真实姓名' })
  realName: string;

  @ApiPropertyOptional({ description: '邮箱' })
  email?: string;

  @ApiPropertyOptional({ description: '手机号' })
  phone?: string;

  @ApiPropertyOptional({ description: '工号' })
  employeeNumber?: string;

  @ApiPropertyOptional({ description: '部门' })
  department?: string;

  @ApiPropertyOptional({ description: '职位' })
  position?: string;

  @ApiPropertyOptional({ description: '教师类型' })
  teacherType?: TeacherType;

  @ApiPropertyOptional({ description: '专业领域' })
  specialization?: string;

  @ApiPropertyOptional({ description: '学历' })
  education?: string;

  @ApiPropertyOptional({ description: '身份证号' })
  idCard?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { UserTeacher } from '@entities/users/user-teacher.entity';
import { UserStudent } from '@entities/users/user-student.entity';
import { UserParent } from '@entities/users/user-parent.entity';
import { UserVisitor } from '@/entities/users/user-visitor.entity';
import { UserPhotograph } from '@entities/users/user-photograph.entity';

// 服务层
import { UserTeacherService } from './services/user-teacher.service';
import { UserStudentService } from './services/user-student.service';
import { UserParentService } from './services/user-parent.service';
import { UserVisitorService } from './services/user-visitor.service';

// 控制器层
import { UserTeacherController } from './controllers/user-teacher.controller';
import { UserStudentController } from './controllers/user-student.controller';
import { UserParentController } from './controllers/user-parent.controller';
import { UserVisitorController } from './controllers/user-visitor.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTeacher,
      UserStudent,
      UserParent,
      UserVisitor,
      UserPhotograph,
    ]),
  ],
  controllers: [
    UserTeacherController,
    UserStudentController,
    UserParentController,
    UserVisitorController,
  ],
  providers: [
    // 服务层
    UserTeacherService,
    UserStudentService,
    UserParentService,
    UserVisitorService,
  ],
  exports: [
    UserTeacherService,
    UserStudentService,
    UserParentService,
    UserVisitorService,
  ],
})
export class UsersModule {}

{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@shared/*": ["src/shared/*"], "@contexts/*": ["src/contexts/*"], "@entities/*": ["src/entities/*"], "@database/*": ["src/database/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "strictFunctionTypes": false, "noImplicitThis": false}}
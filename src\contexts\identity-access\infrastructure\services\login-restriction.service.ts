import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserTypeEnum } from '../../domain/value-objects/user-type';
import { User } from '../../domain/aggregates/user.aggregate';

/**
 * 登录限制配置接口
 */
export interface LoginRestrictionConfig {
  enabled: boolean;
  allowedIPs?: string[];
  timeRestriction?: boolean;
  allowedHours?: string; // 格式: "9-18"
  maxConcurrentSessions?: number;
  sessionTimeout?: string; // 格式: "24h"
  allowedEnvironments?: string[];
}

/**
 * 登录限制结果
 */
export interface LoginRestrictionResult {
  allowed: boolean;
  reason?: string;
  code?: string;
}

/**
 * 登录限制服务
 * 支持配置文件和数据库的混合存储策略
 */
@Injectable()
export class LoginRestrictionService {
  private readonly logger = new Logger(LoginRestrictionService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 检查用户登录限制
   */
  async checkLoginRestrictions(
    user: User,
    clientIP: string,
    userAgent?: string,
  ): Promise<LoginRestrictionResult> {
    const userType = user.getUserTypeString();
    
    // 根据用户类型获取限制配置
    const config = await this.getLoginRestrictionConfig(userType);
    
    if (!config.enabled) {
      return { allowed: true };
    }

    // 1. 检查IP限制
    const ipCheck = this.checkIPRestriction(clientIP, config.allowedIPs);
    if (!ipCheck.allowed) {
      return ipCheck;
    }

    // 2. 检查时间限制
    const timeCheck = this.checkTimeRestriction(config);
    if (!timeCheck.allowed) {
      return timeCheck;
    }

    // 3. 检查环境限制
    const envCheck = this.checkEnvironmentRestriction(config.allowedEnvironments);
    if (!envCheck.allowed) {
      return envCheck;
    }

    // 4. 检查并发会话限制（需要数据库查询）
    const sessionCheck = await this.checkConcurrentSessions(user, config.maxConcurrentSessions);
    if (!sessionCheck.allowed) {
      return sessionCheck;
    }

    return { allowed: true };
  }

  /**
   * 获取登录限制配置
   */
  private async getLoginRestrictionConfig(userType: string): Promise<LoginRestrictionConfig> {
    // 优先级：数据库配置 > 环境变量配置 > 默认配置
    
    // 1. 尝试从数据库获取（如果实现了数据库存储）
    const dbConfig = await this.getConfigFromDatabase(userType);
    if (dbConfig) {
      return dbConfig;
    }

    // 2. 从环境变量获取
    return this.getConfigFromEnvironment(userType);
  }

  /**
   * 从环境变量获取配置
   */
  private getConfigFromEnvironment(userType: string): LoginRestrictionConfig {
    const prefix = this.getConfigPrefix(userType);
    
    return {
      enabled: this.configService.get<boolean>(`${prefix}_LOGIN_ENABLED`, true),
      allowedIPs: this.parseIPList(
        this.configService.get<string>(`${prefix}_LOGIN_ALLOWED_IPS`, '127.0.0.1,::1')
      ),
      timeRestriction: this.configService.get<boolean>(`${prefix}_LOGIN_TIME_RESTRICTION`, false),
      allowedHours: this.configService.get<string>(`${prefix}_LOGIN_ALLOWED_HOURS`, '0-23'),
      maxConcurrentSessions: this.configService.get<number>(`${prefix}_MAX_CONCURRENT_SESSIONS`, 5),
      sessionTimeout: this.configService.get<string>(`${prefix}_SESSION_TIMEOUT`, '24h'),
      allowedEnvironments: this.parseEnvironmentList(
        this.configService.get<string>(`${prefix}_ALLOWED_ENVS`, 'development,staging,production')
      ),
    };
  }

  /**
   * 从数据库获取配置（预留接口）
   */
  private async getConfigFromDatabase(userType: string): Promise<LoginRestrictionConfig | null> {
    // TODO: 实现数据库配置存储
    // const config = await this.loginRestrictionRepository.findByUserType(userType);
    // return config ? this.mapDbConfigToInterface(config) : null;
    return null;
  }

  /**
   * 获取配置前缀
   */
  private getConfigPrefix(userType: string): string {
    const prefixes = {
      [UserTypeEnum.DEVELOPER]: 'DEV',
      [UserTypeEnum.TEACHER]: 'TEACHER',
      [UserTypeEnum.STUDENT]: 'STUDENT',
      [UserTypeEnum.PARENT]: 'PARENT',
      [UserTypeEnum.VISITOR]: 'VISITOR',
    };
    return prefixes[userType as UserTypeEnum] || 'DEFAULT';
  }

  /**
   * 检查IP限制
   */
  private checkIPRestriction(clientIP: string, allowedIPs?: string[]): LoginRestrictionResult {
    if (!allowedIPs || allowedIPs.length === 0) {
      return { allowed: true };
    }

    const isAllowed = allowedIPs.some(allowedIP => {
      if (allowedIP.includes('/')) {
        // CIDR 格式检查
        return this.isIPInCIDR(clientIP, allowedIP);
      } else {
        // 精确匹配
        return clientIP === allowedIP;
      }
    });

    if (!isAllowed) {
      this.logger.warn(`Login blocked: IP ${clientIP} not in allowed list`, {
        clientIP,
        allowedIPs,
      });
      
      return {
        allowed: false,
        reason: `IP地址 ${clientIP} 不在允许的范围内`,
        code: 'IP_NOT_ALLOWED',
      };
    }

    return { allowed: true };
  }

  /**
   * 检查时间限制
   */
  private checkTimeRestriction(config: LoginRestrictionConfig): LoginRestrictionResult {
    if (!config.timeRestriction || !config.allowedHours) {
      return { allowed: true };
    }

    const now = new Date();
    const currentHour = now.getHours();
    const [startHour, endHour] = config.allowedHours.split('-').map(h => parseInt(h.trim()));

    const isInAllowedTime = currentHour >= startHour && currentHour <= endHour;

    if (!isInAllowedTime) {
      this.logger.warn(`Login blocked: Current time ${currentHour} not in allowed hours ${config.allowedHours}`);
      
      return {
        allowed: false,
        reason: `当前时间 ${currentHour}:00 不在允许的登录时间范围内 (${config.allowedHours})`,
        code: 'TIME_NOT_ALLOWED',
      };
    }

    return { allowed: true };
  }

  /**
   * 检查环境限制
   */
  private checkEnvironmentRestriction(allowedEnvironments?: string[]): LoginRestrictionResult {
    if (!allowedEnvironments || allowedEnvironments.length === 0) {
      return { allowed: true };
    }

    const currentEnv = this.configService.get<string>('NODE_ENV', 'development');
    
    if (!allowedEnvironments.includes(currentEnv)) {
      this.logger.warn(`Login blocked: Environment ${currentEnv} not allowed`, {
        currentEnv,
        allowedEnvironments,
      });
      
      return {
        allowed: false,
        reason: `当前环境 ${currentEnv} 不允许登录`,
        code: 'ENVIRONMENT_NOT_ALLOWED',
      };
    }

    return { allowed: true };
  }

  /**
   * 检查并发会话限制
   */
  private async checkConcurrentSessions(
    user: User,
    maxSessions?: number,
  ): Promise<LoginRestrictionResult> {
    if (!maxSessions || maxSessions <= 0) {
      return { allowed: true };
    }

    // TODO: 实现会话计数逻辑
    // const currentSessions = await this.sessionService.getActiveSessionCount(user.id);
    const currentSessions = 0; // 临时值

    if (currentSessions >= maxSessions) {
      this.logger.warn(`Login blocked: User ${user.id} has ${currentSessions} active sessions, max allowed: ${maxSessions}`);
      
      return {
        allowed: false,
        reason: `已达到最大并发会话数限制 (${maxSessions})`,
        code: 'MAX_SESSIONS_EXCEEDED',
      };
    }

    return { allowed: true };
  }

  /**
   * 解析IP列表
   */
  private parseIPList(ipString: string): string[] {
    return ipString.split(',').map(ip => ip.trim()).filter(ip => ip.length > 0);
  }

  /**
   * 解析环境列表
   */
  private parseEnvironmentList(envString: string): string[] {
    return envString.split(',').map(env => env.trim()).filter(env => env.length > 0);
  }

  /**
   * 检查IP是否在CIDR范围内
   */
  private isIPInCIDR(ip: string, cidr: string): boolean {
    // 简化实现，实际项目中建议使用专门的库如 ip-range-check
    try {
      const [network, prefixLength] = cidr.split('/');
      // 这里需要实现CIDR匹配逻辑
      // 为了简化，暂时只做精确匹配
      return ip === network;
    } catch (error) {
      this.logger.error(`Invalid CIDR format: ${cidr}`, error);
      return false;
    }
  }

  /**
   * 获取用户类型的默认限制配置
   */
  getDefaultRestrictionConfig(userType: UserTypeEnum): LoginRestrictionConfig {
    const defaults: Record<UserTypeEnum, LoginRestrictionConfig> = {
      [UserTypeEnum.DEVELOPER]: {
        enabled: true,
        allowedIPs: ['127.0.0.1', '::1', '***********/24'],
        timeRestriction: false,
        allowedHours: '0-23',
        maxConcurrentSessions: 3,
        sessionTimeout: '24h',
        allowedEnvironments: ['development', 'staging'],
      },
      [UserTypeEnum.TEACHER]: {
        enabled: false,
        maxConcurrentSessions: 5,
        sessionTimeout: '8h',
      },
      [UserTypeEnum.STUDENT]: {
        enabled: false,
        maxConcurrentSessions: 2,
        sessionTimeout: '4h',
      },
      [UserTypeEnum.PARENT]: {
        enabled: false,
        maxConcurrentSessions: 3,
        sessionTimeout: '12h',
      },
      [UserTypeEnum.VISITOR]: {
        enabled: true,
        timeRestriction: true,
        allowedHours: '9-17',
        maxConcurrentSessions: 1,
        sessionTimeout: '2h',
      },
    };

    return defaults[userType] || { enabled: false };
  }
}

项目说明：智慧校园平台
主要功能：
1. 门禁管理

用户类型：
1. 学生
2. 教师
3. 家长
4. 访客


技术栈
- **框架**: NestJS 11.x (基于 Node.js)
- **运行时**: Node.js
- **数据库**: MySQL 8.x
- **ORM**: TypeORM 0.3.x
- **Web 框架**: Fastify 5.x
- **认证**: JWT + Passport
- **API 文档**: Swagger/OpenAPI
- **日志**: Pino
- **语言**: TypeScript 5.x

# 智慧校园平台目录结构说明

## 📁 项目根目录

```
smart-campus-server/
├── docs/                           # 📚 文档目录
├── src/                            # 🔧 源代码目录
├── test/                           # 🧪 测试目录
├── scripts/                        # 📜 脚本目录
├── package.json                    # 📦 项目配置文件
├── tsconfig.json                   # ⚙️ TypeScript 配置
├── nest-cli.json                   # 🪺 NestJS 配置
├── eslint.config.mjs               # 🔍 ESLint 配置
├── insert-menus.js                 # 📋 菜单插入脚本
└── .gitignore                      # 🚫 Git 忽略文件
```

---

## 📚 docs/ - 文档目录

```
docs/
├── architecture-guide.md           # 🏗️ 架构指南
├── multi-tenant-system-guide.md    # 🏢 多租户系统指南
├── cache-system-guide.md           # 💾 缓存系统指南
├── audit-system-guide.md           # 📝 审计系统指南
├── auth-usage-examples.md          # 🔐 认证使用示例
├── command-pattern-benefits.md     # 📋 命令模式优势
├── architecture-summary.md         # 📊 架构总结
├── fix-compilation-errors.md       # 🔧 编译错误修复
├── framework-analysis.md           # 📋 框架分析文档
└── directory-structure.md          # 📁 本文档
```

### 文档说明
- **架构指南**: 详细的架构设计说明和最佳实践
- **多租户系统指南**: 多租户架构的实现和使用方法
- **缓存系统指南**: Redis 缓存的使用和配置
- **审计系统指南**: 操作审计功能的实现
- **认证使用示例**: JWT 认证的具体使用示例
- **命令模式优势**: CQRS 命令模式的优势说明
- **架构总结**: 整体架构的简要总结
- **编译错误修复**: 常见编译错误的解决方案
- **框架分析文档**: 完整的框架架构分析
- **目录结构说明**: 本文档，详细说明项目结构

---

## 🔧 src/ - 源代码目录

### 根级文件

```
src/
├── app.controller.ts               # 🎮 应用控制器
├── app.module.ts                  # 📦 应用主模块
├── app.service.ts                 # 🔧 应用服务
├── main.ts                        # 🚀 应用入口文件
├── contexts/                      # 🏛️ 业务上下文目录
├── shared/                        # 🔗 共享模块目录
└── database/                      # 💾 数据库相关
```

#### 文件说明
- **app.controller.ts**: 应用级别的控制器，处理根路径请求
- **app.module.ts**: 应用主模块，配置所有子模块
- **app.service.ts**: 应用级别的服务，提供全局功能
- **main.ts**: 应用程序入口点，启动 NestJS 应用
- **contexts/**: 业务上下文目录，按领域划分业务模块
- **shared/**: 共享模块目录，提供跨上下文的基础设施
- **database/**: 数据库相关配置和工具

---

## 🏛️ contexts/ - 业务上下文目录

### identity-access/ - 身份访问上下文

```
contexts/identity-access/
├── application/                   # 📋 应用层
│   ├── commands/                  # 📝 命令定义
│   │   ├── create-student.command.ts
│   │   └── handlers/              # 🎯 命令处理器
│   ├── dto/                       # 📦 数据传输对象
│   │   └── student.dto.ts
│   ├── queries/                   # 🔍 查询定义
│   │   ├── get-student.query.ts
│   │   └── handlers/              # 🎯 查询处理器
│   └── services/                  # 🔧 应用服务
│       ├── auth-application.service.ts
│       ├── parent-application.service.ts
│       ├── student-application.service.ts
│       ├── teacher-application.service.ts
│       └── visitor-application.service.ts
├── domain/                        # 🏛️ 领域层
│   ├── aggregates/                # 🎯 聚合根
│   │   └── user.aggregate.ts
│   ├── events/                    # 📡 领域事件
│   │   ├── user-created.event.ts
│   │   └── user-updated.event.ts
│   ├── repositories/              # 📚 仓储接口
│   │   └── user.repository.ts
│   ├── services/                  # 🔧 领域服务
│   │   ├── auth-domain.service.ts
│   │   ├── jwt.service.interface.ts
│   │   └── user-domain.service.ts
│   ├── value-objects/             # 💎 值对象
│   │   ├── jwt-token.ts
│   │   ├── user-profile.ts
│   │   └── user-type.ts
│   └── tokens.ts                  # 🔑 依赖注入令牌
├── infrastructure/                # 🏗️ 基础设施层
│   ├── guards/                    # 🛡️ 守卫
│   │   ├── jwt-auth.guard.ts
│   │   └── permissions.guard.ts
│   ├── repositories/              # 📚 仓储实现
│   │   └── typeorm-user.repository.ts
│   └── services/                  # 🔧 基础设施服务
│       └── jwt.service.ts
├── interface/                     # 🌐 接口层
│   └── rest/                      # 🔗 REST API
│       ├── auth.controller.ts
│       ├── parent.controller.ts
│       ├── student.controller.ts
│       ├── teacher.controller.ts
│       ├── user.controller.ts
│       └── visitor.controller.ts
├── test/                          # 🧪 测试文件
│   └── student.integration.spec.ts
└── identity-access.module.ts      # 📦 模块定义
```

#### 应用层 (application/)
- **commands/**: 命令定义和处理器，实现 CQRS 命令模式
- **dto/**: 数据传输对象，定义 API 接口的数据结构
- **queries/**: 查询定义和处理器，实现 CQRS 查询模式
- **services/**: 应用服务，协调领域对象完成业务用例

#### 领域层 (domain/)
- **aggregates/**: 聚合根，业务实体的根对象
- **events/**: 领域事件，业务状态变化的通知
- **repositories/**: 仓储接口，定义数据访问抽象
- **services/**: 领域服务，跨聚合的业务逻辑
- **value-objects/**: 值对象，不可变的对象
- **tokens.ts**: 依赖注入令牌，用于接口绑定

#### 基础设施层 (infrastructure/)
- **guards/**: 守卫，实现认证和授权逻辑
- **repositories/**: 仓储实现，具体的数据访问实现
- **services/**: 基础设施服务，如 JWT 服务

#### 接口层 (interface/)
- **rest/**: REST API 控制器，处理 HTTP 请求

### access-control/ - 门禁控制上下文

```
contexts/access-control/
├── application/                   # 📋 应用层
│   ├── commands/                  # 📝 命令定义
│   │   ├── create-access-record.command.ts
│   │   ├── create-access-request.command.ts
│   │   └── handlers/              # 🎯 命令处理器
├── domain/                        # 🏛️ 领域层
│   ├── aggregates/                # 🎯 聚合根
│   │   ├── access-record.aggregate.ts
│   │   └── access-request.aggregate.ts
│   ├── events/                    # 📡 领域事件
│   │   ├── access-record-created.event.ts
│   │   ├── access-record-updated.event.ts
│   │   ├── access-request-approved.event.ts
│   │   ├── access-request-created.event.ts
│   │   └── access-request-rejected.event.ts
│   ├── repositories/              # 📚 仓储接口
│   │   └── access-record.repository.ts
│   └── value-objects/             # 💎 值对象
│       ├── access-location.ts
│       └── access-time-range.ts
└── infrastructure/                # 🏗️ 基础设施层
    └── repositories/              # 📚 仓储实现
        └── typeorm-access-record.repository.ts
```

#### 门禁控制上下文说明
- **access-record**: 门禁记录聚合根，记录用户的进出记录
- **access-request**: 访问请求聚合根，处理访客的访问申请
- **access-location**: 位置值对象，定义门禁位置信息
- **access-time-range**: 时间范围值对象，定义访问时间范围

---

## 🔗 shared/ - 共享模块目录

```
shared/
├── application/                   # 📋 应用层共享
│   ├── cqrs/                     # 📋 CQRS 基础设施
│   │   ├── command.ts
│   │   ├── event.ts
│   │   └── query.ts
│   ├── dto/                      # 📦 共享 DTO
│   │   ├── base-query.dto.ts
│   │   └── base-response.dto.ts
│   ├── exceptions/               # ⚠️ 异常定义
│   │   ├── base.exception.ts
│   │   └── domain-exception.ts
│   ├── services/                 # 🔧 应用服务
│   │   └── audit.service.ts
│   └── utils/                    # 🛠️ 工具类
│       └── response.util.ts
├── domain/                       # 🏛️ 领域层共享
│   ├── base/                     # 🏗️ 基础类
│   │   ├── aggregate-root.ts
│   │   ├── base-entity.ts
│   │   └── value-object.ts
│   ├── context/                  # 🏢 上下文
│   │   └── tenant.context.ts
│   ├── decorators/               # 🎨 装饰器
│   │   └── tenant.decorator.ts
│   ├── events/                   # 📡 领域事件
│   │   └── domain-event.ts
│   ├── repositories/             # 📚 仓储接口
│   │   └── base-repository.ts
│   └── utils/                    # 🛠️ 工具类
│       └── uuid.util.ts
├── enums/                        # 📊 枚举定义
│   ├── restroom.enum.ts
│   └── users.enum.ts
├── filters/                      # 🔍 异常过滤器
│   └── http-exception.filter.ts
├── infrastructure/               # 🏗️ 基础设施层共享
│   ├── cache/                    # 💾 缓存
│   │   ├── cache.interceptor.ts
│   │   ├── cache.module.ts
│   │   ├── cache.service.ts
│   │   └── redis.config.ts
│   ├── config/                   # ⚙️ 配置
│   │   ├── database.config.ts
│   │   ├── logger.config.ts
│   │   └── swagger.config.ts
│   ├── cqrs/                     # 📋 CQRS 实现
│   │   ├── simple-command-bus.ts
│   │   └── simple-query-bus.ts
│   ├── database/                 # 💾 数据库
│   │   ├── database-health.service.ts
│   │   └── database.config.ts
│   ├── decorators/               # 🎨 装饰器
│   │   ├── cache.decorator.ts
│   │   ├── raw-response.decorator.ts
│   │   └── swagger.decorators.ts
│   ├── filters/                  # 🔍 过滤器
│   │   └── global-exception.filter.ts
│   ├── gateway/                  # 🌐 网关
│   │   └── api-gateway.config.ts
│   ├── guards/                   # 🛡️ 守卫
│   │   ├── jwt-auth.guard.ts
│   │   └── tenant.guard.ts
│   ├── interceptors/             # 🔄 拦截器
│   │   ├── audit.interceptor.ts
│   │   ├── response.interceptor.ts
│   │   └── tenant.interceptor.ts
│   ├── messaging/                # 📨 消息队列
│   │   └── message-queue.config.ts
│   ├── middleware/               # 🔧 中间件
│   │   └── request-logging.middleware.ts
│   └── repositories/             # 📚 仓储实现
│       ├── base-typeorm.repository.ts
│       └── typeorm-base.repository.ts
├── interfaces/                   # 🔌 接口定义
├── models/                       # 📊 模型定义
│   └── user-profile.model.ts
├── types/                        # 📝 类型定义
├── utils/                        # 🛠️ 工具类
└── shared.module.ts              # 📦 共享模块定义
```

#### 应用层共享 (application/)
- **cqrs/**: CQRS 基础设施，定义命令、查询、事件基类
- **dto/**: 共享的数据传输对象
- **exceptions/**: 异常定义，统一的异常处理
- **services/**: 共享的应用服务
- **utils/**: 工具类

#### 领域层共享 (domain/)
- **base/**: 基础类，如聚合根、实体、值对象基类
- **context/**: 上下文，如租户上下文
- **decorators/**: 装饰器，如租户装饰器
- **events/**: 领域事件基类
- **repositories/**: 仓储接口基类
- **utils/**: 工具类，如 UUID 生成

#### 基础设施层共享 (infrastructure/)
- **cache/**: 缓存相关，Redis 缓存实现
- **config/**: 配置相关，数据库、日志、Swagger 配置
- **cqrs/**: CQRS 实现，命令总线、查询总线
- **database/**: 数据库相关，健康检查、配置
- **decorators/**: 装饰器，缓存、响应、Swagger 装饰器
- **filters/**: 过滤器，全局异常过滤器
- **gateway/**: 网关配置
- **guards/**: 守卫，JWT 认证、租户守卫
- **interceptors/**: 拦截器，审计、响应、租户拦截器
- **messaging/**: 消息队列配置
- **middleware/**: 中间件，请求日志
- **repositories/**: 仓储实现，TypeORM 基础仓储

---

## 🧪 test/ - 测试目录

```
test/
├── app.e2e-spec.ts               # 🧪 端到端测试
└── jest-e2e.json                 # ⚙️ Jest 端到端测试配置
```

#### 测试文件说明
- **app.e2e-spec.ts**: 端到端测试文件，测试整个应用的集成
- **jest-e2e.json**: Jest 端到端测试的配置文件

---

## 📜 scripts/ - 脚本目录

```
scripts/
└── (各种脚本文件)
```

#### 脚本说明
- 包含各种构建、部署、数据库迁移等脚本
- 自动化工具和辅助脚本

---

## 📦 配置文件

### package.json
```json
{
  "name": "smart-campus",
  "version": "0.0.1",
  "description": "智慧校园平台",
  "scripts": {
    "build": "nest build",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "test": "jest",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  }
}
```

### tsconfig.json
TypeScript 编译配置，定义编译选项和路径映射。

### nest-cli.json
NestJS CLI 配置，定义项目结构和构建选项。

### eslint.config.mjs
ESLint 代码规范配置，定义代码质量规则。

---

## 🎯 目录结构设计原则

### 1. 领域驱动设计 (DDD)
- 按业务领域划分上下文
- 每个上下文内部按分层架构组织
- 清晰的领域边界和职责分离

### 2. 分层架构
- **接口层**: 处理外部请求
- **应用层**: 协调业务用例
- **领域层**: 核心业务逻辑
- **基础设施层**: 技术实现细节

### 3. 依赖倒置
- 高层模块不依赖低层模块
- 都依赖抽象接口
- 依赖注入容器管理依赖关系

### 4. 单一职责
- 每个文件只负责一个功能
- 每个目录只包含相关文件
- 清晰的模块边界

### 5. 开闭原则
- 对扩展开放
- 对修改关闭
- 通过接口和抽象实现扩展

---

## 🔄 文件命名规范

### 1. 类文件
- 使用 PascalCase 命名
- 文件名与类名一致
- 例如: `UserService.ts`, `AccessRecord.ts`

### 2. 接口文件
- 使用 PascalCase 命名
- 以 `.interface.ts` 结尾
- 例如: `UserRepository.interface.ts`

### 3. 类型文件
- 使用 kebab-case 命名
- 以 `.types.ts` 结尾
- 例如: `user.types.ts`

### 4. 测试文件
- 使用 `.spec.ts` 结尾
- 例如: `user.service.spec.ts`

### 5. 配置文件
- 使用 kebab-case 命名
- 例如: `database.config.ts`

---

## 📋 开发指南

### 1. 添加新功能
1. 在对应的上下文中创建文件
2. 按分层架构组织代码
3. 遵循命名规范
4. 添加相应的测试

### 2. 修改现有功能
1. 找到对应的文件位置
2. 遵循开闭原则
3. 保持向后兼容
4. 更新相关文档

### 3. 代码审查
1. 检查目录结构是否合理
2. 验证命名规范
3. 确认依赖关系
4. 检查测试覆盖

---

*文档版本: 1.0*
*最后更新: 2024年12月*

import { BaseEntity } from './base-entity';
import { DomainEvent } from '../events/domain-event';

/**
 * 聚合根基类
 * 所有聚合根都应该继承此类
 */
export abstract class AggregateRoot extends BaseEntity {
  private _domainEvents: DomainEvent[] = [];

  /**
   * 获取领域事件列表
   */
  get domainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  /**
   * 添加领域事件
   */
  protected addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }

  /**
   * 清除领域事件
   */
  clearDomainEvents(): void {
    this._domainEvents = [];
  }

  /**
   * 标记为已修改
   */
  protected markAsModified(): void {
    this.updatedAt = new Date();
  }
}

import { Module, Global } from '@nestjs/common';
import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisStore } from 'cache-manager-redis-store';
import { CacheService } from './cache.service';
import { CacheInterceptor } from './cache.interceptor';
import { RedisCacheConfigFactory } from './redis.config';

/**
 * 缓存模块
 * 提供Redis缓存功能和相关服务
 */
@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // 验证Redis配置
        RedisCacheConfigFactory.validateConfig(configService);

        // 创建缓存配置
        return RedisCacheConfigFactory.createCacheOptions(configService);
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CacheService,
    CacheInterceptor,
  ],
  exports: [
    NestCacheModule,
    CacheService,
    CacheInterceptor,
  ],
})
export class CacheModule {
  /**
   * 创建根模块配置
   */
  static forRoot() {
    return {
      module: CacheModule,
      global: true,
    };
  }

  /**
   * 创建功能模块配置
   */
  static forFeature() {
    return {
      module: CacheModule,
    };
  }
}

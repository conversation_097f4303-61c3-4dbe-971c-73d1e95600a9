const mysql = require('mysql2/promise');

async function insertMenus() {
  let connection;

  try {
    console.log('准备连接数据库...');
    connection = await mysql.createConnection({
      host: '*************',
      user: 'root',
      password: 'mysql.12345',
      database: 'smart_campus',
      charset: 'utf8mb4',
    });
    console.log('数据库连接成功');

    // 菜单数据
    const menus = [
      {
        id: '1',
        name: '仪表盘',
        path: '/dashboard',
        component: 'Dashboard',
        icon: 'dashboard',
        parent_id: null,
        sort: 1,
        type: 'catalog',
        remark: '系统仪表盘',
      },
      {
        id: '1-1',
        name: '分析页',
        path: '/dashboard/analysis',
        component: './dashboard/analysis',
        icon: 'smile',
        parent_id: '1',
        sort: 1,
        type: 'menu',
        remark: '数据分析页面',
      },
      {
        id: '1-2',
        name: '监控页',
        path: '/dashboard/monitor',
        component: './dashboard/monitor',
        icon: 'smile',
        parent_id: '1',
        sort: 2,
        type: 'menu',
        remark: '系统监控页面',
      },
      {
        id: '1-3',
        name: '工作台',
        path: '/dashboard/workplace',
        component: './dashboard/workplace',
        icon: 'smile',
        parent_id: '1',
        sort: 3,
        type: 'menu',
        remark: '工作台页面',
      },
      {
        id: '2',
        name: '表单页',
        path: '/form',
        component: 'Form',
        icon: 'form',
        parent_id: null,
        sort: 2,
        type: 'catalog',
        remark: '表单相关页面',
      },
      {
        id: '2-1',
        name: '基础表单',
        path: '/form/basic-form',
        component: './form/basic-form',
        icon: 'smile',
        parent_id: '2',
        sort: 1,
        type: 'menu',
        remark: '基础表单页面',
      },
      {
        id: '2-2',
        name: '分步表单',
        path: '/form/step-form',
        component: './form/step-form',
        icon: 'smile',
        parent_id: '2',
        sort: 2,
        type: 'menu',
        remark: '分步表单页面',
      },
      {
        id: '2-3',
        name: '高级表单',
        path: '/form/advanced-form',
        component: './form/advanced-form',
        icon: 'smile',
        parent_id: '2',
        sort: 3,
        type: 'menu',
        remark: '高级表单页面',
      },
      {
        id: '3',
        name: '列表页',
        path: '/list',
        component: 'List',
        icon: 'table',
        parent_id: null,
        sort: 3,
        type: 'catalog',
        remark: '列表相关页面',
      },
      {
        id: '3-1',
        name: '查询表格',
        path: '/list/table-list',
        component: './table-list',
        icon: 'smile',
        parent_id: '3',
        sort: 1,
        type: 'menu',
        remark: '查询表格页面',
      },
      {
        id: '3-2',
        name: '标准列表',
        path: '/list/basic-list',
        component: './list/basic-list',
        icon: 'smile',
        parent_id: '3',
        sort: 2,
        type: 'menu',
        remark: '标准列表页面',
      },
      {
        id: '3-3',
        name: '卡片列表',
        path: '/list/card-list',
        component: './list/card-list',
        icon: 'smile',
        parent_id: '3',
        sort: 3,
        type: 'menu',
        remark: '卡片列表页面',
      },
      {
        id: '3-4',
        name: '搜索列表',
        path: '/list/search',
        component: 'SearchList',
        icon: 'smile',
        parent_id: '3',
        sort: 4,
        type: 'catalog',
        remark: '搜索列表',
      },
      {
        id: '3-4-1',
        name: '文章',
        path: '/list/search/articles',
        component: './list/search/articles',
        icon: 'smile',
        parent_id: '3-4',
        sort: 1,
        type: 'menu',
        remark: '文章搜索页面',
      },
      {
        id: '3-4-2',
        name: '项目',
        path: '/list/search/projects',
        component: './list/search/projects',
        icon: 'smile',
        parent_id: '3-4',
        sort: 2,
        type: 'menu',
        remark: '项目搜索页面',
      },
      {
        id: '3-4-3',
        name: '应用',
        path: '/list/search/applications',
        component: './list/search/applications',
        icon: 'smile',
        parent_id: '3-4',
        sort: 3,
        type: 'menu',
        remark: '应用搜索页面',
      },
      {
        id: '4',
        name: '详情页',
        path: '/profile',
        component: 'Profile',
        icon: 'profile',
        parent_id: null,
        sort: 4,
        type: 'catalog',
        remark: '详情相关页面',
      },
      {
        id: '4-1',
        name: '基础详情页',
        path: '/profile/basic',
        component: './profile/basic',
        icon: 'smile',
        parent_id: '4',
        sort: 1,
        type: 'menu',
        remark: '基础详情页面',
      },
      {
        id: '4-2',
        name: '高级详情页',
        path: '/profile/advanced',
        component: './profile/advanced',
        icon: 'smile',
        parent_id: '4',
        sort: 2,
        type: 'menu',
        remark: '高级详情页面',
      },
      {
        id: '5',
        name: '结果页',
        path: '/result',
        component: 'Result',
        icon: 'CheckCircleOutlined',
        parent_id: null,
        sort: 5,
        type: 'catalog',
        remark: '结果相关页面',
      },
      {
        id: '5-1',
        name: '成功页',
        path: '/result/success',
        component: './result/success',
        icon: 'smile',
        parent_id: '5',
        sort: 1,
        type: 'menu',
        remark: '成功结果页面',
      },
      {
        id: '5-2',
        name: '失败页',
        path: '/result/fail',
        component: './result/fail',
        icon: 'smile',
        parent_id: '5',
        sort: 2,
        type: 'menu',
        remark: '失败结果页面',
      },
      {
        id: '6',
        name: '异常页',
        path: '/exception',
        component: 'Exception',
        icon: 'warning',
        parent_id: null,
        sort: 6,
        type: 'catalog',
        remark: '异常相关页面',
      },
      {
        id: '6-1',
        name: '403',
        path: '/exception/403',
        component: './exception/403',
        icon: 'smile',
        parent_id: '6',
        sort: 1,
        type: 'menu',
        remark: '403错误页面',
      },
      {
        id: '6-2',
        name: '404',
        path: '/exception/404',
        component: './exception/404',
        icon: 'smile',
        parent_id: '6',
        sort: 2,
        type: 'menu',
        remark: '404错误页面',
      },
      {
        id: '6-3',
        name: '500',
        path: '/exception/500',
        component: './exception/500',
        icon: 'smile',
        parent_id: '6',
        sort: 3,
        type: 'menu',
        remark: '500错误页面',
      },
      {
        id: '7',
        name: '个人页',
        path: '/account',
        component: 'Account',
        icon: 'user',
        parent_id: null,
        sort: 7,
        type: 'catalog',
        remark: '个人相关页面',
      },
      {
        id: '7-1',
        name: '个人中心',
        path: '/account/center',
        component: './account/center',
        icon: 'smile',
        parent_id: '7',
        sort: 1,
        type: 'menu',
        remark: '个人中心页面',
      },
      {
        id: '7-2',
        name: '个人设置',
        path: '/account/settings',
        component: './account/settings',
        icon: 'smile',
        parent_id: '7',
        sort: 2,
        type: 'menu',
        remark: '个人设置页面',
      },
    ];

    console.log(`准备插入 ${menus.length} 条菜单数据...`);

    // 逐条插入菜单数据
    for (const menu of menus) {
      try {
        const sql = `
          INSERT INTO sys_menu (id, name, path, component, icon, parent_id, sort, type, remark) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE 
            name = VALUES(name),
            path = VALUES(path),
            component = VALUES(component),
            icon = VALUES(icon),
            parent_id = VALUES(parent_id),
            sort = VALUES(sort),
            type = VALUES(type),
            remark = VALUES(remark),
            updated_at = CURRENT_TIMESTAMP
        `;

        const values = [
          menu.id,
          menu.name,
          menu.path,
          menu.component,
          menu.icon,
          menu.parent_id,
          menu.sort,
          menu.type,
          menu.remark,
        ];

        await connection.execute(sql, values);
        console.log(`✓ 插入菜单: ${menu.name} (${menu.id})`);
      } catch (menuErr) {
        console.error(
          `✗ 插入菜单失败: ${menu.name} (${menu.id})`,
          menuErr.message,
        );
      }
    }

    console.log('\n所有菜单数据插入完成！');

    // 验证插入结果
    const [rows] = await connection.execute(
      'SELECT COUNT(*) as count FROM sys_menu',
    );
    console.log(`数据库中共有 ${rows[0].count} 条菜单记录`);

    // 显示所有菜单
    const [allMenus] = await connection.execute(
      'SELECT id, name, parent_id, sort, type FROM sys_menu ORDER BY sort, id',
    );
    console.log('\n菜单列表:');
    allMenus.forEach((menu) => {
      const indent = menu.parent_id ? '  ' : '';
      console.log(`${indent}${menu.name} (${menu.id}) - ${menu.type}`);
    });
  } catch (error) {
    console.error('插入菜单数据失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n数据库连接已关闭');
    }
  }
}

insertMenus();

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { TenantContext } from '../../domain/context/tenant.context';
import {
  TENANT_ISOLATION_KEY,
  CROSS_TENANT_KEY,
  TENANT_ADMIN_KEY,
  SUPER_ADMIN_KEY,
} from '../../domain/decorators/tenant.decorator';

/**
 * 多租户守卫
 * 验证用户的租户访问权限
 */
@Injectable()
export class TenantGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const user = (request as any).user;

    // 检查是否需要超级管理员权限
    const requiresSuperAdmin = this.reflector.getAllAndOverride<boolean>(
      SUPER_ADMIN_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (requiresSuperAdmin) {
      return this.validateSuperAdmin(user);
    }

    // 检查是否需要租户管理员权限
    const requiresTenantAdmin = this.reflector.getAllAndOverride<boolean>(
      TENANT_ADMIN_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (requiresTenantAdmin) {
      return this.validateTenantAdmin(user);
    }

    // 检查是否允许跨租户访问
    const allowCrossTenant = this.reflector.getAllAndOverride<boolean>(
      CROSS_TENANT_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (allowCrossTenant) {
      return this.validateCrossTenantAccess(user);
    }

    // 检查租户隔离
    const tenantIsolation = this.reflector.getAllAndOverride<boolean>(
      TENANT_ISOLATION_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (tenantIsolation !== false) {
      return this.validateTenantIsolation(user, request);
    }

    return true;
  }

  /**
   * 验证超级管理员权限
   */
  private validateSuperAdmin(user: any): boolean {
    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    const isSuperAdmin = user.roles?.includes('super_admin') || 
                        user.permissions?.includes('super_admin');

    if (!isSuperAdmin) {
      throw new ForbiddenException('Super admin access required');
    }

    return true;
  }

  /**
   * 验证租户管理员权限
   */
  private validateTenantAdmin(user: any): boolean {
    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    const isTenantAdmin = user.roles?.includes('tenant_admin') || 
                         user.roles?.includes('admin') ||
                         user.permissions?.includes('tenant_admin');

    if (!isTenantAdmin) {
      throw new ForbiddenException('Tenant admin access required');
    }

    return true;
  }

  /**
   * 验证跨租户访问权限
   */
  private validateCrossTenantAccess(user: any): boolean {
    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    const canCrossTenant = user.permissions?.includes('cross_tenant_access') ||
                          user.roles?.includes('super_admin') ||
                          user.roles?.includes('system_admin');

    if (!canCrossTenant) {
      throw new ForbiddenException('Cross-tenant access not permitted');
    }

    return true;
  }

  /**
   * 验证租户隔离
   */
  private validateTenantIsolation(user: any, request: Request): boolean {
    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    const currentTenantId = TenantContext.getCurrentTenantId();
    const userTenantId = user.tenantId || user.tenant_id;

    if (!currentTenantId) {
      throw new ForbiddenException('Tenant context not found');
    }

    if (!userTenantId) {
      throw new ForbiddenException('User tenant information not found');
    }

    // 检查用户是否属于当前租户
    if (userTenantId !== currentTenantId) {
      // 允许系统租户访问所有资源
      if (currentTenantId === TenantContext.getDefaultTenantId()) {
        return true;
      }

      // 检查是否有跨租户权限
      const canCrossTenant = user.permissions?.includes('cross_tenant_access') ||
                            user.roles?.includes('super_admin');

      if (!canCrossTenant) {
        throw new ForbiddenException(
          `Access denied: user belongs to tenant ${userTenantId}, current tenant is ${currentTenantId}`
        );
      }
    }

    return true;
  }
}

/**
 * 租户资源守卫
 * 验证用户是否有权限访问特定租户的资源
 */
@Injectable()
export class TenantResourceGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const user = (request as any).user;

    if (!user) {
      throw new UnauthorizedException('Authentication required');
    }

    // 从路径参数中提取资源的租户ID
    const resourceTenantId = this.extractResourceTenantId(request);
    
    if (!resourceTenantId) {
      // 如果无法提取资源租户ID，使用当前租户上下文
      return true;
    }

    const userTenantId = user.tenantId || user.tenant_id;

    // 检查用户是否有权限访问该租户的资源
    if (userTenantId !== resourceTenantId) {
      // 检查是否有跨租户权限
      const canCrossTenant = user.permissions?.includes('cross_tenant_access') ||
                            user.roles?.includes('super_admin') ||
                            user.roles?.includes('system_admin');

      if (!canCrossTenant) {
        throw new ForbiddenException(
          `Access denied: cannot access resources of tenant ${resourceTenantId}`
        );
      }
    }

    return true;
  }

  /**
   * 从请求中提取资源的租户ID
   */
  private extractResourceTenantId(request: Request): string | null {
    // 从路径参数中提取
    const params = request.params;
    if (params.tenantId) {
      return params.tenantId;
    }

    // 从查询参数中提取
    const query = request.query;
    if (query.tenantId) {
      return query.tenantId as string;
    }

    // 从请求体中提取
    const body = request.body;
    if (body && body.tenantId) {
      return body.tenantId;
    }

    return null;
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { IdentityAccessModule } from '../identity-access.module';
import { StudentApplicationService } from '../application/services/student-application.service';
import { CreateStudentCommand } from '../application/commands/create-student.command';
import { User, StudentUser } from '../domain/aggregates/user.aggregate';

describe('Student Integration Tests', () => {
  let module: TestingModule;
  let studentService: StudentApplicationService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.test', '.env'],
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [User, StudentUser],
          synchronize: true,
          logging: false,
        }),
        IdentityAccessModule,
      ],
    }).compile();

    studentService = module.get<StudentApplicationService>(StudentApplicationService);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('Student Creation', () => {
    it('should create a student successfully', async () => {
      // Arrange
      const command = new CreateStudentCommand(
        '张三',
        '13800138000',
        1, // 男性
        undefined, // avatar
        18, // age
        '2005-01-01', // birthday
        '123456789012345678', // cardNo
        '身份证', // cardType
        'class-001', // classId
        'grade-001', // gradeId
        '2023-09-01', // enrollmentTime
        undefined // graduationTime
      );

      // Act
      const result = await studentService.createStudent(command);

      // Assert
      expect(result).toBeDefined();
      expect(result.getProfile().name).toBe('张三');
      expect(result.getProfile().phone).toBe('13800138000');
      expect(result.isStudent()).toBe(true);
      expect(result.getClassId()).toBe('class-001');
      expect(result.getGradeId()).toBe('grade-001');
    });

    it('should throw error for invalid phone number', async () => {
      // Arrange
      const command = new CreateStudentCommand(
        '李四',
        '123', // 无效手机号
        1,
        undefined,
        18,
        '2005-01-01',
        '123456789012345678',
        '身份证',
        'class-001',
        'grade-001',
        '2023-09-01',
        undefined
      );

      // Act & Assert
      await expect(studentService.createStudent(command)).rejects.toThrow();
    });
  });

  describe('Student Query', () => {
    let createdStudentId: string;

    beforeEach(async () => {
      const command = new CreateStudentCommand(
        '王五',
        '13900139000',
        2, // 女性
        undefined,
        17,
        '2006-01-01',
        '123456789012345679',
        '身份证',
        'class-002',
        'grade-001',
        '2023-09-01',
        undefined
      );

      const student = await studentService.createStudent(command);
      createdStudentId = student.id;
    });

    it('should get student by id', async () => {
      // Act
      const student = await studentService.getStudentById(createdStudentId);

      // Assert
      expect(student).toBeDefined();
      expect(student.id).toBe(createdStudentId);
      expect(student.getProfile().name).toBe('王五');
    });

    it('should get students by class', async () => {
      // Act
      const students = await studentService.getStudentsByClass('class-002');

      // Assert
      expect(students).toBeDefined();
      expect(students.length).toBeGreaterThan(0);
      expect(students[0].getClassId()).toBe('class-002');
    });
  });

  describe('Student Update', () => {
    let createdStudentId: string;

    beforeEach(async () => {
      const command = new CreateStudentCommand(
        '赵六',
        '13700137000',
        1,
        undefined,
        19,
        '2004-01-01',
        '123456789012345680',
        '身份证',
        'class-003',
        'grade-002',
        '2022-09-01',
        undefined
      );

      const student = await studentService.createStudent(command);
      createdStudentId = student.id;
    });

    it('should update student information', async () => {
      // Arrange
      const updateCommand = {
        id: createdStudentId,
        name: '赵六六',
        phone: '13700137001',
        gender: 1,
        avatar: 'http://example.com/avatar.jpg',
        age: 20,
        birthday: '2003-01-01',
        cardNo: '123456789012345680',
        cardType: '身份证',
        classId: 'class-004',
        gradeId: 'grade-002',
        enrollmentTime: '2022-09-01',
        graduationTime: undefined
      };

      // Act
      const updatedStudent = await studentService.updateStudent(updateCommand as any);

      // Assert
      expect(updatedStudent.getProfile().name).toBe('赵六六');
      expect(updatedStudent.getProfile().phone).toBe('13700137001');
      expect(updatedStudent.getClassId()).toBe('class-004');
    });
  });
});

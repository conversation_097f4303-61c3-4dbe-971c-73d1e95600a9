import { DomainEvent } from '@/shared/domain/events/domain-event';
import { UserProfile } from '../value-objects/user-profile';

/**
 * 用户更新事件
 */
export class UserUpdatedEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly oldProfile: UserProfile,
    public readonly newProfile: UserProfile,
  ) {
    super(userId);
  }

  getEventName(): string {
    return 'user.updated';
  }

  getEventData(): Record<string, any> {
    return {
      userId: this.userId,
      oldProfile: {
        name: this.oldProfile.name,
        phone: this.oldProfile.phone,
        gender: this.oldProfile.gender,
        avatar: this.oldProfile.avatar,
        age: this.oldProfile.age,
        birthday: this.oldProfile.birthday,
        cardNo: this.oldProfile.cardNo,
        cardType: this.oldProfile.cardType,
      },
      newProfile: {
        name: this.newProfile.name,
        phone: this.newProfile.phone,
        gender: this.newProfile.gender,
        avatar: this.newProfile.avatar,
        age: this.newProfile.age,
        birthday: this.newProfile.birthday,
        cardNo: this.newProfile.cardNo,
        cardType: this.newProfile.cardType,
      },
    };
  }

  /**
   * 获取变更的字段
   */
  getChangedFields(): string[] {
    const changes: string[] = [];

    if (this.oldProfile.name !== this.newProfile.name) {
      changes.push('name');
    }
    if (this.oldProfile.phone !== this.newProfile.phone) {
      changes.push('phone');
    }
    if (this.oldProfile.gender !== this.newProfile.gender) {
      changes.push('gender');
    }
    if (this.oldProfile.avatar !== this.newProfile.avatar) {
      changes.push('avatar');
    }
    if (this.oldProfile.age !== this.newProfile.age) {
      changes.push('age');
    }
    if (
      this.oldProfile.birthday?.getTime() !==
      this.newProfile.birthday?.getTime()
    ) {
      changes.push('birthday');
    }
    if (this.oldProfile.cardNo !== this.newProfile.cardNo) {
      changes.push('cardNo');
    }
    if (this.oldProfile.cardType !== this.newProfile.cardType) {
      changes.push('cardType');
    }

    return changes;
  }
}

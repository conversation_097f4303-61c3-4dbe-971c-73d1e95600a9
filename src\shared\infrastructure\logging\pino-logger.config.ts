import { Injectable, Optional } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import pino from 'pino';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Pino 日志配置服务
 */
@Injectable()
export class PinoLoggerConfig {
  constructor(private readonly configService?: ConfigService) {}

  /**
   * 创建 Pino 日志实例
   */
  createLogger(): pino.Logger {
    const logLevel = this.getConfig('LOG_LEVEL', 'info');
    const nodeEnv = this.getConfig('NODE_ENV', 'development');

    // 确保日志目录存在
    this.ensureLogDirectory();

    // 基础配置
    const baseConfig: pino.LoggerOptions = {
      level: logLevel,
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: {
        level: (label) => {
          return { level: label.toUpperCase() };
        },
        log: (object) => {
          // 添加自定义字段
          return {
            ...object,
            hostname: require('os').hostname(),
            pid: process.pid,
            environment: nodeEnv,
          };
        },
      },
      serializers: {
        req: pino.stdSerializers.req,
        res: pino.stdSerializers.res,
        err: pino.stdSerializers.err,
      },
    };

    // 开发环境配置
    if (nodeEnv === 'development') {
      return this.createDevelopmentLogger(baseConfig);
    }

    // 生产环境配置
    return this.createProductionLogger(baseConfig);
  }

  /**
   * 创建开发环境日志配置
   * 同时输出到控制台（美化格式）和文件（JSON格式）
   */
  private createDevelopmentLogger(baseConfig: pino.LoggerOptions): pino.Logger {
    // 控制台输出流（美化格式）
    const prettyTransport = pino.transport({
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'yyyy-mm-dd HH:MM:ss',
        ignore: 'pid,hostname,environment',
        messageFormat: '[{context}] {msg}',
        levelFirst: true,
      },
    });

    // 文件输出流（按级别分离）
    const fileStreams = this.createFileStreams();

    // 合并所有流：控制台 + 文件
    const allStreams = [
      { stream: prettyTransport },
      ...fileStreams,
    ];

    return pino(baseConfig, pino.multistream(allStreams));
  }

  /**
   * 创建生产环境日志配置
   */
  private createProductionLogger(baseConfig: pino.LoggerOptions): pino.Logger {
    // 生产环境：同时输出到控制台和文件
    const fileDestination = pino.destination({
      dest: path.join(process.cwd(), 'logs', `app-${this.getDateTimeString()}.log`),
      sync: false,
      mkdir: true,
    });

    // 使用 multistream 同时输出到控制台和文件
    const streams = [
      { stream: process.stdout },
      { stream: fileDestination },
    ];

    return pino(baseConfig, pino.multistream(streams));
  }

  /**
   * 创建文件流配置
   */
  private createFileStreams(): pino.StreamEntry[] {
    const logLevels = ['error', 'warn', 'info', 'debug'];
    const streams: pino.StreamEntry[] = [];

    logLevels.forEach(level => {
      const logDir = path.join(process.cwd(), 'logs');
      const fileName = `${level}-${this.getDateTimeString()}.log`;
      const filePath = path.join(logDir, fileName);

      // 创建文件写入流
      const fileStream = pino.destination({
        dest: filePath,
        sync: false,
        mkdir: true,
      });

      streams.push({
        stream: fileStream,
        level: level as pino.Level,
      });
    });

    return streams;
  }

  /**
   * 获取日期时间字符串
   * 格式: yyyyMMddHH
   */
  private getDateTimeString(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');

    return `${year}${month}${day}${hour}`;
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(): void {
    const logDir = path.join(process.cwd(), 'logs');

    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  /**
   * 获取配置值（兼容无 ConfigService 的情况）
   */
  private getConfig(key: string, defaultValue: string): string {
    if (this.configService) {
      return this.configService.get<string>(key, defaultValue);
    }
    return process.env[key] || defaultValue;
  }

  /**
   * 获取日志配置信息
   */
  getLoggerInfo(): {
    level: string;
    environment: string;
    logDirectory: string;
    filePattern: string;
  } {
    return {
      level: this.getConfig('LOG_LEVEL', 'info'),
      environment: this.getConfig('NODE_ENV', 'development'),
      logDirectory: path.join(process.cwd(), 'logs'),
      filePattern: 'level-yyyyMMddHH.log',
    };
  }
}

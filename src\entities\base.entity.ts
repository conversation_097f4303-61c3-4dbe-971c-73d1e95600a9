import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
  VersionColumn,
  BaseEntity as TypeOrmBaseEntity,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UuidUtil } from '../common/utils/uuid.util';
import { TenantContext } from '../common/context/tenant.context';

/**
 * 基础实体类
 * 包含所有实体都需要的公用字段
 */
export abstract class BaseEntity extends TypeOrmBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    name: 'tenant_id',
    type: 'varchar',
    length: 36,
    comment: '租户ID',
    nullable: false,
  })
  @Exclude()
  tenantId: string;

  @VersionColumn({
    name: 'revision',
    comment: '乐观锁版本号',
  })
  @Exclude()
  revision: number;

  @CreateDateColumn({
    name: 'created_at',
    comment: '创建时间',
    type: 'datetime',
  })
  @Exclude()
  createdAt: Date;

  @Column({
    name: 'created_by',
    comment: '创建人',
    nullable: false,
    default: '',
  })
  @Exclude()
  createdBy: string;

  @UpdateDateColumn({
    name: 'updated_at',
    comment: '更新时间',
    type: 'datetime',
  })
  @Exclude()
  updatedAt: Date;

  @Column({
    name: 'updated_by',
    comment: '更新人',
    nullable: false,
    default: '',
  })
  @Exclude()
  updatedBy: string;

  @DeleteDateColumn({
    name: 'deleted_at',
    comment: '删除时间',
    nullable: true,
  })
  @Exclude()
  deletedAt?: Date;

  @Column({
    name: 'deleted_by',
    comment: '删除人',
    nullable: false,
    default: '',
  })
  @Exclude()
  deletedBy: string;

  @Column({
    name: 'is_deleted',
    comment: '是否删除',
    default: false,
    type: 'boolean',
  })
  @Exclude()
  isDeleted: boolean;

  /**
   * 在插入前自动生成 UUID v7 和设置租户ID
   */
  @BeforeInsert()
  generateId() {
    if (!this.id) {
      this.id = UuidUtil.generate();
    }

    // 自动设置租户ID（如果未设置）
    if (!this.tenantId) {
      this.tenantId = this.getCurrentTenantId();
    }
  }

  /**
   * 在更新前验证租户ID
   */
  @BeforeUpdate()
  validateTenant() {
    const currentTenantId = this.getCurrentTenantId();
    if (this.tenantId !== currentTenantId) {
      throw new Error(`Tenant mismatch: entity belongs to ${this.tenantId}, current tenant is ${currentTenantId}`);
    }
  }

  /**
   * 获取当前租户ID
   */
  private getCurrentTenantId(): string {
    return TenantContext.getCurrentTenantId() || TenantContext.getDefaultTenantId();
  }

  /**
   * 设置租户ID
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * 获取租户ID
   */
  getTenantId(): string {
    return this.tenantId;
  }

  /**
   * 获取当前版本号
   */
  getRevision(): number {
    return this.revision;
  }

  /**
   * 检查是否属于指定租户
   */
  belongsToTenant(tenantId: string): boolean {
    return this.tenantId === tenantId;
  }

  /**
   * 软删除
   */
  softDelete(deletedBy?: string): void {
    this.isDeleted = true;
    this.deletedAt = new Date();
    if (deletedBy) {
      this.deletedBy = deletedBy;
    }
  }

  /**
   * 恢复删除
   */
  restore(): void {
    this.isDeleted = false;
    this.deletedAt = undefined;
    this.deletedBy = '';
  }

  /**
   * 设置创建者
   */
  setCreatedBy(userId: string): void {
    this.createdBy = userId;
  }

  /**
   * 设置更新者
   */
  setUpdatedBy(userId: string): void {
    this.updatedBy = userId;
  }

  /**
   * 获取创建者ID
   */
  getCreatedBy(): string {
    return this.createdBy;
  }

  /**
   * 获取更新者ID
   */
  getUpdatedBy(): string {
    return this.updatedBy;
  }

  /**
   * 获取删除者ID
   */
  getDeletedBy(): string {
    return this.deletedBy;
  }
}

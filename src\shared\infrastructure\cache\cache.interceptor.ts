import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from './cache.service';
import { TenantContext } from '../../domain/context/tenant.context';

// 缓存装饰器的元数据键
export const CACHE_KEY_METADATA = 'cache_key_metadata';
export const CACHE_TTL_METADATA = 'cache_ttl_metadata';
export const CACHE_PARAMS_METADATA = 'cache_params_metadata';
export const CACHE_KEY_GENERATOR_METADATA = 'cache_key_generator_metadata';
export const CLEAR_CACHE_KEYS_METADATA = 'clear_cache_keys_metadata';

/**
 * 缓存拦截器
 * 支持动态缓存键生成和多租户缓存隔离
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const handler = context.getHandler();
    const cacheKey = this.reflector.get<string>(CACHE_KEY_METADATA, handler);

    // 如果没有缓存键，直接执行原方法
    if (!cacheKey) {
      return this.handleClearCache(context, next, handler);
    }

    const ttl = this.reflector.get<number>(CACHE_TTL_METADATA, handler);
    const params = this.reflector.get<number[]>(CACHE_PARAMS_METADATA, handler);
    const keyGenerator = this.reflector.get<Function>(
      CACHE_KEY_GENERATOR_METADATA,
      handler,
    );

    // 生成最终的缓存键
    const finalCacheKey = this.generateCacheKey(
      context,
      cacheKey,
      params,
      keyGenerator,
    );

    // 尝试从缓存获取数据
    const cachedData = await this.cacheService.get(finalCacheKey);
    if (cachedData !== null) {
      this.logger.debug(`Cache hit: ${finalCacheKey}`);
      return of(cachedData);
    }

    // 缓存中没有数据，执行原始方法并缓存结果
    return next.handle().pipe(
      tap(async (data) => {
        try {
          await this.cacheService.set(finalCacheKey, data, ttl);
          this.logger.debug(
            `Cache set: ${finalCacheKey}${ttl ? ` (TTL: ${ttl}s)` : ''}`,
          );
        } catch (error) {
          this.logger.error(`Cache set error for key ${finalCacheKey}:`, error);
        }
      }),
    );
  }

  /**
   * 处理缓存清除
   */
  private handleClearCache(
    context: ExecutionContext,
    next: CallHandler,
    handler: any,
  ): Observable<any> {
    const clearCacheKeys = this.reflector.get<string[]>(
      CLEAR_CACHE_KEYS_METADATA,
      handler,
    );

    if (!clearCacheKeys || clearCacheKeys.length === 0) {
      return next.handle();
    }

    return next.handle().pipe(
      tap(async () => {
        try {
          await this.cacheService.mdel(clearCacheKeys);
          this.logger.debug(`Cache cleared: ${clearCacheKeys.join(', ')}`);
        } catch (error) {
          this.logger.error(
            `Cache clear error for keys ${clearCacheKeys.join(', ')}:`,
            error,
          );
        }
      }),
    );
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    context: ExecutionContext,
    baseKey: string,
    params?: number[],
    keyGenerator?: Function,
  ): string {
    const request = context.switchToHttp().getRequest();
    const args = context.getArgs();

    // 添加租户隔离
    const tenantId = TenantContext.getCurrentTenantId();
    const tenantPrefix = tenantId ? `tenant:${tenantId}:` : '';

    // 使用自定义键生成器
    if (keyGenerator) {
      const customKey = keyGenerator(args);
      return `${tenantPrefix}${customKey}`;
    }

    // 使用参数生成键
    if (params && params.length > 0) {
      const paramValues = params
        .map((index) => {
          const arg = args[index];
          return arg !== undefined ? String(arg) : '';
        })
        .filter((value) => value !== '')
        .join(':');

      return paramValues
        ? `${tenantPrefix}${baseKey}:${paramValues}`
        : `${tenantPrefix}${baseKey}`;
    }

    // 使用用户ID作为默认参数（如果可用）
    const user = request?.user;
    if (user?.id) {
      return `${tenantPrefix}${baseKey}:${user.id}`;
    }

    return `${tenantPrefix}${baseKey}`;
  }
}

/**
 * 缓存装饰器选项
 */
export interface CacheOptions {
  key: string;
  ttl?: number;
  params?: number[];
  keyGenerator?: (args: any[]) => string;
}

/**
 * 缓存装饰器
 */
export function Cache(options: CacheOptions) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    Reflect.defineMetadata(CACHE_KEY_METADATA, options.key, descriptor.value);

    if (options.ttl) {
      Reflect.defineMetadata(CACHE_TTL_METADATA, options.ttl, descriptor.value);
    }

    if (options.params) {
      Reflect.defineMetadata(
        CACHE_PARAMS_METADATA,
        options.params,
        descriptor.value,
      );
    }

    if (options.keyGenerator) {
      Reflect.defineMetadata(
        CACHE_KEY_GENERATOR_METADATA,
        options.keyGenerator,
        descriptor.value,
      );
    }

    return descriptor;
  };
}

/**
 * 便捷的缓存装饰器 - 使用第一个参数
 */
export function CacheByFirstParam(key: string, ttl?: number) {
  return Cache({ key, ttl, params: [0] });
}

/**
 * 便捷的缓存装饰器 - 使用指定参数
 */
export function CacheByParams(
  key: string,
  paramIndexes: number[],
  ttl?: number,
) {
  return Cache({ key, ttl, params: paramIndexes });
}

/**
 * 动态缓存装饰器
 */
export function DynamicCache(options: CacheOptions) {
  return Cache(options);
}

/**
 * 清除缓存装饰器
 */
export function ClearCache(keys: string[]) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    Reflect.defineMetadata(CLEAR_CACHE_KEYS_METADATA, keys, descriptor.value);
    return descriptor;
  };
}

/**
 * 用户缓存装饰器
 */
export function UserCache(suffix?: string, ttl?: number) {
  return Cache({
    key: 'user',
    ttl,
    keyGenerator: (args) => {
      const userId = args[0];
      return suffix ? `user:${userId}:${suffix}` : `user:${userId}`;
    },
  });
}

/**
 * 租户缓存装饰器
 */
export function TenantCache(suffix?: string, ttl?: number) {
  return Cache({
    key: 'tenant',
    ttl,
    keyGenerator: (args) => {
      const tenantId = TenantContext.getCurrentTenantId();
      return suffix ? `tenant:${tenantId}:${suffix}` : `tenant:${tenantId}`;
    },
  });
}

/**
 * 统计缓存装饰器
 */
export function StatisticsCache(type: string, ttl?: number) {
  return Cache({
    key: `stats:${type}`,
    ttl,
    keyGenerator: (args) => {
      const period = args[0];
      const params = args.slice(1).join(':');
      return params
        ? `stats:${type}:${period}:${params}`
        : `stats:${type}:${period}`;
    },
  });
}

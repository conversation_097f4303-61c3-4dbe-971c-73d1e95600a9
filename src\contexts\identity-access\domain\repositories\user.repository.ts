import {
  BaseRepository,
  QueryRepository,
} from '@/shared/domain/repositories/base-repository';
import { User, StudentUser, TeacherUser } from '../aggregates/user.aggregate';
import { UserTypeEnum } from '../value-objects/user-type';

/**
 * 用户仓储接口
 */
export interface UserRepository extends BaseRepository<User> {
  /**
   * 根据手机号查找用户
   */
  findByPhone(phone: string): Promise<User | null>;

  /**
   * 根据用户类型查找用户列表
   */
  findByUserType(userType: UserTypeEnum): Promise<User[]>;

  /**
   * 检查手机号是否已存在
   */
  existsByPhone(phone: string): Promise<boolean>;

  /**
   * 根据身份证号查找用户
   */
  findByCardNo(cardNo: string): Promise<User | null>;

  /**
   * 批量保存用户
   */
  saveMany(users: User[]): Promise<User[]>;
}

/**
 * 学生用户仓储接口
 */
export interface StudentUserRepository extends BaseRepository<StudentUser> {
  /**
   * 根据班级ID查找学生
   */
  findByClassId(classId: string): Promise<StudentUser[]>;

  /**
   * 根据年级ID查找学生
   */
  findByGradeId(gradeId: string): Promise<StudentUser[]>;

  /**
   * 根据班级和年级查找学生
   */
  findByClassAndGrade(classId: string, gradeId: string): Promise<StudentUser[]>;
}

/**
 * 教师用户仓储接口
 */
export interface TeacherUserRepository extends BaseRepository<TeacherUser> {
  /**
   * 根据车牌号查找教师
   */
  findByCarNo(carNo: string): Promise<TeacherUser | null>;

  /**
   * 查找有车的教师
   */
  findTeachersWithCar(): Promise<TeacherUser[]>;
}

/**
 * 用户查询仓储接口
 */
export interface UserQueryRepository extends QueryRepository<User> {
  /**
   * 根据条件搜索用户
   */
  searchUsers(criteria: {
    name?: string;
    phone?: string;
    userType?: UserTypeEnum;
    isActive?: boolean;
    classId?: string;
    gradeId?: string;
  }): Promise<User[]>;

  /**
   * 获取用户统计信息
   */
  getUserStatistics(): Promise<{
    totalUsers: number;
    activeUsers: number;
    studentCount: number;
    teacherCount: number;
    parentCount: number;
    visitorCount: number;
  }>;

  /**
   * 根据年龄范围查找用户
   */
  findByAgeRange(minAge: number, maxAge: number): Promise<User[]>;

  /**
   * 查找最近注册的用户
   */
  findRecentlyRegistered(days: number, limit: number): Promise<User[]>;

  /**
   * 根据复杂条件查询用户
   */
  findByCriteria(criteria: {
    names?: string[];
    phones?: string[];
    userTypes?: string[];
    isActive?: boolean;
    ageRange?: { min: number; max: number };
    registrationDateRange?: { start: Date; end: Date };
    classIds?: string[];
    gradeIds?: string[];
    limit?: number;
    offset?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<User[]>;

  /**
   * 根据条件统计用户数量
   */
  countByCriteria(criteria: {
    names?: string[];
    phones?: string[];
    userTypes?: string[];
    isActive?: boolean;
    ageRange?: { min: number; max: number };
    registrationDateRange?: { start: Date; end: Date };
    classIds?: string[];
    gradeIds?: string[];
  }): Promise<number>;

  /**
   * 根据关键词搜索用户
   */
  searchByKeyword(
    keyword: string,
    userTypes?: string[],
    limit?: number,
  ): Promise<User[]>;
}

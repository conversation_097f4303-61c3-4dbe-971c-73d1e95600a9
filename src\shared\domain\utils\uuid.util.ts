import { v7 as uuidv7 } from 'uuid';

/**
 * UUID 工具类
 * 提供 UUID v7 生成和验证功能
 */
export class UuidUtil {
  /**
   * 生成 UUID v7
   * @returns UUID v7 字符串
   */
  static generate(): string {
    return uuidv7();
  }

  /**
   * 生成多个 UUID v7
   * @param count 生成数量
   * @returns UUID v7 字符串数组
   */
  static generateMultiple(count: number): string[] {
    return Array.from({ length: count }, () => uuidv7());
  }

  /**
   * 验证是否为有效的 UUID v7
   * @param uuid 要验证的 UUID 字符串
   * @returns 是否为有效的 UUID v7
   */
  static isValid(uuid: string): boolean {
    // UUID v7 格式: xxxxxxxx-xxxx-7xxx-xxxx-xxxxxxxxxxxx
    const uuidv7Pattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidv7Pattern.test(uuid);
  }

  /**
   * 验证是否为有效的 UUID（任意版本）
   * @param uuid 要验证的 UUID 字符串
   * @returns 是否为有效的 UUID
   */
  static isValidUuid(uuid: string): boolean {
    const uuidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-7][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidPattern.test(uuid);
  }

  /**
   * 从 UUID v7 中提取时间戳
   * @param uuid UUID v7 字符串
   * @returns 时间戳（毫秒）
   */
  static extractTimestamp(uuid: string): number {
    if (!this.isValid(uuid)) {
      throw new Error('Invalid UUID v7 format');
    }

    // UUID v7 的前 48 位包含时间戳
    const hex = uuid.replace(/-/g, '');
    const timestampHex = hex.substring(0, 12);
    return parseInt(timestampHex, 16);
  }

  /**
   * 从 UUID v7 中提取创建时间
   * @param uuid UUID v7 字符串
   * @returns Date 对象
   */
  static extractDate(uuid: string): Date {
    const timestamp = this.extractTimestamp(uuid);
    return new Date(timestamp);
  }

  /**
   * 比较两个 UUID v7 的时间顺序
   * @param uuid1 第一个 UUID v7
   * @param uuid2 第二个 UUID v7
   * @returns 负数表示 uuid1 更早，正数表示 uuid1 更晚，0 表示同时
   */
  static compareTimestamp(uuid1: string, uuid2: string): number {
    const timestamp1 = this.extractTimestamp(uuid1);
    const timestamp2 = this.extractTimestamp(uuid2);
    return timestamp1 - timestamp2;
  }

  /**
   * 生成指定时间的 UUID v7（主要用于测试）
   * @param timestamp 时间戳（毫秒）
   * @returns UUID v7 字符串
   */
  static generateWithTimestamp(timestamp: number): string {
    // 注意：这是一个简化实现，实际的 UUID v7 还包含随机性
    // 在生产环境中应该使用标准的 UUID v7 库
    const hex = timestamp.toString(16).padStart(12, '0');
    const random = Math.random()
      .toString(16)
      .substring(2, 14)
      .padStart(12, '0');

    return [
      hex.substring(0, 8),
      hex.substring(8, 12),
      '7' + random.substring(0, 3),
      '8' + random.substring(3, 6),
      random.substring(6, 18),
    ].join('-');
  }

  /**
   * 批量验证 UUID 数组
   * @param uuids UUID 字符串数组
   * @returns 验证结果数组
   */
  static validateBatch(uuids: string[]): boolean[] {
    return uuids.map((uuid) => this.isValid(uuid));
  }

  /**
   * 过滤出有效的 UUID
   * @param uuids UUID 字符串数组
   * @returns 有效的 UUID 数组
   */
  static filterValid(uuids: string[]): string[] {
    return uuids.filter((uuid) => this.isValid(uuid));
  }
}

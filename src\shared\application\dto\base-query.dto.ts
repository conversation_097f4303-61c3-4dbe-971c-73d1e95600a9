import { IsOption<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 基础查询DTO
 */
export class BaseQueryDto {
  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: '每页大小',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页大小必须是数字' })
  @Min(1, { message: '每页大小不能小于1' })
  @Max(100, { message: '每页大小不能超过100' })
  pageSize?: number = 10;

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: '排序方向',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  /**
   * 获取跳过的记录数
   */
  getSkip(): number {
    return ((this.page || 1) - 1) * (this.pageSize || 10);
  }

  /**
   * 获取排序配置
   */
  getSort(): Record<string, 'ASC' | 'DESC'> {
    const sortBy = this.sortBy || 'createdAt';
    const sortOrder = this.sortOrder || 'DESC';
    return { [sortBy]: sortOrder };
  }
}

/**
 * 日期范围查询DTO
 */
export class DateRangeQueryDto extends BaseQueryDto {
  @ApiPropertyOptional({
    description: '开始日期',
    example: '2024-01-01',
    type: 'string',
    format: 'date',
  })
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: '结束日期',
    example: '2024-12-31',
    type: 'string',
    format: 'date',
  })
  @IsOptional()
  endDate?: string;

  /**
   * 获取日期范围条件
   */
  getDateRangeCondition(field: string = 'createdAt'): Record<string, any> {
    const condition: Record<string, any> = {};

    if (this.startDate) {
      condition[field] = {
        ...condition[field],
        $gte: new Date(this.startDate),
      };
    }

    if (this.endDate) {
      const endDate = new Date(this.endDate);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
      condition[field] = { ...condition[field], $lte: endDate };
    }

    return condition;
  }
}

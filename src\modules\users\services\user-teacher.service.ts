import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like } from 'typeorm';
import { UserTeacher } from '@entities/users/user-teacher.entity';
import {
  CreateUserTeacherDto,
  UpdateUserTeacherDto,
  QueryUserTeacherDto,
  UserTeacherResponseDto,
} from '../dto/user-teacher.dto';
import { ApiException } from '@/common/exceptions/api.exception';

@Injectable()
export class UserTeacherService {
  private readonly logger = new Logger(UserTeacherService.name);

  constructor(
    @InjectRepository(UserTeacher)
    private readonly userTeacherRepository: Repository<UserTeacher>,
  ) {}

  /**
   * 创建教师
   */
  async create(createDto: CreateUserTeacherDto): Promise<UserTeacherResponseDto> {
    // 检查手机号是否已存在
    const existingPhone = await this.userTeacherRepository.findOne({
      where: { phone: createDto.phone },
    });

    if (existingPhone) {
      throw ApiException.Error('手机号已存在');
    }

    // 转换日期字符串为Date对象
    const teacherData = {
      ...createDto,
      birthday: createDto.birthday ? new Date(createDto.birthday) : undefined,
    };

    const teacher = this.userTeacherRepository.create(teacherData);
    const savedTeacher = await this.userTeacherRepository.save(teacher);
    return this.transformToResponseDto(savedTeacher);
  }

  /**
   * 根据ID查找教师
   */
  async findById(id: string): Promise<UserTeacherResponseDto> {
    const teacher = await this.userTeacherRepository.findOne({
      where: { id },
    });

    if (!teacher) {
      throw ApiException.Error('教师不存在');
    }

    return this.transformToResponseDto(teacher);
  }

  /**
   * 分页查询教师
   */
  async findMany(
    query: QueryUserTeacherDto,
  ): Promise<{ data: UserTeacherResponseDto[]; total: number; page: number; pageSize: number }> {
    const { page = 1, pageSize = 10, name, phone } = query;
    const skip = (page - 1) * pageSize;

    const whereConditions: FindOptionsWhere<UserTeacher> = {};

    if (name) {
      whereConditions.name = Like(`%${name}%`);
    }

    if (phone) {
      whereConditions.phone = Like(`%${phone}%`);
    }

    const [teachers, total] = await this.userTeacherRepository.findAndCount({
      where: whereConditions,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' },
    });

    return {
      data: teachers.map(this.transformToResponseDto),
      total,
      page,
      pageSize,
    };
  }

  /**
   * 更新教师信息
   */
  async update(id: string, updateDto: UpdateUserTeacherDto): Promise<UserTeacherResponseDto> {
    const teacher = await this.userTeacherRepository.findOne({
      where: { id },
    });

    if (!teacher) {
      throw ApiException.Error('教师不存在');
    }

    // 如果更新手机号，检查是否已存在
    if (updateDto.phone && updateDto.phone !== teacher.phone) {
      const existingPhone = await this.userTeacherRepository.findOne({
        where: { phone: updateDto.phone },
      });

      if (existingPhone) {
        throw ApiException.Error('手机号已存在');
      }
    }

    // 转换日期字符串为Date对象
    const updateData = {
      ...updateDto,
      birthday: updateDto.birthday ? new Date(updateDto.birthday) : undefined,
    };

    await this.userTeacherRepository.update(id, updateData);
    const updatedTeacher = await this.userTeacherRepository.findOne({
      where: { id },
    });

    return this.transformToResponseDto(updatedTeacher!);
  }

  /**
   * 删除教师
   */
  async delete(id: string): Promise<void> {
    const teacher = await this.userTeacherRepository.findOne({
      where: { id },
    });

    if (!teacher) {
      throw ApiException.Error('教师不存在');
    }

    await this.userTeacherRepository.softDelete(id);
  }

  /**
   * 批量删除教师
   */
  async batchDelete(ids: string[]): Promise<void> {
    await this.userTeacherRepository.softDelete(ids);
  }

  /**
   * 根据手机号查找教师
   */
  async findByPhone(phone: string): Promise<UserTeacher | null> {
    return await this.userTeacherRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto = (teacher: UserTeacher): UserTeacherResponseDto => ({
    id: teacher.id,
    name: teacher.name,
    phone: teacher.phone,
    gender: teacher.gender,
    avatar: teacher.avatar,
    age: teacher.age,
    birthday: teacher.birthday,
    cardNo: teacher.cardNo,
    cardType: teacher.cardType,
    carNo: teacher.carNo,
    createdAt: teacher.createdAt,
    updatedAt: teacher.updatedAt,
  });
}

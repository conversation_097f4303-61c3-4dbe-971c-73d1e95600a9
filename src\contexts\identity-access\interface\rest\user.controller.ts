import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/shared/infrastructure/guards/jwt-auth.guard';
import { StudentApplicationService } from '../../application/services/student-application.service';
import {
  CreateStudentDto,
  UpdateStudentDto,
  StudentQueryDto,
  StudentResponseDto,
  StudentStatisticsDto,
  BatchDeleteStudentsDto,
  TransferStudentDto,
} from '../../application/dto/student.dto';
import {
  CreateStudentCommand,
  UpdateStudentCommand,
  DeleteStudentCommand,
} from '../../application/commands/create-student.command';
import {
  ApiResponse as ApiResponseWrapper,
  PaginatedResponseDto,
} from '@/shared/application/dto/base-response.dto';

@ApiTags('用户管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('users')
export class UserController {
  constructor(
    private readonly studentApplicationService: StudentApplicationService,
  ) {}

  @Post('students')
  @ApiOperation({ summary: '创建学生' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '创建成功',
    type: StudentResponseDto,
  })
  async createStudent(
    @Body() createDto: CreateStudentDto,
  ): Promise<ApiResponseWrapper<StudentResponseDto>> {
    const command = new CreateStudentCommand(
      createDto.name,
      createDto.phone,
      createDto.gender,
      createDto.avatar,
      createDto.age,
      createDto.birthday,
      createDto.cardNo,
      createDto.cardType,
      createDto.classId,
      createDto.gradeId,
      createDto.enrollmentTime,
      createDto.graduationTime,
    );

    const student = await this.studentApplicationService.createStudent(command);
    const responseDto = this.mapToStudentResponseDto(student);

    return ApiResponseWrapper.success(responseDto, '学生创建成功');
  }

  @Get('students')
  @ApiOperation({ summary: '分页查询学生' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '查询成功',
    type: PaginatedResponseDto<StudentResponseDto>,
  })
  async getStudents(
    @Query() query: StudentQueryDto,
  ): Promise<ApiResponseWrapper<PaginatedResponseDto<StudentResponseDto>>> {
    const result = await this.studentApplicationService.getStudents(
      query.page,
      query.pageSize,
      {
        name: query.name,
        phone: query.phone,
        classId: query.classId,
        gradeId: query.gradeId,
        isActive: query.isActive,
      },
      {
        sortBy: query.sortBy,
        sortOrder: query.sortOrder,
      },
    );

    const responseData = {
      ...result,
      data: result.data.map((student) => this.mapToStudentResponseDto(student)),
    };

    return ApiResponseWrapper.success(responseData, '学生列表查询成功');
  }

  @Get('students/search')
  @ApiOperation({ summary: '搜索学生' })
  @ApiQuery({ name: 'keyword', description: '搜索关键词' })
  @ApiQuery({ name: 'classId', description: '班级ID', required: false })
  @ApiQuery({ name: 'gradeId', description: '年级ID', required: false })
  @ApiQuery({ name: 'limit', description: '限制数量', required: false })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索成功',
    type: [StudentResponseDto],
  })
  async searchStudents(
    @Query('keyword') keyword: string,
    @Query('classId') classId?: string,
    @Query('gradeId') gradeId?: string,
    @Query('limit') limit?: number,
  ): Promise<ApiResponseWrapper<StudentResponseDto[]>> {
    const students = await this.studentApplicationService.searchStudents(
      keyword,
      classId,
      gradeId,
      limit,
    );

    const responseData = students.map((student) =>
      this.mapToStudentResponseDto(student),
    );
    return ApiResponseWrapper.success(responseData, '学生搜索成功');
  }

  @Get('students/statistics')
  @ApiOperation({ summary: '获取学生统计信息' })
  @ApiQuery({ name: 'classId', description: '班级ID', required: false })
  @ApiQuery({ name: 'gradeId', description: '年级ID', required: false })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '统计成功',
    type: StudentStatisticsDto,
  })
  async getStudentStatistics(
    @Query('classId') classId?: string,
    @Query('gradeId') gradeId?: string,
  ): Promise<ApiResponseWrapper<StudentStatisticsDto>> {
    const statistics =
      await this.studentApplicationService.getStudentStatistics(
        classId,
        gradeId,
      );

    return ApiResponseWrapper.success(statistics, '学生统计信息获取成功');
  }

  @Get('students/class/:classId')
  @ApiOperation({ summary: '获取班级学生' })
  @ApiParam({ name: 'classId', description: '班级ID' })
  @ApiQuery({
    name: 'includeInactive',
    description: '是否包含非激活学生',
    required: false,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '查询成功',
    type: [StudentResponseDto],
  })
  async getStudentsByClass(
    @Param('classId') classId: string,
    @Query('includeInactive') includeInactive?: boolean,
  ): Promise<ApiResponseWrapper<StudentResponseDto[]>> {
    const students = await this.studentApplicationService.getStudentsByClass(
      classId,
      includeInactive,
    );

    const responseData = students.map((student) =>
      this.mapToStudentResponseDto(student),
    );
    return ApiResponseWrapper.success(responseData, '班级学生查询成功');
  }

  @Get('students/:id')
  @ApiOperation({ summary: '根据ID查询学生' })
  @ApiParam({ name: 'id', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '查询成功',
    type: StudentResponseDto,
  })
  async getStudentById(
    @Param('id') id: string,
  ): Promise<ApiResponseWrapper<StudentResponseDto>> {
    const student = await this.studentApplicationService.getStudentById(id);
    const responseDto = this.mapToStudentResponseDto(student);

    return ApiResponseWrapper.success(responseDto, '学生信息查询成功');
  }

  @Put('students/:id')
  @ApiOperation({ summary: '更新学生信息' })
  @ApiParam({ name: 'id', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: StudentResponseDto,
  })
  async updateStudent(
    @Param('id') id: string,
    @Body() updateDto: UpdateStudentDto,
  ): Promise<ApiResponseWrapper<StudentResponseDto>> {
    const command = new UpdateStudentCommand(
      id,
      updateDto.name,
      updateDto.phone,
      updateDto.gender,
      updateDto.avatar,
      updateDto.age,
      updateDto.birthday,
      updateDto.cardNo,
      updateDto.cardType,
      updateDto.classId,
      updateDto.gradeId,
      updateDto.enrollmentTime,
      updateDto.graduationTime,
    );

    const student = await this.studentApplicationService.updateStudent(command);
    const responseDto = this.mapToStudentResponseDto(student);

    return ApiResponseWrapper.success(responseDto, '学生信息更新成功');
  }

  @Put('students/:id/transfer')
  @ApiOperation({ summary: '学生转班' })
  @ApiParam({ name: 'id', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '转班成功',
    type: StudentResponseDto,
  })
  async transferStudent(
    @Param('id') id: string,
    @Body() transferDto: TransferStudentDto,
  ): Promise<ApiResponseWrapper<StudentResponseDto>> {
    const student = await this.studentApplicationService.transferStudent(
      id,
      transferDto.classId,
      transferDto.gradeId,
    );

    const responseDto = this.mapToStudentResponseDto(student);
    return ApiResponseWrapper.success(responseDto, '学生转班成功');
  }

  @Delete('students/:id')
  @ApiOperation({ summary: '删除学生' })
  @ApiParam({ name: 'id', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  async deleteStudent(
    @Param('id') id: string,
  ): Promise<ApiResponseWrapper<void>> {
    const command = new DeleteStudentCommand(id);
    await this.studentApplicationService.deleteStudent(command);

    return ApiResponseWrapper.success(undefined, '学生删除成功');
  }

  @Delete('students/batch')
  @ApiOperation({ summary: '批量删除学生' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量删除成功',
  })
  async batchDeleteStudents(
    @Body() batchDeleteDto: BatchDeleteStudentsDto,
  ): Promise<ApiResponseWrapper<void>> {
    await this.studentApplicationService.batchDeleteStudents(
      batchDeleteDto.ids,
    );

    return ApiResponseWrapper.success(undefined, '学生批量删除成功');
  }

  /**
   * 将领域对象映射为响应DTO
   */
  private mapToStudentResponseDto(student: any): StudentResponseDto {
    const profile = student.getProfile();

    return {
      id: student.id,
      name: profile.name,
      phone: profile.phone,
      userType: student.getUserType().getDisplayName(),
      gender: profile.gender,
      avatar: profile.avatar,
      age: profile.age,
      birthday: profile.birthday,
      cardNo: profile.cardNo,
      cardType: profile.cardType,
      classId: student.getClassId?.(),
      gradeId: student.getGradeId?.(),
      enrollmentTime: student.enrollmentTime,
      graduationTime: student.graduationTime,
      isActive: student.isUserActive(),
      displayName: student.getDisplayName(),
      createdAt: student.createdAt,
      updatedAt: student.updatedAt,
    };
  }
}

# 开发环境配置

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=smart_campus_dev

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_CACHE_DB=0

# JWT配置
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 多租户配置
TENANT_ISOLATION=true
DEFAULT_TENANT_ID=system

# 开发者账号配置
CREATE_DEV_ACCOUNT=true
DEV_ACCOUNT_NAME=系统开发者
DEV_ACCOUNT_PHONE=***********
DEV_ACCOUNT_PASSWORD=Dev@123456
DEV_ACCOUNT_AVATAR=
DEV_ACCESS_LEVEL=admin
DEV_ALLOWED_ENVS=development,staging,production

# 开发者登录限制配置
DEV_LOGIN_ENABLED=true
DEV_LOGIN_ALLOWED_IPS=127.0.0.1,::1,***********/24
DEV_LOGIN_TIME_RESTRICTION=false
DEV_LOGIN_ALLOWED_HOURS=9-18
DEV_MAX_CONCURRENT_SESSIONS=3
DEV_SESSION_TIMEOUT=24h

# 日志配置
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
LOG_MAX_FILE_SIZE=100m
LOG_MAX_FILES=24
LOG_DATE_PATTERN=yyyyMMddHH

# 应用配置
NODE_ENV=development
PORT=3000

# 缓存配置
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# 文件上传配置
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# 邮件配置（开发环境可选）
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=
MAIL_PASS=
MAIL_FROM=<EMAIL>

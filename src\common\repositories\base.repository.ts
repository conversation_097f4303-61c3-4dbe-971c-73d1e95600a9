import { Repository, FindManyOptions, DeepPartial } from 'typeorm';
import { BaseEntity } from '@/entities/base.entity';
import { ApiException } from '@/common/exceptions/api.exception';

export abstract class BaseRepository<T extends BaseEntity> {
  constructor(protected readonly repository: Repository<T>) {}

  async create(entity: DeepPartial<T>): Promise<T> {
    const created = this.repository.create(entity);
    return await this.repository.save(created);
  }

  async findById(id: string): Promise<T> {
    const entity = await this.repository.findOne({ where: { id } as any });
    if (!entity) {
      throw ApiException.Error('记录不存在');
    }
    return entity;
  }

  async findMany(options: FindManyOptions<T>): Promise<[T[], number]> {
    return await this.repository.findAndCount(options);
  }

  async update(id: string, updateData: Partial<T>): Promise<T> {
    await this.repository.update(id, updateData as any);
    return await this.findById(id);
  }

  async softDelete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }

  async batchDelete(ids: string[]): Promise<void> {
    await this.repository.softDelete(ids);
  }
}

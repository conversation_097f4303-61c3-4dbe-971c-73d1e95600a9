import { Query } from '@/shared/application/cqrs/query';

/**
 * 获取学生查询
 */
export class GetStudentQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(public readonly id: string) {
    this.queryId = `get-student-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 获取学生列表查询
 */
export class GetStudentsQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly page: number = 1,
    public readonly pageSize: number = 10,
    public readonly name?: string,
    public readonly phone?: string,
    public readonly classId?: string,
    public readonly gradeId?: string,
    public readonly isActive?: boolean,
    public readonly sortBy: string = 'createdAt',
    public readonly sortOrder: 'ASC' | 'DESC' = 'DESC',
  ) {
    this.queryId = `get-students-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 搜索学生查询
 */
export class SearchStudentsQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly keyword: string,
    public readonly classId?: string,
    public readonly gradeId?: string,
    public readonly limit: number = 20,
  ) {
    this.queryId = `search-students-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 获取班级学生查询
 */
export class GetStudentsByClassQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly classId: string,
    public readonly includeInactive: boolean = false,
  ) {
    this.queryId = `get-students-by-class-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 获取年级学生查询
 */
export class GetStudentsByGradeQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly gradeId: string,
    public readonly includeInactive: boolean = false,
  ) {
    this.queryId = `get-students-by-grade-${Date.now()}`;
    this.timestamp = new Date();
  }
}

/**
 * 获取学生统计查询
 */
export class GetStudentStatisticsQuery implements Query {
  public readonly queryId?: string;
  public readonly timestamp?: Date;

  constructor(
    public readonly classId?: string,
    public readonly gradeId?: string,
  ) {
    this.queryId = `get-student-statistics-${Date.now()}`;
    this.timestamp = new Date();
  }
}

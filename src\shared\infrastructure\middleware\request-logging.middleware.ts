import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 请求日志中间件
 * 记录所有HTTP请求的详细信息
 */
@Injectable()
export class RequestLoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestLoggingMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // 将请求ID添加到请求对象中，供后续使用
    (req as any).requestId = requestId;

    // 记录请求开始
    this.logRequestStart(req, requestId);

    // 监听响应结束事件
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      this.logRequestEnd(req, res, duration, requestId);
    });

    next();
  }

  /**
   * 记录请求开始
   */
  private logRequestStart(req: Request, requestId: string): void {
    const { method, url, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const contentType = headers['content-type'] || '';
    const contentLength = headers['content-length'] || '0';
    const authorization = headers['authorization'] ? 'Bearer ***' : 'None';

    this.logger.log('Request started', {
      requestId,
      method,
      url,
      ip,
      userAgent: userAgent.substring(0, 100),
      contentType,
      contentLength,
      authorization,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 记录请求结束
   */
  private logRequestEnd(
    req: Request,
    res: Response,
    duration: number,
    requestId: string
  ): void {
    const { method, url, ip } = req;
    const { statusCode } = res;
    const contentLength = res.get('content-length') || '0';

    const logData = {
      requestId,
      method,
      url,
      ip,
      statusCode,
      duration: `${duration}ms`,
      contentLength,
      timestamp: new Date().toISOString(),
    };

    if (statusCode >= 500) {
      this.logger.error('Request completed with server error', logData);
    } else if (statusCode >= 400) {
      this.logger.warn('Request completed with client error', logData);
    } else if (duration > 1000) {
      this.logger.warn('Slow request completed', logData);
    } else {
      this.logger.log('Request completed', logData);
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 请求体日志中间件
 * 记录请求体内容（敏感信息会被脱敏）
 */
@Injectable()
export class RequestBodyLoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestBodyLoggingMiddleware.name);
  private readonly sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
  private readonly maxBodySize = 1024 * 10; // 10KB

  use(req: Request, res: Response, next: NextFunction) {
    // 只记录POST、PUT、PATCH请求的请求体
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      this.logRequestBody(req);
    }

    next();
  }

  /**
   * 记录请求体
   */
  private logRequestBody(req: Request): void {
    try {
      const body = req.body;
      
      if (!body || Object.keys(body).length === 0) {
        return;
      }

      const sanitizedBody = this.sanitizeBody(body);
      const bodyString = JSON.stringify(sanitizedBody);

      // 如果请求体太大，只记录前面部分
      const logBody = bodyString.length > this.maxBodySize
        ? bodyString.substring(0, this.maxBodySize) + '...[truncated]'
        : bodyString;

      this.logger.debug('Request body', {
        requestId: (req as any).requestId,
        method: req.method,
        url: req.url,
        body: logBody,
        bodySize: bodyString.length,
      });
    } catch (error) {
      this.logger.warn('Failed to log request body', {
        requestId: (req as any).requestId,
        error: error.message,
      });
    }
  }

  /**
   * 脱敏请求体中的敏感信息
   */
  private sanitizeBody(body: any): any {
    if (typeof body !== 'object' || body === null) {
      return body;
    }

    if (Array.isArray(body)) {
      return body.map(item => this.sanitizeBody(item));
    }

    const sanitized: any = {};
    
    Object.keys(body).forEach(key => {
      const lowerKey = key.toLowerCase();
      const isSensitive = this.sensitiveFields.some(field => lowerKey.includes(field));
      
      if (isSensitive) {
        sanitized[key] = '***';
      } else if (typeof body[key] === 'object') {
        sanitized[key] = this.sanitizeBody(body[key]);
      } else {
        sanitized[key] = body[key];
      }
    });

    return sanitized;
  }
}

/**
 * 请求限流中间件
 * 基于IP地址的简单限流实现
 */
@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RateLimitMiddleware.name);
  private readonly requests = new Map<string, { count: number; resetTime: number }>();
  private readonly maxRequests = 100; // 每分钟最大请求数
  private readonly windowMs = 60 * 1000; // 1分钟窗口

  use(req: Request, res: Response, next: NextFunction) {
    const ip = this.getClientIp(req);
    const now = Date.now();
    
    // 清理过期的记录
    this.cleanupExpiredRecords(now);
    
    // 检查当前IP的请求频率
    const requestInfo = this.requests.get(ip);
    
    if (!requestInfo) {
      // 首次请求
      this.requests.set(ip, { count: 1, resetTime: now + this.windowMs });
      next();
      return;
    }

    if (now > requestInfo.resetTime) {
      // 窗口已重置
      this.requests.set(ip, { count: 1, resetTime: now + this.windowMs });
      next();
      return;
    }

    if (requestInfo.count >= this.maxRequests) {
      // 超出限制
      this.logger.warn('Rate limit exceeded', {
        ip,
        count: requestInfo.count,
        maxRequests: this.maxRequests,
        resetTime: new Date(requestInfo.resetTime).toISOString(),
      });

      res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        errorCode: 'RATE_LIMIT_EXCEEDED',
        data: null,
        metadata: {
          retryAfter: Math.ceil((requestInfo.resetTime - now) / 1000),
        },
      });
      return;
    }

    // 增加请求计数
    requestInfo.count++;
    next();
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIp(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    ).split(',')[0].trim();
  }

  /**
   * 清理过期的记录
   */
  private cleanupExpiredRecords(now: number): void {
    for (const [ip, info] of this.requests.entries()) {
      if (now > info.resetTime) {
        this.requests.delete(ip);
      }
    }
  }
}

/**
 * CORS中间件
 * 处理跨域请求
 */
@Injectable()
export class CorsMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CorsMiddleware.name);
  private readonly allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://smart-campus.example.com',
  ];

  use(req: Request, res: Response, next: NextFunction) {
    const origin = req.headers.origin;
    
    // 检查来源是否被允许
    if (origin && this.isOriginAllowed(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
    }

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24小时

    // 处理预检请求
    if (req.method === 'OPTIONS') {
      res.status(200).end();
      return;
    }

    next();
  }

  /**
   * 检查来源是否被允许
   */
  private isOriginAllowed(origin: string): boolean {
    // 开发环境允许所有localhost
    if (process.env.NODE_ENV === 'development' && origin.includes('localhost')) {
      return true;
    }

    return this.allowedOrigins.includes(origin);
  }
}

/**
 * 安全头中间件
 * 添加安全相关的HTTP头
 */
@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityHeadersMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // 移除暴露服务器信息的头
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');

    // 添加安全头
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    
    // HTTPS相关头（仅在生产环境）
    if (process.env.NODE_ENV === 'production') {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }

    next();
  }
}

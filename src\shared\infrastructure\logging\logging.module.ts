import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PinoLoggerConfig } from './pino-logger.config';
import { PinoLoggerService } from './pino-logger.service';

/**
 * 日志模块
 * 全局模块，提供统一的日志服务
 */
@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    PinoLoggerConfig,
    {
      provide: 'LOGGER_SERVICE',
      useClass: PinoLoggerService,
    },
    PinoLoggerService,
  ],
  exports: [
    'LOGGER_SERVICE',
    PinoLoggerService,
    PinoLoggerConfig,
  ],
})
export class LoggingModule {}

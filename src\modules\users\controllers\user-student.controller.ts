import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { UserStudentService } from '../services/user-student.service';
import {
  CreateUserStudentDto,
  UpdateUserStudentDto,
  QueryUserStudentDto,
  UserStudentResponseDto,
} from '../dto/user-student.dto';

@ApiTags('学生管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('user-students')
export class UserStudentController {
  constructor(private readonly userStudentService: UserStudentService) {}

  @Post()
  @ApiOperation({ summary: '创建学生' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: UserStudentResponseDto,
  })
  async create(
    @Body() createDto: CreateUserStudentDto,
  ): Promise<UserStudentResponseDto> {
    return this.userStudentService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: '分页查询学生' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserStudentResponseDto' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' },
      },
    },
  })
  async findMany(@Query() query: QueryUserStudentDto) {
    return this.userStudentService.findMany(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询学生' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: UserStudentResponseDto,
  })
  async findById(@Param('id') id: string): Promise<UserStudentResponseDto> {
    return this.userStudentService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新学生信息' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UserStudentResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserStudentDto,
  ): Promise<UserStudentResponseDto> {
    return this.userStudentService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除学生' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.userStudentService.delete(id);
  }

  @Delete('batch/:ids')
  @ApiOperation({ summary: '批量删除学生' })
  @ApiResponse({
    status: 200,
    description: '批量删除成功',
  })
  async batchDelete(@Param('ids') ids: string): Promise<void> {
    const idArray = ids.split(',');
    return this.userStudentService.batchDelete(idArray);
  }
}

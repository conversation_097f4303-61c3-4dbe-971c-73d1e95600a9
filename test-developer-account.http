### 开发者账号测试流程

### 1. 检查系统信息
GET http://localhost:3000/api/dev-setup/system-info
Content-Type: application/json

###

### 2. 检查开发者账号是否存在
GET http://localhost:3000/api/dev-setup/check-developer
Content-Type: application/json

###

### 3. 手动创建开发者账号
POST http://localhost:3000/api/dev-setup/create-developer
Content-Type: application/json

###

### 4. 获取开发者登录信息
GET http://localhost:3000/api/dev-setup/login-info
Content-Type: application/json

###

### 5. 使用开发者账号登录
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "phone": "19900000000",
  "password": "Dev@123456"
}

###

### 6. 获取初始化状态
GET http://localhost:3000/api/dev-setup/initialization-status
Content-Type: application/json

###

### 7. 初始化所有数据
POST http://localhost:3000/api/dev-setup/initialize-data
Content-Type: application/json

###

### 8. 重置开发者密码（如果需要）
POST http://localhost:3000/api/dev-setup/reset-password
Content-Type: application/json

###

### 9. 使用JWT Token访问开发者接口（需要先登录获取token）
# @name login
POST http://localhost:3000/api/auth/login
Content-Type: application/json

{
  "phone": "19900000000",
  "password": "Dev@123456"
}

###

### 10. 使用获取的token访问开发者信息
GET http://localhost:3000/api/developers/19900000000
Authorization: Bearer {{login.response.body.data.access_token}}
Content-Type: application/json

###

### 11. 重新生成API密钥
PUT http://localhost:3000/api/developers/19900000000/api-key
Authorization: Bearer {{login.response.body.data.access_token}}
Content-Type: application/json

###

### 12. 列出所有开发者账号
GET http://localhost:3000/api/developers
Authorization: Bearer {{login.response.body.data.access_token}}
Content-Type: application/json

###

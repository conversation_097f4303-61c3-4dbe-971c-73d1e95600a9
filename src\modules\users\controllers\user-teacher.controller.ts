import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { UserTeacherService } from '../services/user-teacher.service';
import {
  CreateUserTeacherDto,
  UpdateUserTeacherDto,
  QueryUserTeacherDto,
  UserTeacherResponseDto,
} from '../dto/user-teacher.dto';

@ApiTags('教师管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('user-teachers')
export class UserTeacherController {
  constructor(private readonly userTeacherService: UserTeacherService) {}

  @Post()
  @ApiOperation({ summary: '创建教师' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: UserTeacherResponseDto,
  })
  async create(
    @Body() createDto: CreateUserTeacherDto,
  ): Promise<UserTeacherResponseDto> {
    return this.userTeacherService.create(createDto);
  }

  @Get()
  @ApiOperation({ summary: '分页查询教师' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/UserTeacherResponseDto' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        pageSize: { type: 'number' },
      },
    },
  })
  async findMany(@Query() query: QueryUserTeacherDto) {
    return this.userTeacherService.findMany(query);
  }

  @Get(':id')
  @ApiOperation({ summary: '根据ID查询教师' })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: UserTeacherResponseDto,
  })
  async findById(@Param('id') id: string): Promise<UserTeacherResponseDto> {
    return this.userTeacherService.findById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新教师信息' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UserTeacherResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserTeacherDto,
  ): Promise<UserTeacherResponseDto> {
    return this.userTeacherService.update(id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除教师' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  async delete(@Param('id') id: string): Promise<void> {
    return this.userTeacherService.delete(id);
  }

  @Delete('batch/:ids')
  @ApiOperation({ summary: '批量删除教师' })
  @ApiResponse({
    status: 200,
    description: '批量删除成功',
  })
  async batchDelete(@Param('ids') ids: string): Promise<void> {
    const idArray = ids.split(',');
    return this.userTeacherService.batchDelete(idArray);
  }
}

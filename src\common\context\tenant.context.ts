import { Injectable, Scope } from '@nestjs/common';

/**
 * 租户上下文服务
 * 用于在请求生命周期内管理当前租户信息
 */
@Injectable({ scope: Scope.REQUEST })
export class TenantContext {
  private tenantId?: string;
  private tenantName?: string;
  private tenantConfig?: Record<string, any>;

  /**
   * 设置当前租户ID
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * 获取当前租户ID
   */
  getTenantId(): string | undefined {
    return this.tenantId;
  }

  /**
   * 设置租户名称
   */
  setTenantName(name: string): void {
    this.tenantName = name;
  }

  /**
   * 获取租户名称
   */
  getTenantName(): string | undefined {
    return this.tenantName;
  }

  /**
   * 设置租户配置
   */
  setTenantConfig(config: Record<string, any>): void {
    this.tenantConfig = config;
  }

  /**
   * 获取租户配置
   */
  getTenantConfig(): Record<string, any> | undefined {
    return this.tenantConfig;
  }

  /**
   * 清除租户上下文
   */
  clear(): void {
    this.tenantId = undefined;
    this.tenantName = undefined;
    this.tenantConfig = undefined;
  }

  /**
   * 检查是否有租户上下文
   */
  hasTenant(): boolean {
    return !!this.tenantId;
  }

  /**
   * 获取完整的租户信息
   */
  getTenantInfo(): {
    id?: string;
    name?: string;
    config?: Record<string, any>;
  } {
    return {
      id: this.tenantId,
      name: this.tenantName,
      config: this.tenantConfig,
    };
  }
}

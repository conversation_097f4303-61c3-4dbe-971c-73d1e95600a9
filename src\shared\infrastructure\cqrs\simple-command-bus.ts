import { Injectable, Logger } from '@nestjs/common';
import {
  Command,
  CommandBus,
  CommandHandler,
  CommandResult,
  CommandValidator,
  ValidationResult,
} from '@/shared/application/cqrs/command';
import { DomainEventPublisher } from '@/shared/domain/events/domain-event';

/**
 * 简单命令总线实现
 */
@Injectable()
export class SimpleCommandBus implements CommandBus {
  private readonly logger = new Logger(SimpleCommandBus.name);
  private readonly handlers = new Map<string, CommandHandler<any, any>>();
  private readonly validators = new Map<string, CommandValidator<any>>();

  constructor() {}

  /**
   * 执行命令
   */
  async execute<TCommand extends Command, TResult = void>(
    command: TCommand,
  ): Promise<TResult> {
    const commandName = command.constructor.name;
    this.logger.debug(`Executing command: ${commandName}`, {
      commandId: command.commandId,
      timestamp: command.timestamp,
    });

    try {
      // 1. 验证命令
      await this.validateCommand(command);

      // 2. 获取处理器
      const handler = this.getHandler<TCommand, TResult>(commandName);

      // 3. 执行命令
      const startTime = Date.now();
      const result = await handler.handle(command);
      const executionTime = Date.now() - startTime;

      this.logger.debug(`Command executed successfully: ${commandName}`, {
        commandId: command.commandId,
        executionTime: `${executionTime}ms`,
      });

      return result;
    } catch (error) {
      this.logger.error(`Command execution failed: ${commandName}`, {
        commandId: command.commandId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * 注册命令处理器
   */
  register<TCommand extends Command, TResult = void>(
    commandType: new (...args: any[]) => TCommand,
    handler: CommandHandler<TCommand, TResult>,
  ): void {
    const commandName = commandType.name;

    if (this.handlers.has(commandName)) {
      throw new Error(
        `Command handler for ${commandName} is already registered`,
      );
    }

    this.handlers.set(commandName, handler);
    this.logger.debug(`Registered command handler: ${commandName}`);
  }

  /**
   * 注册命令验证器
   */
  registerValidator<TCommand extends Command>(
    commandType: new (...args: any[]) => TCommand,
    validator: CommandValidator<TCommand>,
  ): void {
    const commandName = commandType.name;
    this.validators.set(commandName, validator);
    this.logger.debug(`Registered command validator: ${commandName}`);
  }

  /**
   * 批量注册处理器
   */
  registerHandlers(
    handlers: Array<{
      commandType: new (...args: any[]) => Command;
      handler: CommandHandler<any, any>;
    }>,
  ): void {
    handlers.forEach(({ commandType, handler }) => {
      this.register(commandType, handler);
    });
  }

  /**
   * 获取所有已注册的命令类型
   */
  getRegisteredCommands(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * 检查命令是否已注册
   */
  isCommandRegistered(commandName: string): boolean {
    return this.handlers.has(commandName);
  }

  /**
   * 验证命令
   */
  private async validateCommand<TCommand extends Command>(
    command: TCommand,
  ): Promise<void> {
    const commandName = command.constructor.name;
    const validator = this.validators.get(commandName);

    if (validator) {
      const validationResult = await validator.validate(command);
      if (!validationResult.isValid) {
        const error = new Error(
          `Command validation failed: ${validationResult.errors.join(', ')}`,
        );
        error.name = 'CommandValidationError';
        throw error;
      }
    }
  }

  /**
   * 获取命令处理器
   */
  private getHandler<TCommand extends Command, TResult>(
    commandName: string,
  ): CommandHandler<TCommand, TResult> {
    const handler = this.handlers.get(commandName);

    if (!handler) {
      throw new Error(`No handler registered for command: ${commandName}`);
    }

    return handler;
  }
}

/**
 * 命令总线装饰器工厂
 */
export function createCommandBusDecorator() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      try {
        const result = await originalMethod.apply(this, args);
        const executionTime = Date.now() - startTime;

        Logger.debug(`Command method executed: ${propertyKey}`, {
          executionTime: `${executionTime}ms`,
          args: args.length,
        });

        return result;
      } catch (error) {
        const executionTime = Date.now() - startTime;

        Logger.error(`Command method failed: ${propertyKey}`, {
          executionTime: `${executionTime}ms`,
          error: error.message,
        });

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 命令执行中间件接口
 */
export interface CommandMiddleware {
  execute<TCommand extends Command, TResult>(
    command: TCommand,
    next: (command: TCommand) => Promise<TResult>,
  ): Promise<TResult>;
}

/**
 * 日志中间件
 */
@Injectable()
export class LoggingCommandMiddleware implements CommandMiddleware {
  private readonly logger = new Logger(LoggingCommandMiddleware.name);

  async execute<TCommand extends Command, TResult>(
    command: TCommand,
    next: (command: TCommand) => Promise<TResult>,
  ): Promise<TResult> {
    const commandName = command.constructor.name;
    const startTime = Date.now();

    this.logger.log(`Executing command: ${commandName}`, {
      commandId: command.commandId,
    });

    try {
      const result = await next(command);
      const executionTime = Date.now() - startTime;

      this.logger.log(`Command completed: ${commandName}`, {
        commandId: command.commandId,
        executionTime: `${executionTime}ms`,
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.logger.error(`Command failed: ${commandName}`, {
        commandId: command.commandId,
        executionTime: `${executionTime}ms`,
        error: error.message,
      });

      throw error;
    }
  }
}

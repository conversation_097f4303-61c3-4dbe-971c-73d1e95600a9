import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * API网关配置
 */
export interface ApiGatewayConfig {
  port: number;
  host: string;
  prefix: string;
  version: string;
  cors: {
    enabled: boolean;
    origins: string[];
    methods: string[];
    allowedHeaders: string[];
    credentials: boolean;
  };
  rateLimit: {
    enabled: boolean;
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  swagger: {
    enabled: boolean;
    title: string;
    description: string;
    version: string;
    path: string;
  };
  security: {
    helmet: boolean;
    csrf: boolean;
    contentSecurityPolicy: boolean;
  };
  logging: {
    level: string;
    requests: boolean;
    responses: boolean;
    errors: boolean;
  };
}

/**
 * API网关配置工厂
 */
@Injectable()
export class ApiGatewayConfigFactory {
  private static readonly logger = new Logger(ApiGatewayConfigFactory.name);

  /**
   * 创建API网关配置
   */
  static createConfig(configService: ConfigService): ApiGatewayConfig {
    const config: ApiGatewayConfig = {
      port: configService.get<number>('PORT', 3000),
      host: configService.get<string>('HOST', '0.0.0.0'),
      prefix: configService.get<string>('API_PREFIX', 'api'),
      version: configService.get<string>('API_VERSION', 'v1'),
      
      cors: {
        enabled: configService.get<boolean>('CORS_ENABLED', true),
        origins: configService.get<string>('CORS_ORIGINS', '*').split(','),
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'X-Requested-With',
          'X-Request-ID',
          'X-Tenant-ID',
        ],
        credentials: configService.get<boolean>('CORS_CREDENTIALS', true),
      },

      rateLimit: {
        enabled: configService.get<boolean>('RATE_LIMIT_ENABLED', true),
        windowMs: configService.get<number>('RATE_LIMIT_WINDOW_MS', 60000), // 1分钟
        maxRequests: configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
        skipSuccessfulRequests: configService.get<boolean>('RATE_LIMIT_SKIP_SUCCESS', false),
      },

      swagger: {
        enabled: configService.get<boolean>('SWAGGER_ENABLED', true),
        title: configService.get<string>('SWAGGER_TITLE', '智慧校园API'),
        description: configService.get<string>('SWAGGER_DESCRIPTION', '智慧校园系统API文档'),
        version: configService.get<string>('SWAGGER_VERSION', '1.0.0'),
        path: configService.get<string>('SWAGGER_PATH', 'docs'),
      },

      security: {
        helmet: configService.get<boolean>('SECURITY_HELMET', true),
        csrf: configService.get<boolean>('SECURITY_CSRF', false),
        contentSecurityPolicy: configService.get<boolean>('SECURITY_CSP', true),
      },

      logging: {
        level: configService.get<string>('LOG_LEVEL', 'info'),
        requests: configService.get<boolean>('LOG_REQUESTS', true),
        responses: configService.get<boolean>('LOG_RESPONSES', true),
        errors: configService.get<boolean>('LOG_ERRORS', true),
      },
    };

    this.logger.log('API Gateway configuration created', {
      port: config.port,
      host: config.host,
      prefix: config.prefix,
      version: config.version,
      corsEnabled: config.cors.enabled,
      rateLimitEnabled: config.rateLimit.enabled,
      swaggerEnabled: config.swagger.enabled,
    });

    return config;
  }

  /**
   * 验证配置
   */
  static validateConfig(config: ApiGatewayConfig): void {
    const errors: string[] = [];

    if (config.port < 1 || config.port > 65535) {
      errors.push('Port must be between 1 and 65535');
    }

    if (!config.host) {
      errors.push('Host is required');
    }

    if (!config.prefix) {
      errors.push('API prefix is required');
    }

    if (!config.version) {
      errors.push('API version is required');
    }

    if (config.rateLimit.enabled) {
      if (config.rateLimit.windowMs <= 0) {
        errors.push('Rate limit window must be positive');
      }
      if (config.rateLimit.maxRequests <= 0) {
        errors.push('Rate limit max requests must be positive');
      }
    }

    if (errors.length > 0) {
      throw new Error(`API Gateway configuration validation failed: ${errors.join(', ')}`);
    }

    this.logger.log('API Gateway configuration validated successfully');
  }
}

/**
 * 路由配置
 */
export interface RouteConfig {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  handler: string;
  middleware?: string[];
  rateLimit?: {
    windowMs: number;
    maxRequests: number;
  };
  cache?: {
    ttl: number;
    key?: string;
  };
  auth?: {
    required: boolean;
    roles?: string[];
    permissions?: string[];
  };
  validation?: {
    body?: any;
    query?: any;
    params?: any;
  };
}

/**
 * 路由管理器
 */
@Injectable()
export class RouteManager {
  private static readonly logger = new Logger(RouteManager.name);
  private readonly routes = new Map<string, RouteConfig>();

  /**
   * 注册路由
   */
  registerRoute(config: RouteConfig): void {
    const key = `${config.method}:${config.path}`;
    
    if (this.routes.has(key)) {
      throw new Error(`Route already registered: ${key}`);
    }

    this.routes.set(key, config);
    RouteManager.logger.debug(`Route registered: ${key}`);
  }

  /**
   * 批量注册路由
   */
  registerRoutes(configs: RouteConfig[]): void {
    configs.forEach(config => this.registerRoute(config));
    RouteManager.logger.log(`Registered ${configs.length} routes`);
  }

  /**
   * 获取路由配置
   */
  getRoute(method: string, path: string): RouteConfig | undefined {
    const key = `${method}:${path}`;
    return this.routes.get(key);
  }

  /**
   * 获取所有路由
   */
  getAllRoutes(): RouteConfig[] {
    return Array.from(this.routes.values());
  }

  /**
   * 检查路由是否存在
   */
  hasRoute(method: string, path: string): boolean {
    const key = `${method}:${path}`;
    return this.routes.has(key);
  }

  /**
   * 移除路由
   */
  removeRoute(method: string, path: string): boolean {
    const key = `${method}:${path}`;
    const removed = this.routes.delete(key);
    
    if (removed) {
      RouteManager.logger.debug(`Route removed: ${key}`);
    }
    
    return removed;
  }

  /**
   * 清空所有路由
   */
  clearRoutes(): void {
    const count = this.routes.size;
    this.routes.clear();
    RouteManager.logger.log(`Cleared ${count} routes`);
  }

  /**
   * 获取路由统计信息
   */
  getRouteStats(): {
    total: number;
    byMethod: Record<string, number>;
    withAuth: number;
    withCache: number;
    withRateLimit: number;
  } {
    const stats = {
      total: this.routes.size,
      byMethod: {} as Record<string, number>,
      withAuth: 0,
      withCache: 0,
      withRateLimit: 0,
    };

    this.routes.forEach(route => {
      // 按方法统计
      stats.byMethod[route.method] = (stats.byMethod[route.method] || 0) + 1;

      // 功能统计
      if (route.auth?.required) stats.withAuth++;
      if (route.cache) stats.withCache++;
      if (route.rateLimit) stats.withRateLimit++;
    });

    return stats;
  }
}

/**
 * API版本管理器
 */
@Injectable()
export class ApiVersionManager {
  private static readonly logger = new Logger(ApiVersionManager.name);
  private readonly versions = new Map<string, {
    routes: RouteConfig[];
    deprecated: boolean;
    deprecationDate?: Date;
    sunsetDate?: Date;
  }>();

  /**
   * 注册API版本
   */
  registerVersion(
    version: string,
    routes: RouteConfig[],
    options: {
      deprecated?: boolean;
      deprecationDate?: Date;
      sunsetDate?: Date;
    } = {}
  ): void {
    if (this.versions.has(version)) {
      throw new Error(`API version already registered: ${version}`);
    }

    this.versions.set(version, {
      routes,
      deprecated: options.deprecated || false,
      deprecationDate: options.deprecationDate,
      sunsetDate: options.sunsetDate,
    });

    ApiVersionManager.logger.log(`API version registered: ${version}`, {
      routeCount: routes.length,
      deprecated: options.deprecated,
    });
  }

  /**
   * 获取版本信息
   */
  getVersion(version: string) {
    return this.versions.get(version);
  }

  /**
   * 获取所有版本
   */
  getAllVersions(): string[] {
    return Array.from(this.versions.keys());
  }

  /**
   * 检查版本是否已弃用
   */
  isVersionDeprecated(version: string): boolean {
    const versionInfo = this.versions.get(version);
    return versionInfo?.deprecated || false;
  }

  /**
   * 检查版本是否已过期
   */
  isVersionSunset(version: string): boolean {
    const versionInfo = this.versions.get(version);
    if (!versionInfo?.sunsetDate) return false;
    
    return new Date() > versionInfo.sunsetDate;
  }

  /**
   * 标记版本为已弃用
   */
  deprecateVersion(version: string, deprecationDate?: Date, sunsetDate?: Date): void {
    const versionInfo = this.versions.get(version);
    if (!versionInfo) {
      throw new Error(`API version not found: ${version}`);
    }

    versionInfo.deprecated = true;
    versionInfo.deprecationDate = deprecationDate || new Date();
    versionInfo.sunsetDate = sunsetDate;

    ApiVersionManager.logger.warn(`API version deprecated: ${version}`, {
      deprecationDate: versionInfo.deprecationDate,
      sunsetDate: versionInfo.sunsetDate,
    });
  }

  /**
   * 获取版本统计信息
   */
  getVersionStats(): {
    total: number;
    active: number;
    deprecated: number;
    sunset: number;
  } {
    const stats = {
      total: this.versions.size,
      active: 0,
      deprecated: 0,
      sunset: 0,
    };

    this.versions.forEach((info, version) => {
      if (this.isVersionSunset(version)) {
        stats.sunset++;
      } else if (info.deprecated) {
        stats.deprecated++;
      } else {
        stats.active++;
      }
    });

    return stats;
  }
}

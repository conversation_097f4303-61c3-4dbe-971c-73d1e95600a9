/**
 * 领域异常基类
 */
export abstract class DomainException extends Error {
  public readonly code: string;
  public readonly statusCode: number;

  constructor(message: string, code: string, statusCode: number = 400) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;

    // 确保堆栈跟踪正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * 业务规则异常
 */
export class BusinessRuleException extends DomainException {
  constructor(message: string, code: string = 'BUSINESS_RULE_VIOLATION') {
    super(message, code, 400);
  }
}

/**
 * 实体未找到异常
 */
export class EntityNotFoundException extends DomainException {
  constructor(entityName: string, id: string) {
    super(`${entityName} with id ${id} not found`, 'ENTITY_NOT_FOUND', 404);
  }
}

/**
 * 实体已存在异常
 */
export class EntityAlreadyExistsException extends DomainException {
  constructor(entityName: string, field: string, value: string) {
    super(
      `${entityName} with ${field} '${value}' already exists`,
      'ENTITY_ALREADY_EXISTS',
      409,
    );
  }
}

/**
 * 验证异常
 */
export class ValidationException extends DomainException {
  public readonly errors: Record<string, string[]>;

  constructor(errors: Record<string, string[]>) {
    const message = Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('; ');

    super(`Validation failed: ${message}`, 'VALIDATION_ERROR', 422);
    this.errors = errors;
  }
}

/**
 * 权限异常
 */
export class PermissionDeniedException extends DomainException {
  constructor(action: string, resource: string) {
    super(
      `Permission denied for action '${action}' on resource '${resource}'`,
      'PERMISSION_DENIED',
      403,
    );
  }
}

/**
 * 并发冲突异常
 */
export class ConcurrencyException extends DomainException {
  constructor(entityName: string, id: string) {
    super(
      `Concurrency conflict detected for ${entityName} with id ${id}`,
      'CONCURRENCY_CONFLICT',
      409,
    );
  }
}

/**
 * 领域不变量违反异常
 */
export class DomainInvariantException extends DomainException {
  constructor(invariant: string) {
    super(
      `Domain invariant violated: ${invariant}`,
      'DOMAIN_INVARIANT_VIOLATION',
      400,
    );
  }
}

/**
 * 乐观锁异常
 */
export class OptimisticLockException extends DomainException {
  constructor(
    entityName: string,
    id: string,
    expectedVersion?: number,
    actualVersion?: number,
  ) {
    const versionInfo =
      expectedVersion && actualVersion
        ? ` Expected version: ${expectedVersion}, actual: ${actualVersion}`
        : '';
    super(
      `${entityName} with id ${id} has been modified by another user.${versionInfo}`,
      'OPTIMISTIC_LOCK_ERROR',
      409,
    );
  }
}

/**
 * 租户访问异常
 */
export class TenantAccessException extends DomainException {
  constructor(message: string, tenantId?: string) {
    super(
      tenantId ? `${message} (Tenant: ${tenantId})` : message,
      'TENANT_ACCESS_DENIED',
      403,
    );
  }
}

/**
 * 聚合根不变量违反异常
 */
export class AggregateInvariantViolationException extends DomainException {
  constructor(aggregateName: string, invariantName: string, message: string) {
    super(
      `Aggregate invariant violation in ${aggregateName}.${invariantName}: ${message}`,
      'AGGREGATE_INVARIANT_VIOLATION',
      422,
    );
  }
}

/**
 * 领域服务异常
 */
export class DomainServiceException extends DomainException {
  constructor(serviceName: string, message: string) {
    super(
      `Domain service error in ${serviceName}: ${message}`,
      'DOMAIN_SERVICE_ERROR',
      500,
    );
  }
}

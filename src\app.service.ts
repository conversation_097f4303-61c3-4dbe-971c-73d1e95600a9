import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  getHello(): string {
    this.logger.log('获取问候语');
    this.logger.debug('调试信息: 处理问候语请求');
    this.logger.warn('警告: 这是一个示例警告');
    this.logger.error('错误: 这是一个示例错误');

    return 'Hello World!';
  }

  getHealth(): object {
    this.logger.log('健康检查请求');
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}

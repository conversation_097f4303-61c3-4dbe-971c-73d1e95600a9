import { LoggerOptions } from 'pino';

/**
 * 日志配置
 * 基于Pino的高性能日志配置
 */
export const loggerConfig: LoggerOptions = {
  // 日志级别
  level:
    process.env.LOG_LEVEL ||
    (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),

  // 开发环境使用美化输出
  transport:
    process.env.NODE_ENV !== 'production'
      ? {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'yyyy-mm-dd HH:MM:ss',
            ignore: 'pid,hostname',
            singleLine: true,
          },
        }
      : undefined,

  // 生产环境配置
  ...(process.env.NODE_ENV === 'production' && {
    formatters: {
      level: (label) => {
        return { level: label };
      },
    },
    timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
  }),

  // 基础字段
  base: {
    pid: process.pid,
    hostname: process.env.HOSTNAME || 'unknown',
    service: 'smart-campus-server',
    version: process.env.npm_package_version || '1.0.0',
  },

  // 序列化器
  serializers: {
    req: (req) => ({
      method: req.method,
      url: req.url,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
        authorization: req.headers.authorization ? '[REDACTED]' : undefined,
      },
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort,
    }),
    res: (res) => ({
      statusCode: res.statusCode,
      headers: res.headers,
    }),
    err: (err) => ({
      type: err.constructor.name,
      message: err.message,
      stack: err.stack,
      code: err.code,
    }),
  },

  // 自定义方法
  mixin() {
    return {
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
    };
  },
};

/**
 * 过滤敏感数据
 */
function filterSensitiveData(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const sensitiveKeys = [
    'password',
    'token',
    'authorization',
    'secret',
    'key',
    'credential',
    'auth',
  ];

  const filtered = { ...obj };

  for (const key in filtered) {
    if (
      sensitiveKeys.some((sensitive) => key.toLowerCase().includes(sensitive))
    ) {
      filtered[key] = '[REDACTED]';
    } else if (typeof filtered[key] === 'object' && filtered[key] !== null) {
      filtered[key] = filterSensitiveData(filtered[key]);
    }
  }

  return filtered;
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  FATAL = 'fatal',
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace',
}

/**
 * 日志上下文接口
 */
export interface LogContext {
  userId?: string;
  tenantId?: string;
  requestId?: string;
  operation?: string;
  module?: string;
  [key: string]: any;
}

/**
 * 结构化日志工具类
 */
export class StructuredLogger {
  /**
   * 创建带上下文的日志对象
   */
  static withContext(context: LogContext) {
    return {
      info: (message: string, data?: any) =>
        this.log('info', message, data, context),
      error: (message: string, error?: Error, data?: any) =>
        this.log('error', message, { error, ...data }, context),
      warn: (message: string, data?: any) =>
        this.log('warn', message, data, context),
      debug: (message: string, data?: any) =>
        this.log('debug', message, data, context),
    };
  }

  /**
   * 记录日志
   */
  private static log(
    level: string,
    message: string,
    data?: any,
    context?: LogContext,
  ) {
    const logData = {
      message,
      ...context,
      ...data,
      timestamp: new Date().toISOString(),
    };

    // 使用适当的console方法
    switch (level) {
      case 'error':
        console.error(logData);
        break;
      case 'warn':
        console.warn(logData);
        break;
      case 'debug':
        console.debug(logData);
        break;
      default:
        console.log(logData);
    }
  }

  /**
   * 记录性能日志
   */
  static performance(
    operation: string,
    duration: number,
    context?: LogContext,
  ) {
    this.log(
      'info',
      `Performance: ${operation}`,
      {
        operation,
        duration: `${duration}ms`,
        performance: true,
      },
      context,
    );
  }

  /**
   * 记录审计日志
   */
  static audit(
    action: string,
    userId: string,
    resource: string,
    details?: any,
  ) {
    this.log('info', `Audit: ${action}`, {
      action,
      userId,
      resource,
      details,
      audit: true,
    });
  }

  /**
   * 记录安全日志
   */
  static security(event: string, userId?: string, ip?: string, details?: any) {
    this.log('warn', `Security: ${event}`, {
      event,
      userId,
      ip,
      details,
      security: true,
    });
  }
}

/**
 * 请求日志中间件配置
 */
export const requestLoggerConfig = {
  // 排除的路径
  exclude: ['/health', '/metrics', '/favicon.ico'],

  // 自定义日志格式
  customLogLevel: (req: any, res: any) => {
    if (res.statusCode >= 400) return 'error';
    if (res.statusCode >= 300) return 'warn';
    return 'info';
  },

  // 自定义成功消息
  customSuccessMessage: (req: any, res: any) => {
    return `${req.method} ${req.url} ${res.statusCode}`;
  },

  // 自定义错误消息
  customErrorMessage: (req: any, res: any, err: any) => {
    return `${req.method} ${req.url} ${res.statusCode} - ${err.message}`;
  },
};

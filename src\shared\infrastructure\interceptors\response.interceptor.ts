import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ResponseUtil } from '@/shared/application/utils/response.util';

/**
 * 统一响应拦截器
 * 将所有API响应包装为统一格式
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const startTime = Date.now();

    return next.handle().pipe(
      map((data) => {
        // 如果数据已经是ApiResponse格式，直接返回
        if (
          data &&
          typeof data === 'object' &&
          'success' in data &&
          'message' in data
        ) {
          return data;
        }

        // 包装为统一响应格式
        return ResponseUtil.success(data);
      }),
      tap(() => {
        const duration = Date.now() - startTime;
        this.logRequest(request, response, duration);
      }),
    );
  }

  /**
   * 记录请求日志
   */
  private logRequest(
    request: Request,
    response: Response,
    duration: number,
  ): void {
    const { method, url, ip, headers } = request;
    const { statusCode } = response;
    const userAgent = headers['user-agent'] || '';
    // 兼容 Fastify 和 Express
    const contentLength =
      this.getResponseHeader(response, 'content-length') || '0';

    const logData = {
      method,
      url,
      statusCode,
      duration: `${duration}ms`,
      ip,
      userAgent: userAgent.substring(0, 100),
      contentLength,
    };

    if (duration > 1000) {
      this.logger.warn('Slow API request detected', logData);
    } else if (statusCode >= 400) {
      this.logger.error('API request failed', logData);
    } else {
      this.logger.log('API request completed', logData);
    }
  }

  /**
   * 获取响应头（兼容 Fastify 和 Express）
   */
  private getResponseHeader(
    response: any,
    headerName: string,
  ): string | undefined {
    // Fastify 使用 getHeader
    if (typeof response.getHeader === 'function') {
      return response.getHeader(headerName);
    }
    // Express 使用 get
    if (typeof response.get === 'function') {
      return response.get(headerName);
    }
    // 直接访问 headers 属性
    if (response.headers && response.headers[headerName]) {
      return response.headers[headerName];
    }
    return undefined;
  }
}

/**
 * 分页响应拦截器
 * 专门处理分页查询的响应格式
 */
@Injectable()
export class PaginationResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PaginationResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // 检查是否是分页数据
        if (this.isPaginationData(data)) {
          return ResponseUtil.success(data, '查询成功');
        }

        // 如果已经是标准响应格式，直接返回
        if (data && typeof data === 'object' && 'errCode' in data) {
          return data;
        }

        // 普通数据包装
        return ResponseUtil.success(data);
      }),
    );
  }

  /**
   * 检查是否是分页数据
   */
  private isPaginationData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'data' in data &&
      'total' in data &&
      'page' in data &&
      'pageSize' in data &&
      Array.isArray(data.data)
    );
  }
}

/**
 * 缓存响应拦截器
 * 为可缓存的响应添加缓存头
 */
@Injectable()
export class CacheResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    return next.handle().pipe(
      tap((data) => {
        // 只对GET请求设置缓存头
        if (request.method === 'GET') {
          this.setCacheHeaders(request, response, data);
        }
      }),
    );
  }

  /**
   * 设置缓存头
   */
  private setCacheHeaders(
    request: Request,
    response: Response,
    data: any,
  ): void {
    const url = request.url;
    let cacheControl = 'no-cache';
    let maxAge = 0;

    // 根据URL路径设置不同的缓存策略
    if (url.includes('/statistics')) {
      // 统计数据缓存5分钟
      cacheControl = 'public, max-age=300';
      maxAge = 300;
    } else if (url.includes('/users/') && !url.includes('/search')) {
      // 用户详情缓存2分钟
      cacheControl = 'private, max-age=120';
      maxAge = 120;
    } else if (url.includes('/config') || url.includes('/menu')) {
      // 配置和菜单数据缓存10分钟
      cacheControl = 'public, max-age=600';
      maxAge = 600;
    }

    response.set({
      'Cache-Control': cacheControl,
      ETag: this.generateETag(data),
      'Last-Modified': new Date().toUTCString(),
    });

    if (maxAge > 0) {
      const expires = new Date(Date.now() + maxAge * 1000);
      response.set('Expires', expires.toUTCString());
    }

    this.logger.debug(`Cache headers set for ${request.url}`, {
      cacheControl,
      maxAge,
    });
  }

  /**
   * 生成ETag
   */
  private generateETag(data: any): string {
    const content = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `"${Math.abs(hash).toString(36)}"`;
  }
}

/**
 * 性能监控拦截器
 * 监控API性能并记录慢请求
 */
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);
  private readonly slowRequestThreshold = 1000; // 1秒

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const endMemory = process.memoryUsage();
        const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

        const performanceData = {
          method: request.method,
          url: request.url,
          duration: `${duration}ms`,
          memoryDelta: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`,
          heapUsed: `${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`,
          heapTotal: `${(endMemory.heapTotal / 1024 / 1024).toFixed(2)}MB`,
        };

        if (duration > this.slowRequestThreshold) {
          this.logger.warn('Slow request detected', performanceData);
        } else {
          this.logger.debug('Request performance', performanceData);
        }

        // 记录到性能监控系统（如果有的话）
        this.recordPerformanceMetrics(request, duration, memoryDelta);
      }),
    );
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetrics(
    request: Request,
    duration: number,
    memoryDelta: number,
  ): void {
    // 这里可以集成到监控系统，如Prometheus、DataDog等
    // 暂时只记录到日志
    if (duration > this.slowRequestThreshold) {
      this.logger.warn(
        `Performance alert: ${request.method} ${request.url} took ${duration}ms`,
      );
    }
  }
}

/**
 * 安全响应拦截器
 * 添加安全相关的响应头
 */
@Injectable()
export class SecurityResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SecurityResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse<Response>();

    return next.handle().pipe(
      tap(() => {
        this.setSecurityHeaders(response);
      }),
    );
  }

  /**
   * 设置安全头
   */
  private setSecurityHeaders(response: Response): void {
    const headers = {
      // 防止XSS攻击
      'X-XSS-Protection': '1; mode=block',
      // 防止MIME类型嗅探
      'X-Content-Type-Options': 'nosniff',
      // 防止点击劫持
      'X-Frame-Options': 'DENY',
      // 强制HTTPS
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      // 内容安全策略
      'Content-Security-Policy': "default-src 'self'",
      // 引用策略
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      // 权限策略
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
    };

    // 兼容 Fastify 和 Express
    this.setResponseHeaders(response, headers);
  }

  /**
   * 设置响应头（兼容 Fastify 和 Express）
   */
  private setResponseHeaders(
    response: any,
    headers: Record<string, string>,
  ): void {
    // Fastify 使用 header 方法逐个设置
    if (typeof response.header === 'function') {
      Object.entries(headers).forEach(([key, value]) => {
        response.header(key, value);
      });
      return;
    }
    // Express 使用 set 方法
    if (typeof response.set === 'function') {
      response.set(headers);
      return;
    }
    // 直接设置 headers 属性
    if (response.headers) {
      Object.assign(response.headers, headers);
    }
  }

  /**
   * 获取响应头（兼容 Fastify 和 Express）
   */
  private getResponseHeader(
    response: any,
    headerName: string,
  ): string | undefined {
    // Fastify 使用 getHeader
    if (typeof response.getHeader === 'function') {
      return response.getHeader(headerName);
    }
    // Express 使用 get
    if (typeof response.get === 'function') {
      return response.get(headerName);
    }
    // 直接访问 headers 属性
    if (response.headers && response.headers[headerName]) {
      return response.headers[headerName];
    }
    return undefined;
  }
}

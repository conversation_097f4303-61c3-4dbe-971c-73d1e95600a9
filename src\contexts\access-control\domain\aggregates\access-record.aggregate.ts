import { Entity, Column, Index } from 'typeorm';
import { AggregateRoot } from '@/shared/domain/base/aggregate-root';
import { AccessLocation } from '../value-objects/access-location';
import { AccessRecordCreatedEvent } from '../events/access-record-created.event';
import { AccessRecordUpdatedEvent } from '../events/access-record-updated.event';
import { BusinessRuleException } from '@/shared/application/exceptions/domain-exception';

/**
 * 门禁类型枚举
 */
export enum AccessType {
  ENTER = 'enter',
  LEAVE = 'leave',
}

/**
 * 门禁记录状态枚举
 */
export enum AccessRecordStatus {
  NORMAL = 'normal',
  ABNORMAL = 'abnormal',
  SUSPICIOUS = 'suspicious',
}

/**
 * 门禁记录聚合根
 */
@Entity('access_records')
@Index(['userId', 'accessType', 'accessTime'])
@Index(['accessTime'])
@Index(['deviceId'])
@Index(['status'])
export class AccessRecord extends AggregateRoot {
  @Column({
    name: 'user_id',
    type: 'varchar',
    length: 36,
    comment: '用户ID',
  })
  private userId: string;

  @Column({
    name: 'access_type',
    type: 'enum',
    enum: AccessType,
    comment: '门禁类型：进入或离开',
  })
  private accessType: AccessType;

  @Column({
    name: 'access_time',
    type: 'datetime',
    comment: '门禁时间',
  })
  private accessTime: Date;

  @Column({
    name: 'location_name',
    type: 'varchar',
    length: 100,
    comment: '位置名称',
  })
  private locationName: string;

  @Column({
    name: 'location_description',
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '位置描述',
  })
  private locationDescription?: string;

  @Column({
    name: 'location_building',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '建筑名称',
  })
  private locationBuilding?: string;

  @Column({
    name: 'location_floor',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '楼层',
  })
  private locationFloor?: string;

  @Column({
    name: 'device_id',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '设备ID',
  })
  private deviceId?: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: AccessRecordStatus,
    default: AccessRecordStatus.NORMAL,
    comment: '记录状态',
  })
  private status: AccessRecordStatus;

  @Column({
    name: 'verification_method',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '验证方式：face_recognition, card, fingerprint等',
  })
  private verificationMethod?: string;

  @Column({
    name: 'confidence_score',
    type: 'decimal',
    precision: 5,
    scale: 4,
    nullable: true,
    comment: '识别置信度（0-1）',
  })
  private confidenceScore?: number;

  @Column({
    name: 'temperature',
    type: 'decimal',
    precision: 4,
    scale: 2,
    nullable: true,
    comment: '体温（摄氏度）',
  })
  private temperature?: number;

  @Column({
    name: 'remark',
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  private remark?: string;

  constructor(
    userId: string,
    accessType: AccessType,
    accessTime: Date,
    location: AccessLocation,
    deviceId?: string
  ) {
    super();
    this.userId = userId;
    this.accessType = accessType;
    this.accessTime = accessTime;
    this.setLocation(location);
    this.deviceId = deviceId;
    this.status = AccessRecordStatus.NORMAL;

    // 发布门禁记录创建事件
    this.addDomainEvent(new AccessRecordCreatedEvent(
      this.id,
      userId,
      accessType,
      accessTime,
      location
    ));
  }

  /**
   * 获取用户ID
   */
  getUserId(): string {
    return this.userId;
  }

  /**
   * 获取门禁类型
   */
  getAccessType(): AccessType {
    return this.accessType;
  }

  /**
   * 获取门禁时间
   */
  getAccessTime(): Date {
    return this.accessTime;
  }

  /**
   * 获取位置信息
   */
  getLocation(): AccessLocation {
    return new AccessLocation({
      name: this.locationName,
      description: this.locationDescription,
      building: this.locationBuilding,
      floor: this.locationFloor,
    });
  }

  /**
   * 获取设备ID
   */
  getDeviceId(): string | undefined {
    return this.deviceId;
  }

  /**
   * 获取记录状态
   */
  getStatus(): AccessRecordStatus {
    return this.status;
  }

  /**
   * 获取验证方式
   */
  getVerificationMethod(): string | undefined {
    return this.verificationMethod;
  }

  /**
   * 获取置信度
   */
  getConfidenceScore(): number | undefined {
    return this.confidenceScore;
  }

  /**
   * 获取体温
   */
  getTemperature(): number | undefined {
    return this.temperature;
  }

  /**
   * 获取备注
   */
  getRemark(): string | undefined {
    return this.remark;
  }

  /**
   * 设置位置信息
   */
  private setLocation(location: AccessLocation): void {
    this.locationName = location.name;
    this.locationDescription = location.description;
    this.locationBuilding = location.building;
    this.locationFloor = location.floor;
  }

  /**
   * 更新验证信息
   */
  updateVerification(
    method: string,
    confidenceScore?: number,
    temperature?: number
  ): void {
    this.verificationMethod = method;
    this.confidenceScore = confidenceScore;
    this.temperature = temperature;

    // 根据置信度和体温判断状态
    this.updateStatusBasedOnVerification();
    this.markAsModified();

    // 发布更新事件
    this.addDomainEvent(new AccessRecordUpdatedEvent(this.id, {
      verificationMethod: method,
      confidenceScore,
      temperature,
      status: this.status
    }));
  }

  /**
   * 标记为异常
   */
  markAsAbnormal(reason: string): void {
    if (this.status === AccessRecordStatus.ABNORMAL) {
      throw new BusinessRuleException('记录已经是异常状态');
    }

    this.status = AccessRecordStatus.ABNORMAL;
    this.remark = reason;
    this.markAsModified();

    // 发布更新事件
    this.addDomainEvent(new AccessRecordUpdatedEvent(this.id, {
      status: this.status,
      remark: reason
    }));
  }

  /**
   * 标记为可疑
   */
  markAsSuspicious(reason: string): void {
    this.status = AccessRecordStatus.SUSPICIOUS;
    this.remark = reason;
    this.markAsModified();

    // 发布更新事件
    this.addDomainEvent(new AccessRecordUpdatedEvent(this.id, {
      status: this.status,
      remark: reason
    }));
  }

  /**
   * 恢复为正常状态
   */
  markAsNormal(): void {
    this.status = AccessRecordStatus.NORMAL;
    this.remark = undefined;
    this.markAsModified();

    // 发布更新事件
    this.addDomainEvent(new AccessRecordUpdatedEvent(this.id, {
      status: this.status,
      remark: undefined
    }));
  }

  /**
   * 添加备注
   */
  addRemark(remark: string): void {
    this.remark = remark;
    this.markAsModified();
  }

  /**
   * 检查是否是进入记录
   */
  isEnterRecord(): boolean {
    return this.accessType === AccessType.ENTER;
  }

  /**
   * 检查是否是离开记录
   */
  isLeaveRecord(): boolean {
    return this.accessType === AccessType.LEAVE;
  }

  /**
   * 检查是否是异常记录
   */
  isAbnormal(): boolean {
    return this.status === AccessRecordStatus.ABNORMAL;
  }

  /**
   * 检查是否是可疑记录
   */
  isSuspicious(): boolean {
    return this.status === AccessRecordStatus.SUSPICIOUS;
  }

  /**
   * 检查体温是否正常
   */
  isTemperatureNormal(): boolean {
    if (!this.temperature) return true;
    return this.temperature >= 36.0 && this.temperature <= 37.5;
  }

  /**
   * 根据验证信息更新状态
   */
  private updateStatusBasedOnVerification(): void {
    // 体温异常
    if (this.temperature && !this.isTemperatureNormal()) {
      this.status = AccessRecordStatus.ABNORMAL;
      return;
    }

    // 置信度过低
    if (this.confidenceScore && this.confidenceScore < 0.7) {
      this.status = AccessRecordStatus.SUSPICIOUS;
      return;
    }

    // 其他情况保持正常
    if (this.status !== AccessRecordStatus.ABNORMAL) {
      this.status = AccessRecordStatus.NORMAL;
    }
  }
}

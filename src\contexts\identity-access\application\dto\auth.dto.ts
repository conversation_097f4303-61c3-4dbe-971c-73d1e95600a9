import { IsString, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 登录请求DTO
 */
export class LoginRequestDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    minLength: 11,
    maxLength: 11,
  })
  @IsString()
  phone: string;

  @ApiProperty({
    description: '密码',
    example: '123456',
    minLength: 6,
    maxLength: 20,
  })
  @IsString()
  password: string;

  @ApiPropertyOptional({
    description: '记住我',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;
}

/**
 * 刷新令牌请求DTO
 */
export class RefreshTokenRequestDto {
  @ApiProperty({
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  refresh_token: string;
}

/**
 * 登录响应DTO
 */
export class LoginResponseDto {
  @ApiProperty({ description: '访问令牌' })
  access_token: string;

  @ApiProperty({ description: '刷新令牌' })
  refresh_token: string;

  @ApiProperty({ description: '令牌过期时间（秒）' })
  expires_in: number;

  @ApiProperty({ description: '令牌类型', example: 'Bearer' })
  token_type: string;

  @ApiProperty({ description: '用户信息' })
  user: {
    id: string;
    name: string;
    phone: string;
    userType: string;
    avatar?: string;
    roles: string[];
    permissions: string[];
  };
}

/**
 * 令牌验证响应DTO
 */
export class TokenValidationResponseDto {
  @ApiProperty({ description: '令牌是否有效' })
  valid: boolean;

  @ApiProperty({ description: '用户信息' })
  user?: {
    id: string;
    name: string;
    phone: string;
    userType: string;
    roles: string[];
    permissions: string[];
  };

  @ApiProperty({ description: '令牌剩余有效时间（秒）' })
  expires_in?: number;
}

/**
 * 权限检查请求DTO
 */
export class CheckPermissionRequestDto {
  @ApiProperty({
    description: '权限名称',
    example: 'user:read',
  })
  @IsString()
  permission: string;
}

/**
 * 权限检查响应DTO
 */
export class CheckPermissionResponseDto {
  @ApiProperty({ description: '是否具有权限' })
  hasPermission: boolean;
}

/**
 * 角色检查请求DTO
 */
export class CheckRoleRequestDto {
  @ApiProperty({
    description: '角色名称',
    example: 'admin',
  })
  @IsString()
  role: string;
}

/**
 * 角色检查响应DTO
 */
export class CheckRoleResponseDto {
  @ApiProperty({ description: '是否具有角色' })
  hasRole: boolean;
}

/**
 * 登出请求DTO
 */
export class LogoutRequestDto {
  @ApiPropertyOptional({
    description: '刷新令牌（可选）',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsOptional()
  @IsString()
  refresh_token?: string;
}

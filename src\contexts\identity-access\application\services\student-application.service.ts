import { Injectable } from '@nestjs/common';
import {
  CreateStudentCommand,
  UpdateStudentCommand,
  DeleteStudentCommand,
} from '../commands/create-student.command';
import {
  GetStudentQuery,
  GetStudentsQuery,
  SearchStudentsQuery,
  GetStudentsByClassQuery,
  GetStudentsByGradeQuery,
  GetStudentStatisticsQuery,
} from '../queries/get-student.query';
import { StudentUser } from '../../domain/aggregates/user.aggregate';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';
import { SimpleCommandBus } from '@/shared/infrastructure/cqrs/simple-command-bus';
import { SimpleQueryBus } from '@/shared/infrastructure/cqrs/simple-query-bus';

/**
 * 学生应用服务
 * 协调命令和查询处理器，提供统一的应用层接口
 */
@Injectable()
export class StudentApplicationService {
  constructor(
    private readonly commandBus: SimpleCommandBus,
    private readonly queryBus: SimpleQueryBus,
  ) {}

  // ========== 命令操作 ==========

  /**
   * 创建学生
   */
  async createStudent(command: CreateStudentCommand): Promise<StudentUser> {
    return await this.commandBus.execute(command);
  }

  /**
   * 更新学生
   */
  async updateStudent(command: UpdateStudentCommand): Promise<StudentUser> {
    return await this.commandBus.execute(command);
  }

  /**
   * 删除学生
   */
  async deleteStudent(command: DeleteStudentCommand): Promise<void> {
    return await this.commandBus.execute(command);
  }

  /**
   * 批量删除学生
   */
  async batchDeleteStudents(ids: string[]): Promise<void> {
    // 这里可以优化为批量操作，而不是逐个删除
    for (const id of ids) {
      await this.commandBus.execute(new DeleteStudentCommand(id));
    }
  }

  // ========== 查询操作 ==========

  /**
   * 根据ID获取学生
   */
  async getStudentById(id: string): Promise<StudentUser> {
    return await this.queryBus.execute(new GetStudentQuery(id));
  }

  /**
   * 分页获取学生列表
   */
  async getStudents(
    page: number = 1,
    pageSize: number = 10,
    filters?: {
      name?: string;
      phone?: string;
      classId?: string;
      gradeId?: string;
      isActive?: boolean;
    },
    sorting?: {
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
    },
  ): Promise<PaginatedResponseDto<StudentUser>> {
    const query = new GetStudentsQuery(
      page,
      pageSize,
      filters?.name,
      filters?.phone,
      filters?.classId,
      filters?.gradeId,
      filters?.isActive,
      sorting?.sortBy,
      sorting?.sortOrder,
    );

    return await this.queryBus.execute(query);
  }

  /**
   * 搜索学生
   */
  async searchStudents(
    keyword: string,
    classId?: string,
    gradeId?: string,
    limit: number = 20,
  ): Promise<StudentUser[]> {
    const query = new SearchStudentsQuery(keyword, classId, gradeId, limit);
    return await this.queryBus.execute(query);
  }

  /**
   * 获取班级学生
   */
  async getStudentsByClass(
    classId: string,
    includeInactive: boolean = false,
  ): Promise<StudentUser[]> {
    const query = new GetStudentsByClassQuery(classId, includeInactive);
    return await this.queryBus.execute(query);
  }

  /**
   * 获取年级学生
   */
  async getStudentsByGrade(
    gradeId: string,
    includeInactive: boolean = false,
  ): Promise<StudentUser[]> {
    const query = new GetStudentsByGradeQuery(gradeId, includeInactive);
    return await this.queryBus.execute(query);
  }

  /**
   * 获取学生统计信息
   */
  async getStudentStatistics(
    classId?: string,
    gradeId?: string,
  ): Promise<{
    totalStudents: number;
    activeStudents: number;
    maleStudents: number;
    femaleStudents: number;
    averageAge: number;
  }> {
    const query = new GetStudentStatisticsQuery(classId, gradeId);
    return await this.queryBus.execute(query);
  }

  // ========== 业务操作 ==========

  /**
   * 激活学生
   */
  async activateStudent(id: string): Promise<StudentUser> {
    const student = await this.getStudentById(id);
    student.activate();

    // 这里应该通过仓储保存，但为了简化示例，直接返回
    // 实际实现中需要注入仓储并保存
    return student;
  }

  /**
   * 停用学生
   */
  async deactivateStudent(id: string): Promise<StudentUser> {
    const student = await this.getStudentById(id);
    student.deactivate();

    // 这里应该通过仓储保存，但为了简化示例，直接返回
    // 实际实现中需要注入仓储并保存
    return student;
  }

  /**
   * 转班操作
   */
  async transferStudent(
    studentId: string,
    newClassId: string,
    newGradeId?: string,
  ): Promise<StudentUser> {
    const command = new UpdateStudentCommand(
      studentId,
      undefined, // name
      undefined, // phone
      undefined, // gender
      undefined, // avatar
      undefined, // age
      undefined, // birthday
      undefined, // cardNo
      undefined, // cardType
      newClassId,
      newGradeId,
    );

    return await this.commandBus.execute(command);
  }
}

const http = require('http');

// 测试系统信息接口
function testSystemInfo() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/dev-setup/system-info',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头:`, res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('响应数据:', data);
      try {
        const jsonData = JSON.parse(data);
        console.log('解析后的数据:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('数据解析失败:', e.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  req.end();
}

// 等待服务器启动后测试
console.log('等待服务器启动...');
setTimeout(() => {
  console.log('开始测试系统信息接口...');
  testSystemInfo();
}, 5000);

# 生产环境配置

# 数据库配置
DB_HOST=your-production-db-host
DB_PORT=3306
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password
DB_DATABASE=smart_campus_prod

# Redis配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_CACHE_DB=0

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=7d

# 多租户配置
TENANT_ISOLATION=true
DEFAULT_TENANT_ID=system

# 开发者账号配置（生产环境严格限制）
CREATE_DEV_ACCOUNT=false
DEV_ACCOUNT_NAME=系统管理员
DEV_ACCOUNT_PHONE=***********
DEV_ACCOUNT_PASSWORD=your-secure-password
DEV_ACCESS_LEVEL=admin
DEV_ALLOWED_ENVS=production

# 开发者登录限制配置（生产环境严格限制）
DEV_LOGIN_ENABLED=true
DEV_LOGIN_ALLOWED_IPS=your-office-ip,your-vpn-ip
DEV_LOGIN_TIME_RESTRICTION=true
DEV_LOGIN_ALLOWED_HOURS=9-18
DEV_MAX_CONCURRENT_SESSIONS=1
DEV_SESSION_TIMEOUT=4h

# 教师登录限制
TEACHER_LOGIN_ENABLED=false
TEACHER_MAX_CONCURRENT_SESSIONS=3
TEACHER_SESSION_TIMEOUT=8h

# 学生登录限制
STUDENT_LOGIN_ENABLED=false
STUDENT_MAX_CONCURRENT_SESSIONS=2
STUDENT_SESSION_TIMEOUT=4h

# 家长登录限制
PARENT_LOGIN_ENABLED=false
PARENT_MAX_CONCURRENT_SESSIONS=2
PARENT_SESSION_TIMEOUT=12h

# 访客登录限制
VISITOR_LOGIN_ENABLED=true
VISITOR_LOGIN_TIME_RESTRICTION=true
VISITOR_LOGIN_ALLOWED_HOURS=9-17
VISITOR_MAX_CONCURRENT_SESSIONS=1
VISITOR_SESSION_TIMEOUT=2h

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
LOG_MAX_FILE_SIZE=100m
LOG_MAX_FILES=168
LOG_DATE_PATTERN=yyyyMMddHH

# 应用配置
NODE_ENV=production
PORT=3000

# 缓存配置
CACHE_TTL=600
CACHE_MAX_ITEMS=10000

# 文件上传配置
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,pdf

# 邮件配置
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USER=your-email
MAIL_PASS=your-email-password
MAIL_FROM=<EMAIL>

import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheKeyManager } from './redis.config';

/**
 * 缓存服务
 * 提供统一的缓存操作接口
 */
@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  /**
   * 获取缓存值
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);
      if (value !== undefined) {
        this.logger.debug(`Cache hit: ${key}`);
        return value;
      }
      this.logger.debug(`Cache miss: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * 设置缓存值
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
      this.logger.debug(`Cache set: ${key}${ttl ? ` (TTL: ${ttl}s)` : ''}`);
    } catch (error) {
      this.logger.error(`Cache set error for key ${key}:`, error);
    }
  }

  /**
   * 删除缓存值
   */
  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Cache deleted: ${key}`);
    } catch (error) {
      this.logger.error(`Cache delete error for key ${key}:`, error);
    }
  }

  /**
   * 清除所有缓存
   */
  async reset(): Promise<void> {
    try {
      // cache-manager 可能没有reset方法，使用store的reset方法
      const store = (this.cacheManager as any).store;
      if (store && typeof store.reset === 'function') {
        await store.reset();
      }
      this.logger.debug('All cache cleared');
    } catch (error) {
      this.logger.error('Cache reset error:', error);
    }
  }

  /**
   * 批量获取缓存值
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const results: (T | null)[] = [];

    for (const key of keys) {
      const value = await this.get<T>(key);
      results.push(value);
    }

    return results;
  }

  /**
   * 批量设置缓存值
   */
  async mset<T>(keyValuePairs: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    for (const { key, value, ttl } of keyValuePairs) {
      await this.set(key, value, ttl);
    }
  }

  /**
   * 批量删除缓存值
   */
  async mdel(keys: string[]): Promise<void> {
    for (const key of keys) {
      await this.del(key);
    }
  }

  /**
   * 检查缓存键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.cacheManager.get(key);
      return value !== undefined;
    } catch (error) {
      this.logger.error(`Cache exists check error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * 获取或设置缓存值（如果不存在则执行回调函数）
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const cached = await this.get<T>(key);

    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }

  /**
   * 用户相关缓存操作
   */
  async getUserCache<T>(userId: string, suffix?: string): Promise<T | null> {
    const key = CacheKeyManager.userKey(userId, suffix);
    return this.get<T>(key);
  }

  async setUserCache<T>(userId: string, value: T, suffix?: string, ttl?: number): Promise<void> {
    const key = CacheKeyManager.userKey(userId, suffix);
    await this.set(key, value, ttl);
  }

  async delUserCache(userId: string, suffix?: string): Promise<void> {
    const key = CacheKeyManager.userKey(userId, suffix);
    await this.del(key);
  }

  /**
   * 统计数据缓存操作
   */
  async getStatisticsCache<T>(type: string, period: string, ...params: string[]): Promise<T | null> {
    const key = CacheKeyManager.statisticsKey(type, period, ...params);
    return this.get<T>(key);
  }

  async setStatisticsCache<T>(
    type: string,
    period: string,
    value: T,
    ttl?: number,
    ...params: string[]
  ): Promise<void> {
    const key = CacheKeyManager.statisticsKey(type, period, ...params);
    await this.set(key, value, ttl);
  }

  /**
   * 门禁记录缓存操作
   */
  async getAccessRecordCache<T>(recordId: string, suffix?: string): Promise<T | null> {
    const key = CacheKeyManager.accessRecordKey(recordId, suffix);
    return this.get<T>(key);
  }

  async setAccessRecordCache<T>(
    recordId: string,
    value: T,
    suffix?: string,
    ttl?: number,
  ): Promise<void> {
    const key = CacheKeyManager.accessRecordKey(recordId, suffix);
    await this.set(key, value, ttl);
  }

  /**
   * 门禁申请缓存操作
   */
  async getAccessRequestCache<T>(requestId: string, suffix?: string): Promise<T | null> {
    const key = CacheKeyManager.accessRequestKey(requestId, suffix);
    return this.get<T>(key);
  }

  async setAccessRequestCache<T>(
    requestId: string,
    value: T,
    suffix?: string,
    ttl?: number,
  ): Promise<void> {
    const key = CacheKeyManager.accessRequestKey(requestId, suffix);
    await this.set(key, value, ttl);
  }

  /**
   * 租户相关缓存操作
   */
  async getTenantCache<T>(tenantId: string, suffix?: string): Promise<T | null> {
    const key = CacheKeyManager.tenantKey(tenantId, suffix);
    return this.get<T>(key);
  }

  async setTenantCache<T>(
    tenantId: string,
    value: T,
    suffix?: string,
    ttl?: number,
  ): Promise<void> {
    const key = CacheKeyManager.tenantKey(tenantId, suffix);
    await this.set(key, value, ttl);
  }

  /**
   * 权限相关缓存操作
   */
  async getPermissionCache<T>(userId: string, resource?: string): Promise<T | null> {
    const key = CacheKeyManager.permissionKey(userId, resource);
    return this.get<T>(key);
  }

  async setPermissionCache<T>(
    userId: string,
    value: T,
    resource?: string,
    ttl?: number,
  ): Promise<void> {
    const key = CacheKeyManager.permissionKey(userId, resource);
    await this.set(key, value, ttl);
  }

  /**
   * 会话相关缓存操作
   */
  async getSessionCache<T>(sessionId: string): Promise<T | null> {
    const key = CacheKeyManager.sessionKey(sessionId);
    return this.get<T>(key);
  }

  async setSessionCache<T>(sessionId: string, value: T, ttl?: number): Promise<void> {
    const key = CacheKeyManager.sessionKey(sessionId);
    await this.set(key, value, ttl);
  }

  async delSessionCache(sessionId: string): Promise<void> {
    const key = CacheKeyManager.sessionKey(sessionId);
    await this.del(key);
  }
}

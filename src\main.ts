import 'dotenv/config';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { ResponseInterceptor } from './shared/infrastructure/interceptors/response.interceptor';
import { HttpExceptionFilter } from './shared/filters/http-exception.filter';
import { PinoLoggerService } from './shared/infrastructure/logging/pino-logger.service';
import { PinoLoggerConfig } from './shared/infrastructure/logging/pino-logger.config';

async function bootstrap() {
  // 创建 Pino 日志配置
  const pinoConfig = new PinoLoggerConfig();
  const pinoLogger = new PinoLoggerService(pinoConfig);

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter({
      logger: false, // 禁用 Fastify 默认日志，使用我们的 Pino 配置
    }),
    {
      logger: pinoLogger, // 使用自定义 Pino 日志服务
    },
  );

  // 配置CORS
  app.enableCors({
    origin: [
      'http://localhost:8000',
      'http://localhost:3000',
      'http://127.0.0.1:8000',
      'http://127.0.0.1:3000',
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
    ],
    credentials: true,
  });

  // 设置全局前缀（可选）
  app.setGlobalPrefix('api');

  // 应用全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      enableDebugMessages: true,
      disableErrorMessages: false,
    }),
  );

  // 应用全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 应用全局拦截器
  app.useGlobalInterceptors(new ResponseInterceptor());

  // 配置 Swagger
  const config = new DocumentBuilder()
    .setTitle('智能校园 API')
    .setDescription('智能校园系统的 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: '智能校园 API 文档',
  });

  await app.listen(process.env.PORT ?? 3000, '0.0.0.0');

  // 使用 NestJS Logger 记录启动信息
  const logger = new Logger('Bootstrap');
  logger.log(
    `🚀 应用程序正在运行: http://localhost:${process.env.PORT ?? 3000}`,
  );
  logger.log(
    `📚 Swagger 文档: http://localhost:${process.env.PORT ?? 3000}/api/docs`,
  );
}
void bootstrap();

import { IsString, IsOptional, IsN<PERSON>ber, IsDateString, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseResponseDto } from '@/shared/application/dto/base-response.dto';
import { BaseQueryDto } from '@/shared/application/dto/base-query.dto';

/**
 * 创建学生DTO
 */
export class CreateStudentDto {
  @ApiProperty({ description: '姓名', example: '张三' })
  @IsString()
  name: string;

  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  phone: string;

  @ApiPropertyOptional({ description: '性别：0-未知，1-男，2-女', example: 1 })
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄', example: 18 })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiPropertyOptional({ description: '生日', example: '2005-01-01' })
  @IsOptional()
  @IsDateString()
  birthday?: string;

  @ApiPropertyOptional({ description: '身份证号', example: '110101200501011234' })
  @IsOptional()
  @IsString()
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型', example: '身份证' })
  @IsOptional()
  @IsString()
  cardType?: string;

  @ApiPropertyOptional({ description: '班级ID' })
  @IsOptional()
  @IsString()
  classId?: string;

  @ApiPropertyOptional({ description: '年级ID' })
  @IsOptional()
  @IsString()
  gradeId?: string;

  @ApiPropertyOptional({ description: '入学时间', example: '2023-09-01' })
  @IsOptional()
  @IsDateString()
  enrollmentTime?: string;

  @ApiPropertyOptional({ description: '毕业时间', example: '2027-06-30' })
  @IsOptional()
  @IsDateString()
  graduationTime?: string;
}

/**
 * 更新学生DTO
 */
export class UpdateStudentDto {
  @ApiPropertyOptional({ description: '姓名', example: '李四' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '手机号', example: '13900139000' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '性别：0-未知，1-男，2-女', example: 2 })
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiPropertyOptional({ description: '头像URL', example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄', example: 19 })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiPropertyOptional({ description: '生日', example: '2004-01-01' })
  @IsOptional()
  @IsDateString()
  birthday?: string;

  @ApiPropertyOptional({ description: '身份证号', example: '110101200401011234' })
  @IsOptional()
  @IsString()
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型', example: '身份证' })
  @IsOptional()
  @IsString()
  cardType?: string;

  @ApiPropertyOptional({ description: '班级ID' })
  @IsOptional()
  @IsString()
  classId?: string;

  @ApiPropertyOptional({ description: '年级ID' })
  @IsOptional()
  @IsString()
  gradeId?: string;

  @ApiPropertyOptional({ description: '入学时间', example: '2023-09-01' })
  @IsOptional()
  @IsDateString()
  enrollmentTime?: string;

  @ApiPropertyOptional({ description: '毕业时间', example: '2027-06-30' })
  @IsOptional()
  @IsDateString()
  graduationTime?: string;
}

/**
 * 学生查询DTO
 */
export class StudentQueryDto extends BaseQueryDto {
  @ApiPropertyOptional({ description: '姓名（模糊搜索）', example: '张' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '手机号（模糊搜索）', example: '138' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '班级ID' })
  @IsOptional()
  @IsString()
  classId?: string;

  @ApiPropertyOptional({ description: '年级ID' })
  @IsOptional()
  @IsString()
  gradeId?: string;

  @ApiPropertyOptional({ description: '是否激活', example: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: '性别：0-未知，1-男，2-女', example: 1 })
  @IsOptional()
  @IsNumber()
  gender?: number;
}

/**
 * 学生响应DTO
 */
export class StudentResponseDto extends BaseResponseDto {
  @ApiProperty({ description: '姓名' })
  name: string;

  @ApiProperty({ description: '手机号' })
  phone: string;

  @ApiProperty({ description: '用户类型' })
  userType: string;

  @ApiPropertyOptional({ description: '性别：0-未知，1-男，2-女' })
  gender?: number;

  @ApiPropertyOptional({ description: '头像URL' })
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄' })
  age?: number;

  @ApiPropertyOptional({ description: '生日' })
  birthday?: Date;

  @ApiPropertyOptional({ description: '身份证号' })
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型' })
  cardType?: string;

  @ApiPropertyOptional({ description: '班级ID' })
  classId?: string;

  @ApiPropertyOptional({ description: '年级ID' })
  gradeId?: string;

  @ApiPropertyOptional({ description: '入学时间' })
  enrollmentTime?: Date;

  @ApiPropertyOptional({ description: '毕业时间' })
  graduationTime?: Date;

  @ApiProperty({ description: '是否激活' })
  isActive: boolean;

  @ApiProperty({ description: '显示名称' })
  displayName: string;
}

/**
 * 学生统计DTO
 */
export class StudentStatisticsDto {
  @ApiProperty({ description: '学生总数' })
  totalStudents: number;

  @ApiProperty({ description: '激活学生数' })
  activeStudents: number;

  @ApiProperty({ description: '男学生数' })
  maleStudents: number;

  @ApiProperty({ description: '女学生数' })
  femaleStudents: number;

  @ApiProperty({ description: '平均年龄' })
  averageAge: number;
}

/**
 * 批量删除DTO
 */
export class BatchDeleteStudentsDto {
  @ApiProperty({ description: '学生ID列表', type: [String] })
  @IsString({ each: true })
  ids: string[];
}

/**
 * 转班DTO
 */
export class TransferStudentDto {
  @ApiProperty({ description: '新班级ID' })
  @IsString()
  classId: string;

  @ApiPropertyOptional({ description: '新年级ID' })
  @IsOptional()
  @IsString()
  gradeId?: string;
}

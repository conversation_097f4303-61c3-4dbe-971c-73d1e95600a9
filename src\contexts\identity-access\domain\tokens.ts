/**
 * 身份访问上下文的依赖注入令牌
 */

// 仓储令牌
export const USER_REPOSITORY = Symbol('USER_REPOSITORY');
export const USER_QUERY_REPOSITORY = Symbol('USER_QUERY_REPOSITORY');

// 服务令牌
export const JWT_SERVICE = Symbol('JWT_SERVICE');
export const STUDENT_USER_REPOSITORY = Symbol('STUDENT_USER_REPOSITORY');
export const TEACHER_USER_REPOSITORY = Symbol('TEACHER_USER_REPOSITORY');

// 领域服务令牌
export const USER_DOMAIN_SERVICE = Symbol('USER_DOMAIN_SERVICE');

// 应用服务令牌
export const STUDENT_APPLICATION_SERVICE = Symbol(
  'STUDENT_APPLICATION_SERVICE',
);
export const TEACHER_APPLICATION_SERVICE = Symbol(
  'TEACHER_APPLICATION_SERVICE',
);

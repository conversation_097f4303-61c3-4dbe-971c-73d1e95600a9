import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { Redis } from 'ioredis';

/**
 * 消息队列配置
 */
export interface MessageQueueConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  connectTimeout: number;
  lazyConnect: boolean;
}

/**
 * 消息队列配置工厂
 */
export class MessageQueueConfigFactory {
  private static readonly logger = new Logger(MessageQueueConfigFactory.name);

  /**
   * 创建消息队列配置
   */
  static createConfig(configService: ConfigService): MessageQueueConfig {
    const config: MessageQueueConfig = {
      host: configService.get<string>('REDIS_HOST', 'localhost'),
      port: configService.get<number>('REDIS_PORT', 6379),
      password: configService.get<string>('REDIS_PASSWORD'),
      db: configService.get<number>('REDIS_QUEUE_DB', 2),
      keyPrefix: configService.get<string>('QUEUE_KEY_PREFIX', 'smart_campus:queue:'),
      maxRetriesPerRequest: configService.get<number>('QUEUE_MAX_RETRIES', 3),
      retryDelayOnFailover: configService.get<number>('QUEUE_RETRY_DELAY', 100),
      connectTimeout: configService.get<number>('QUEUE_CONNECT_TIMEOUT', 10000),
      lazyConnect: true,
    };

    this.logger.log('Message queue configuration created', {
      host: config.host,
      port: config.port,
      db: config.db,
      keyPrefix: config.keyPrefix,
    });

    return config;
  }

  /**
   * 创建Redis连接
   */
  static createRedisConnection(config: MessageQueueConfig): Redis {
    return new Redis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.db,
      keyPrefix: config.keyPrefix,
      maxRetriesPerRequest: config.maxRetriesPerRequest,
      connectTimeout: config.connectTimeout,
      lazyConnect: config.lazyConnect,
    });
  }
}

/**
 * 消息队列管理器
 */
export class MessageQueueManager {
  private static readonly logger = new Logger(MessageQueueManager.name);
  private readonly redis: Redis;
  private readonly queues = new Map<string, QueueInfo>();

  constructor(config: MessageQueueConfig) {
    this.redis = MessageQueueConfigFactory.createRedisConnection(config);
    this.setupEventHandlers();
  }

  /**
   * 注册队列
   */
  registerQueue(name: string, options: QueueOptions = {}): void {
    const queueInfo: QueueInfo = {
      name,
      options: {
        maxRetries: options.maxRetries || 3,
        retryDelay: options.retryDelay || 1000,
        timeout: options.timeout || 30000,
        concurrency: options.concurrency || 1,
        ...options,
      },
      stats: {
        processed: 0,
        failed: 0,
        retried: 0,
        active: 0,
      },
    };

    this.queues.set(name, queueInfo);
    MessageQueueManager.logger.log(`Queue registered: ${name}`, queueInfo.options);
  }

  /**
   * 发送消息到队列
   */
  async sendMessage(queueName: string, message: any, options: MessageOptions = {}): Promise<string> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue not found: ${queueName}`);
    }

    const messageId = this.generateMessageId();
    const messageData: QueueMessage = {
      id: messageId,
      queueName,
      payload: message,
      attempts: 0,
      maxRetries: options.maxRetries || queue.options.maxRetries,
      createdAt: new Date(),
      scheduledAt: options.delay ? new Date(Date.now() + options.delay) : new Date(),
      priority: options.priority || 0,
    };

    const key = this.getQueueKey(queueName);
    const score = options.delay ? Date.now() + options.delay : Date.now();

    await this.redis.zadd(key, score, JSON.stringify(messageData));

    MessageQueueManager.logger.debug(`Message sent to queue: ${queueName}`, {
      messageId,
      delay: options.delay,
      priority: options.priority,
    });

    return messageId;
  }

  /**
   * 处理队列消息
   */
  async processQueue(queueName: string, processor: MessageProcessor): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue not found: ${queueName}`);
    }

    const key = this.getQueueKey(queueName);
    const now = Date.now();

    // 获取到期的消息
    const messages = await this.redis.zrangebyscore(key, 0, now, 'LIMIT', 0, queue.options.concurrency);

    for (const messageStr of messages) {
      try {
        const message: QueueMessage = JSON.parse(messageStr);
        queue.stats.active++;

        // 从队列中移除消息
        await this.redis.zrem(key, messageStr);

        // 处理消息
        await this.processMessage(message, processor, queue);

        queue.stats.processed++;
        queue.stats.active--;
      } catch (error) {
        queue.stats.active--;
        MessageQueueManager.logger.error(`Error processing message from queue: ${queueName}`, {
          error: error.message,
          message: messageStr,
        });
      }
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(queueName: string): Promise<QueueStats> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue not found: ${queueName}`);
    }

    const key = this.getQueueKey(queueName);
    const waiting = await this.redis.zcard(key);
    const delayed = await this.redis.zcount(key, Date.now(), '+inf');

    return {
      ...queue.stats,
      waiting,
      delayed,
    };
  }

  /**
   * 清空队列
   */
  async clearQueue(queueName: string): Promise<void> {
    const key = this.getQueueKey(queueName);
    await this.redis.del(key);

    const queue = this.queues.get(queueName);
    if (queue) {
      queue.stats = {
        processed: 0,
        failed: 0,
        retried: 0,
        active: 0,
      };
    }

    MessageQueueManager.logger.log(`Queue cleared: ${queueName}`);
  }

  /**
   * 关闭连接
   */
  async close(): Promise<void> {
    await this.redis.quit();
    MessageQueueManager.logger.log('Message queue connection closed');
  }

  /**
   * 处理单个消息
   */
  private async processMessage(
    message: QueueMessage,
    processor: MessageProcessor,
    queue: QueueInfo
  ): Promise<void> {
    try {
      message.attempts++;
      await processor(message.payload, message);
    } catch (error) {
      MessageQueueManager.logger.error(`Message processing failed`, {
        messageId: message.id,
        queueName: message.queueName,
        attempts: message.attempts,
        error: error.message,
      });

      // 重试逻辑
      if (message.attempts < message.maxRetries) {
        await this.retryMessage(message, queue);
        queue.stats.retried++;
      } else {
        await this.handleFailedMessage(message, error);
        queue.stats.failed++;
      }
    }
  }

  /**
   * 重试消息
   */
  private async retryMessage(message: QueueMessage, queue: QueueInfo): Promise<void> {
    const delay = queue.options.retryDelay * Math.pow(2, message.attempts - 1); // 指数退避
    const retryTime = Date.now() + delay;

    message.scheduledAt = new Date(retryTime);

    const key = this.getQueueKey(message.queueName);
    await this.redis.zadd(key, retryTime, JSON.stringify(message));

    MessageQueueManager.logger.debug(`Message scheduled for retry`, {
      messageId: message.id,
      attempts: message.attempts,
      delay,
    });
  }

  /**
   * 处理失败的消息
   */
  private async handleFailedMessage(message: QueueMessage, error: Error): Promise<void> {
    const failedKey = this.getFailedQueueKey(message.queueName);
    const failedMessage = {
      ...message,
      failedAt: new Date(),
      error: error.message,
    };

    await this.redis.lpush(failedKey, JSON.stringify(failedMessage));

    MessageQueueManager.logger.error(`Message moved to failed queue`, {
      messageId: message.id,
      queueName: message.queueName,
      error: error.message,
    });
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      MessageQueueManager.logger.log('Connected to Redis for message queue');
    });

    this.redis.on('error', (error) => {
      MessageQueueManager.logger.error('Redis connection error', error);
    });

    this.redis.on('close', () => {
      MessageQueueManager.logger.log('Redis connection closed');
    });
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取队列键名
   */
  private getQueueKey(queueName: string): string {
    return `queue:${queueName}`;
  }

  /**
   * 获取失败队列键名
   */
  private getFailedQueueKey(queueName: string): string {
    return `queue:${queueName}:failed`;
  }
}

/**
 * 队列选项
 */
export interface QueueOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  concurrency?: number;
}

/**
 * 消息选项
 */
export interface MessageOptions {
  delay?: number;
  priority?: number;
  maxRetries?: number;
}

/**
 * 队列消息
 */
export interface QueueMessage {
  id: string;
  queueName: string;
  payload: any;
  attempts: number;
  maxRetries: number;
  createdAt: Date;
  scheduledAt: Date;
  priority: number;
}

/**
 * 队列信息
 */
interface QueueInfo {
  name: string;
  options: Required<QueueOptions>;
  stats: {
    processed: number;
    failed: number;
    retried: number;
    active: number;
  };
}

/**
 * 队列统计
 */
export interface QueueStats {
  processed: number;
  failed: number;
  retried: number;
  active: number;
  waiting: number;
  delayed: number;
}

/**
 * 消息处理器类型
 */
export type MessageProcessor = (payload: any, message: QueueMessage) => Promise<void>;

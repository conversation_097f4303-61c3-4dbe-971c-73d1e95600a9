import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
  ServiceUnavailableException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { FastifyRequest } from 'fastify';
import {
  DEVELOPER_ONLY_KEY,
  DEV_ENVIRONMENT_ONLY_KEY,
  API_KEY_REQUIRED_KEY,
} from '../decorators/developer.decorator';
import { DeveloperUser } from '../../domain/aggregates/user.aggregate';
import { UserTypeEnum } from '../../domain/value-objects/user-type';

/**
 * 开发者权限守卫
 * 验证用户是否为开发者并具有相应权限
 */
@Injectable()
export class DeveloperGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    // 检查是否需要开发者权限
    const isDeveloperOnly = this.reflector.getAllAndOverride<boolean>(
      DEVELOPER_ONLY_KEY,
      [context.getHandler(), context.getClass()],
    );

    // 检查是否仅限开发环境
    const isDevEnvironmentOnly = this.reflector.getAllAndOverride<boolean>(
      DEV_ENVIRONMENT_ONLY_KEY,
      [context.getHandler(), context.getClass()],
    );

    // 检查是否需要API密钥
    const isApiKeyRequired = this.reflector.getAllAndOverride<boolean>(
      API_KEY_REQUIRED_KEY,
      [context.getHandler(), context.getClass()],
    );

    // 如果不需要开发者权限，直接通过
    if (!isDeveloperOnly && !isDevEnvironmentOnly && !isApiKeyRequired) {
      return true;
    }

    // 检查环境限制
    if (isDevEnvironmentOnly && !this.isDevelopmentEnvironment()) {
      throw new ServiceUnavailableException(
        '此功能仅在开发环境可用',
      );
    }

    // 检查用户是否为开发者
    if (isDeveloperOnly) {
      const user = (request as any).user;

      if (!user) {
        throw new UnauthorizedException('用户未认证');
      }

      if (user.userType !== UserTypeEnum.DEVELOPER) {
        throw new ForbiddenException('权限不足，仅限开发者访问');
      }

      // 如果用户是开发者实例，进行额外检查
      if (user instanceof DeveloperUser) {
        // 检查访问级别
        if (!user.canPerformAdminOperations() && this.requiresAdminAccess(context)) {
          throw new ForbiddenException('权限不足，需要管理员级别访问权限');
        }

        // 检查环境访问权限
        const currentEnv = this.getCurrentEnvironment();
        if (!user.canAccessEnvironment(currentEnv)) {
          throw new ForbiddenException(`权限不足，无法访问 ${currentEnv} 环境`);
        }
      }
    }

    // 检查API密钥
    if (isApiKeyRequired) {
      await this.validateApiKey(request);
    }

    return true;
  }

  /**
   * 验证API密钥
   */
  private async validateApiKey(request: FastifyRequest): Promise<void> {
    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      throw new UnauthorizedException('API密钥缺失');
    }

    // 验证API密钥格式
    if (!this.isValidApiKeyFormat(apiKey)) {
      throw new UnauthorizedException('API密钥格式无效');
    }

    // 这里可以添加更复杂的API密钥验证逻辑
    // 例如从数据库验证API密钥是否有效
    const user = (request as any).user;
    if (user instanceof DeveloperUser) {
      const userApiKey = user.getApiKey();
      if (userApiKey !== apiKey) {
        throw new UnauthorizedException('API密钥无效');
      }
    }
  }

  /**
   * 提取API密钥
   */
  private extractApiKey(request: FastifyRequest): string | null {
    // 从Header中获取
    const headerApiKey = request.headers['x-api-key'] as string;
    if (headerApiKey) {
      return headerApiKey;
    }

    // 从查询参数中获取
    const queryApiKey = (request.query as any)?.api_key;
    if (queryApiKey) {
      return queryApiKey;
    }

    return null;
  }

  /**
   * 验证API密钥格式
   */
  private isValidApiKeyFormat(apiKey: string): boolean {
    // 开发者API密钥格式: dev_timestamp_randomhex
    const devApiKeyPattern = /^dev_\d+_[a-f0-9]{32}$/;
    return devApiKeyPattern.test(apiKey);
  }

  /**
   * 检查是否为开发环境
   */
  private isDevelopmentEnvironment(): boolean {
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    return nodeEnv === 'development' || nodeEnv === 'staging';
  }

  /**
   * 获取当前环境
   */
  private getCurrentEnvironment(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  /**
   * 检查是否需要管理员访问权限
   */
  private requiresAdminAccess(context: ExecutionContext): boolean {
    // 可以通过装饰器或其他方式标记需要管理员权限的接口
    const requiresAdmin = this.reflector.getAllAndOverride<boolean>(
      'requires_admin_access',
      [context.getHandler(), context.getClass()],
    );

    return requiresAdmin || false;
  }
}

/**
 * API密钥守卫
 * 专门用于验证API密钥的守卫
 */
@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    const apiKey = this.extractApiKey(request);

    if (!apiKey) {
      throw new UnauthorizedException('API密钥缺失');
    }

    if (!this.isValidApiKeyFormat(apiKey)) {
      throw new UnauthorizedException('API密钥格式无效');
    }

    // 这里可以添加API密钥验证逻辑
    // 例如从数据库或缓存中验证API密钥

    return true;
  }

  private extractApiKey(request: FastifyRequest): string | null {
    const headerApiKey = request.headers['x-api-key'] as string;
    if (headerApiKey) return headerApiKey;

    const queryApiKey = (request.query as any)?.api_key;
    if (queryApiKey) return queryApiKey;

    return null;
  }

  private isValidApiKeyFormat(apiKey: string): boolean {
    const devApiKeyPattern = /^dev_\d+_[a-f0-9]{32}$/;
    return devApiKeyPattern.test(apiKey);
  }
}

/**
 * 环境限制守卫
 * 限制某些功能只能在特定环境使用
 */
@Injectable()
export class EnvironmentGuard implements CanActivate {
  constructor(
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const isDevEnvironmentOnly = this.reflector.getAllAndOverride<boolean>(
      DEV_ENVIRONMENT_ONLY_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!isDevEnvironmentOnly) {
      return true;
    }

    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    const isDevelopmentEnv = nodeEnv === 'development' || nodeEnv === 'staging';

    if (!isDevelopmentEnv) {
      throw new ServiceUnavailableException(
        '此功能仅在开发环境可用',
      );
    }

    return true;
  }
}

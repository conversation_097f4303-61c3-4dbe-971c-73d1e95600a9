import { Injectable, Inject } from '@nestjs/common';
import { ParentUser } from '../../domain/aggregates/user.aggregate';
import { UserProfile } from '../../domain/value-objects/user-profile';
import { UserDomainService } from '../../domain/services/user-domain.service';
import { UserRepository } from '../../domain/repositories/user.repository';
import { USER_REPOSITORY } from '../../domain/tokens';
import {
  EntityNotFoundException,
  BusinessRuleException,
} from '@/shared/application/exceptions/domain-exception';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';
import { UserTypeEnum } from '../../domain/value-objects/user-type';

/**
 * 家长应用服务
 */
@Injectable()
export class ParentApplicationService {
  constructor(
    @Inject(USER_REPOSITORY) private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  /**
   * 创建家长
   */
  async createParent(parentData: {
    name: string;
    phone: string;
    gender?: number;
    avatar?: string;
    age?: number;
    birthday?: Date;
    cardNo?: string;
    cardType?: string;
    childrenIds?: string[];
    emergencyContact?: string;
    relationship?: string;
    workUnit?: string;
  }): Promise<ParentUser> {
    // 验证手机号是否已存在
    await this.userDomainService.validatePhoneUniqueness(parentData.phone);

    // 创建用户档案
    const profile = new UserProfile({
      name: parentData.name,
      phone: parentData.phone,
      gender: parentData.gender,
      avatar: parentData.avatar,
      age: parentData.age,
      birthday: parentData.birthday,
      cardNo: parentData.cardNo,
      cardType: parentData.cardType,
    });

    // 创建家长用户
    const parent = new ParentUser(profile, parentData.childrenIds);

    // 设置额外信息
    if (parentData.emergencyContact) {
      parent.setEmergencyContact(parentData.emergencyContact);
    }
    if (parentData.relationship) {
      parent.setRelationship(parentData.relationship);
    }
    if (parentData.workUnit) {
      parent.setWorkUnit(parentData.workUnit);
    }

    // 保存到数据库
    return (await this.userRepository.save(parent)) as ParentUser;
  }

  /**
   * 更新家长信息
   */
  async updateParent(
    parentId: string,
    updateData: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      emergencyContact?: string;
      relationship?: string;
      workUnit?: string;
    },
  ): Promise<ParentUser> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    // 如果更新了基本信息，需要验证和更新档案
    if (
      updateData.name ||
      updateData.phone ||
      updateData.gender ||
      updateData.avatar ||
      updateData.age ||
      updateData.birthday ||
      updateData.cardNo ||
      updateData.cardType
    ) {
      const currentProfile = parent.getProfile();
      if (!currentProfile) {
        throw new BusinessRuleException('家长档案数据不完整');
      }

      const newProfile = new UserProfile({
        name: updateData.name || currentProfile.name,
        phone: updateData.phone || currentProfile.phone,
        gender:
          updateData.gender !== undefined
            ? updateData.gender
            : currentProfile.gender,
        avatar: updateData.avatar || currentProfile.avatar,
        age: updateData.age !== undefined ? updateData.age : currentProfile.age,
        birthday: updateData.birthday || currentProfile.birthday,
        cardNo: updateData.cardNo || currentProfile.cardNo,
        cardType: updateData.cardType || currentProfile.cardType,
      });

      // 验证更新
      await this.userDomainService.validateProfileUpdate(parentId, newProfile);
      parent.updateProfile(newProfile);
    }

    // 更新家长特有信息
    if (updateData.emergencyContact !== undefined) {
      parent.setEmergencyContact(updateData.emergencyContact);
    }
    if (updateData.relationship !== undefined) {
      parent.setRelationship(updateData.relationship);
    }
    if (updateData.workUnit !== undefined) {
      parent.setWorkUnit(updateData.workUnit);
    }

    return (await this.userRepository.save(parent)) as ParentUser;
  }

  /**
   * 为家长添加关联学生
   */
  async addChildToParent(
    parentId: string,
    studentId: string,
  ): Promise<ParentUser> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    // 验证学生是否存在
    const student = await this.userRepository.findById(studentId);
    if (!student) {
      throw new EntityNotFoundException('Student', studentId);
    }

    parent.addChild(studentId);
    return (await this.userRepository.save(parent)) as ParentUser;
  }

  /**
   * 移除家长关联的学生
   */
  async removeChildFromParent(
    parentId: string,
    studentId: string,
  ): Promise<ParentUser> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    parent.removeChild(studentId);
    return (await this.userRepository.save(parent)) as ParentUser;
  }

  /**
   * 删除家长
   */
  async deleteParent(parentId: string): Promise<void> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    await this.userRepository.delete(parentId);
  }

  /**
   * 获取家长详情
   */
  async getParent(parentId: string): Promise<ParentUser> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    return parent;
  }

  /**
   * 获取家长列表
   */
  async getParents(params: {
    page?: number;
    pageSize?: number;
    name?: string;
    phone?: string;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<PaginatedResponseDto<ParentUser>> {
    const {
      page = 1,
      pageSize = 10,
      name,
      phone,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = params;

    const criteria = {
      userTypes: ['parent'],
      names: name ? [name] : undefined,
      phones: phone ? [phone] : undefined,
      isActive,
      limit: pageSize,
      offset: (page - 1) * pageSize,
      sortBy,
      sortOrder,
    };

    // 临时实现：使用现有方法
    const allParents = await this.userRepository.findByUserType(
      UserTypeEnum.PARENT,
    );
    const parents = allParents.slice((page - 1) * pageSize, page * pageSize);
    const total = allParents.length;

    return new PaginatedResponseDto(
      parents.filter((user) => user) as ParentUser[],
      total,
      page,
      pageSize,
    );
  }

  /**
   * 根据学生ID获取家长列表
   */
  async getParentsByStudentId(studentId: string): Promise<ParentUser[]> {
    const allParents = await this.userRepository.findByUserType(
      UserTypeEnum.PARENT,
    );
    return allParents
      .filter((user) => user)
      .filter((parent) =>
        (parent as ParentUser).isParentOf(studentId),
      ) as ParentUser[];
  }

  /**
   * 搜索家长
   */
  async searchParents(
    keyword: string,
    limit: number = 20,
  ): Promise<ParentUser[]> {
    // 临时实现：使用现有方法进行简单搜索
    const allParents = await this.userRepository.findByUserType(
      UserTypeEnum.PARENT,
    );
    const parents = allParents
      .filter((parent) => {
        const profile = parent.getProfile();
        return (
          profile?.name?.includes(keyword) || profile?.phone?.includes(keyword)
        );
      })
      .slice(0, limit);
    return parents.filter((user) => user) as ParentUser[];
  }

  /**
   * 激活/停用家长
   */
  async toggleParentStatus(parentId: string): Promise<ParentUser> {
    const parent = await this.userRepository.findById(parentId);
    if (!parent || !(parent instanceof ParentUser)) {
      throw new EntityNotFoundException('Parent', parentId);
    }

    if (parent.isUserActive()) {
      parent.deactivate();
    } else {
      parent.activate();
    }

    return (await this.userRepository.save(parent)) as ParentUser;
  }

  /**
   * 批量删除家长
   */
  async batchDeleteParents(parentIds: string[]): Promise<void> {
    for (const parentId of parentIds) {
      await this.deleteParent(parentId);
    }
  }
}

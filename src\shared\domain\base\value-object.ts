/**
 * 值对象基类
 * 值对象是不可变的，通过值相等性进行比较
 */
export abstract class ValueObject<T> {
  protected readonly _value: T;

  constructor(value: T) {
    this._value = Object.freeze(value);
  }

  /**
   * 获取值
   */
  get value(): T {
    return this._value;
  }

  /**
   * 检查是否相等
   */
  equals(other: ValueObject<T>): boolean {
    if (!other || other.constructor !== this.constructor) {
      return false;
    }
    return this.deepEquals(this._value, other._value);
  }

  /**
   * 深度比较
   */
  private deepEquals(a: any, b: any): boolean {
    if (a === b) return true;

    if (a == null || b == null) return false;

    if (typeof a !== typeof b) return false;

    if (typeof a === 'object') {
      const keysA = Object.keys(a);
      const keysB = Object.keys(b);

      if (keysA.length !== keysB.length) return false;

      for (const key of keysA) {
        if (!keysB.includes(key)) return false;
        if (!this.deepEquals(a[key], b[key])) return false;
      }

      return true;
    }

    return false;
  }

  /**
   * 转换为字符串
   */
  toString(): string {
    return JSON.stringify(this._value);
  }
}

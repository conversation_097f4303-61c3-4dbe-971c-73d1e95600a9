import { CacheModuleOptions } from '@nestjs/cache-manager';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import * as redisStore from 'cache-manager-redis-store';

/**
 * Redis缓存配置工厂
 */
export class RedisCacheConfigFactory {
  private static readonly logger = new Logger(RedisCacheConfigFactory.name);

  /**
   * 创建Redis缓存配置
   */
  static createCacheOptions(configService: ConfigService): CacheModuleOptions {
    const config: CacheModuleOptions = {
      store: redisStore,
      host: configService.get<string>('REDIS_HOST', 'localhost'),
      port: configService.get<number>('REDIS_PORT', 6379),
      password: configService.get<string>('REDIS_PASSWORD'),
      db: configService.get<number>('REDIS_CACHE_DB', 0),
      ttl: configService.get<number>('CACHE_TTL', 300), // 5分钟默认TTL
      max: configService.get<number>('CACHE_MAX_ITEMS', 1000), // 最大缓存项数

      // 连接配置
      connectTimeout: configService.get<number>('REDIS_CONNECT_TIMEOUT', 10000),
      lazyConnect: true,
      retryDelayOnFailover: configService.get<number>('REDIS_RETRY_DELAY', 100),
      maxRetriesPerRequest: configService.get<number>('REDIS_MAX_RETRIES', 3),

      // 键前缀
      keyPrefix: configService.get<string>('CACHE_KEY_PREFIX', 'smart_campus:'),

      // 序列化配置
      serialize: JSON.stringify,
      deserialize: JSON.parse,
    };

    this.logger.log('Redis cache configuration created', {
      host: config.host,
      port: config.port,
      db: config.db,
      ttl: config.ttl,
      keyPrefix: config.keyPrefix,
    });

    return config;
  }

  /**
   * 验证Redis配置
   */
  static validateConfig(configService: ConfigService): void {
    const redisHost = configService.get<string>('REDIS_HOST');
    if (!redisHost) {
      this.logger.warn('REDIS_HOST not configured, using localhost');
    }

    this.logger.log('Redis cache configuration validated');
  }
}

/**
 * 缓存键管理器
 */
export class CacheKeyManager {
  private static readonly KEY_SEPARATOR = ':';

  /**
   * 生成用户相关的缓存键
   */
  static userKey(userId: string, suffix?: string): string {
    return this.buildKey('user', userId, suffix);
  }

  /**
   * 生成门禁记录相关的缓存键
   */
  static accessRecordKey(recordId: string, suffix?: string): string {
    return this.buildKey('access_record', recordId, suffix);
  }

  /**
   * 生成门禁申请相关的缓存键
   */
  static accessRequestKey(requestId: string, suffix?: string): string {
    return this.buildKey('access_request', requestId, suffix);
  }

  /**
   * 生成统计数据相关的缓存键
   */
  static statisticsKey(
    type: string,
    period: string,
    ...params: string[]
  ): string {
    return this.buildKey('stats', type, period, ...params);
  }

  /**
   * 生成查询结果相关的缓存键
   */
  static queryKey(queryName: string, ...params: string[]): string {
    const paramsHash = this.hashParams(params);
    return this.buildKey('query', queryName, paramsHash);
  }

  /**
   * 生成会话相关的缓存键
   */
  static sessionKey(sessionId: string): string {
    return this.buildKey('session', sessionId);
  }

  /**
   * 生成权限相关的缓存键
   */
  static permissionKey(userId: string, resource?: string): string {
    return this.buildKey('permission', userId, resource);
  }

  /**
   * 生成租户相关的缓存键
   */
  static tenantKey(tenantId: string, suffix?: string): string {
    return this.buildKey('tenant', tenantId, suffix);
  }

  /**
   * 构建缓存键
   */
  private static buildKey(...parts: (string | undefined)[]): string {
    return parts
      .filter((part) => part !== undefined && part !== null && part !== '')
      .join(this.KEY_SEPARATOR);
  }

  /**
   * 对参数进行哈希处理
   */
  private static hashParams(params: string[]): string {
    if (params.length === 0) return '';

    const paramString = params.join('|');
    // 简单哈希函数，实际项目中可以使用更复杂的哈希算法
    let hash = 0;
    for (let i = 0; i < paramString.length; i++) {
      const char = paramString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 解析缓存键
   */
  static parseKey(key: string): string[] {
    return key.split(this.KEY_SEPARATOR);
  }

  /**
   * 生成模式匹配键（用于批量删除）
   */
  static patternKey(...parts: (string | '*')[]): string {
    return parts.join(this.KEY_SEPARATOR);
  }
}

/**
 * 缓存策略配置
 */
export class CacheStrategy {
  /**
   * 用户数据缓存策略
   */
  static readonly USER_DATA = {
    ttl: 300, // 5分钟
    refreshThreshold: 60, // 1分钟内刷新
  };

  /**
   * 查询结果缓存策略
   */
  static readonly QUERY_RESULT = {
    ttl: 120, // 2分钟
    refreshThreshold: 30, // 30秒内刷新
  };

  /**
   * 统计数据缓存策略
   */
  static readonly STATISTICS = {
    ttl: 600, // 10分钟
    refreshThreshold: 120, // 2分钟内刷新
  };

  /**
   * 会话数据缓存策略
   */
  static readonly SESSION = {
    ttl: 1800, // 30分钟
    refreshThreshold: 300, // 5分钟内刷新
  };

  /**
   * 权限数据缓存策略
   */
  static readonly PERMISSION = {
    ttl: 900, // 15分钟
    refreshThreshold: 180, // 3分钟内刷新
  };

  /**
   * 获取缓存策略
   */
  static getStrategy(
    type: 'user' | 'query' | 'statistics' | 'session' | 'permission',
  ): {
    ttl: number;
    refreshThreshold: number;
  } {
    switch (type) {
      case 'user':
        return this.USER_DATA;
      case 'query':
        return this.QUERY_RESULT;
      case 'statistics':
        return this.STATISTICS;
      case 'session':
        return this.SESSION;
      case 'permission':
        return this.PERMISSION;
      default:
        return this.QUERY_RESULT;
    }
  }
}

/**
 * 缓存健康检查
 */
export class CacheHealthCheck {
  private static readonly logger = new Logger(CacheHealthCheck.name);

  /**
   * 检查缓存健康状态
   */
  static async checkHealth(cacheManager: any): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      memoryUsage?: number;
      keyCount?: number;
      lastError?: string;
    };
  }> {
    const startTime = Date.now();
    const testKey = 'health_check_' + Date.now();
    const testValue = 'test';

    try {
      // 测试写入
      await cacheManager.set(testKey, testValue, 10);

      // 测试读取
      const retrievedValue = await cacheManager.get(testKey);

      // 测试删除
      await cacheManager.del(testKey);

      const responseTime = Date.now() - startTime;

      if (retrievedValue !== testValue) {
        throw new Error('Cache value mismatch');
      }

      // 获取缓存信息（如果支持）
      let memoryUsage: number | undefined;
      let keyCount: number | undefined;

      try {
        const info = await cacheManager.store.getClient().info('memory');
        const memoryMatch = info.match(/used_memory:(\d+)/);
        if (memoryMatch) {
          memoryUsage = parseInt(memoryMatch[1]);
        }

        const dbSize = await cacheManager.store.getClient().dbsize();
        keyCount = dbSize;
      } catch (infoError) {
        // 忽略获取信息的错误
      }

      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime,
          memoryUsage,
          keyCount,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      this.logger.error('Cache health check failed', {
        error: error.message,
        responseTime,
      });

      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime,
          lastError: error.message,
        },
      };
    }
  }
}

import { DomainEvent } from '@/shared/domain/events/domain-event';

/**
 * 应用事件接口
 * 应用层事件，不同于领域事件
 */
export interface ApplicationEvent {
  readonly eventId: string;
  readonly eventName: string;
  readonly occurredOn: Date;
  readonly data: Record<string, any>;
}

/**
 * 事件处理器接口
 */
export interface EventHandler<TEvent extends ApplicationEvent | DomainEvent> {
  handle(event: TEvent): Promise<void>;
}

/**
 * 事件总线接口
 */
export interface EventBus {
  /**
   * 发布事件
   */
  publish(event: ApplicationEvent | DomainEvent): Promise<void>;

  /**
   * 批量发布事件
   */
  publishAll(events: (ApplicationEvent | DomainEvent)[]): Promise<void>;

  /**
   * 订阅事件
   */
  subscribe<TEvent extends ApplicationEvent | DomainEvent>(
    eventName: string,
    handler: EventHandler<TEvent>,
  ): void;

  /**
   * 取消订阅
   */
  unsubscribe(eventName: string, handler: EventHandler<any>): void;
}

/**
 * 基础应用事件类
 */
export abstract class BaseApplicationEvent implements ApplicationEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;

  constructor(
    public readonly eventName: string,
    public readonly data: Record<string, any>,
  ) {
    this.eventId = this.generateId();
    this.occurredOn = new Date();
  }

  private generateId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * 集成事件
 * 用于跨限界上下文的通信
 */
export abstract class IntegrationEvent extends BaseApplicationEvent {
  constructor(
    eventName: string,
    data: Record<string, any>,
    public readonly version: number = 1,
  ) {
    super(eventName, data);
  }
}

/**
 * 事件存储接口
 */
export interface EventStore {
  /**
   * 保存事件
   */
  save(event: ApplicationEvent | DomainEvent): Promise<void>;

  /**
   * 批量保存事件
   */
  saveAll(events: (ApplicationEvent | DomainEvent)[]): Promise<void>;

  /**
   * 根据聚合ID获取事件
   */
  getEventsByAggregateId(aggregateId: string): Promise<DomainEvent[]>;

  /**
   * 根据事件类型获取事件
   */
  getEventsByType(
    eventType: string,
  ): Promise<(ApplicationEvent | DomainEvent)[]>;

  /**
   * 获取指定时间范围内的事件
   */
  getEventsByTimeRange(
    startTime: Date,
    endTime: Date,
  ): Promise<(ApplicationEvent | DomainEvent)[]>;
}

/**
 * 事件发布器实现
 */
export class SimpleEventPublisher implements EventBus {
  private handlers = new Map<string, EventHandler<any>[]>();

  async publish(event: ApplicationEvent | DomainEvent): Promise<void> {
    const eventName = this.getEventName(event);
    const handlers = this.handlers.get(eventName) || [];

    await Promise.all(handlers.map((handler) => handler.handle(event)));
  }

  async publishAll(events: (ApplicationEvent | DomainEvent)[]): Promise<void> {
    await Promise.all(events.map((event) => this.publish(event)));
  }

  subscribe<TEvent extends ApplicationEvent | DomainEvent>(
    eventName: string,
    handler: EventHandler<TEvent>,
  ): void {
    if (!this.handlers.has(eventName)) {
      this.handlers.set(eventName, []);
    }
    this.handlers.get(eventName)!.push(handler);
  }

  unsubscribe(eventName: string, handler: EventHandler<any>): void {
    const handlers = this.handlers.get(eventName);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private getEventName(event: ApplicationEvent | DomainEvent): string {
    if ('eventName' in event) {
      return event.eventName;
    }
    if ('getEventName' in event) {
      return event.getEventName();
    }
    return (event as any).constructor.name;
  }
}

/**
 * 事件处理器装饰器
 */
export function EventHandler(eventName: string) {
  return function (target: any) {
    Reflect.defineMetadata('event-handler', eventName, target);
    return target;
  };
}

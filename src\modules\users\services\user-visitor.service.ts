import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like } from 'typeorm';
import { UserVisitor } from '@entities/users/user-visitor.entity';
import {
  CreateUserVisitorDto,
  UpdateUserVisitorDto,
  QueryUserVisitorDto,
  UserVisitorResponseDto,
} from '../dto/user-visitor.dto';
import { ApiException } from '@/common/exceptions/api.exception';

@Injectable()
export class UserVisitorService {
  private readonly logger = new Logger(UserVisitorService.name);

  constructor(
    @InjectRepository(UserVisitor)
    private readonly userVisitorRepository: Repository<UserVisitor>,
  ) {}

  /**
   * 创建访客
   */
  async create(createDto: CreateUserVisitorDto): Promise<UserVisitorResponseDto> {
    // 检查手机号是否已存在
    const existingPhone = await this.userVisitorRepository.findOne({
      where: { phone: createDto.phone },
    });

    if (existingPhone) {
      throw ApiException.Error('手机号已存在');
    }

    // 转换日期字符串为Date对象
    const visitorData = {
      ...createDto,
      birthday: createDto.birthday ? new Date(createDto.birthday) : undefined,
    };

    const visitor = this.userVisitorRepository.create(visitorData);
    const savedVisitor = await this.userVisitorRepository.save(visitor);
    return this.transformToResponseDto(savedVisitor);
  }

  /**
   * 根据ID查找访客
   */
  async findById(id: string): Promise<UserVisitorResponseDto> {
    const visitor = await this.userVisitorRepository.findOne({
      where: { id },
    });

    if (!visitor) {
      throw ApiException.Error('访客不存在');
    }

    return this.transformToResponseDto(visitor);
  }

  /**
   * 分页查询访客
   */
  async findMany(
    query: QueryUserVisitorDto,
  ): Promise<{ data: UserVisitorResponseDto[]; total: number; page: number; pageSize: number }> {
    const { page = 1, pageSize = 10, name, phone } = query;
    const skip = (page - 1) * pageSize;

    const whereConditions: FindOptionsWhere<UserVisitor> = {};

    if (name) {
      whereConditions.name = Like(`%${name}%`);
    }

    if (phone) {
      whereConditions.phone = Like(`%${phone}%`);
    }

    const [visitors, total] = await this.userVisitorRepository.findAndCount({
      where: whereConditions,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' },
    });

    return {
      data: visitors.map(this.transformToResponseDto),
      total,
      page,
      pageSize,
    };
  }

  /**
   * 更新访客信息
   */
  async update(id: string, updateDto: UpdateUserVisitorDto): Promise<UserVisitorResponseDto> {
    const visitor = await this.userVisitorRepository.findOne({
      where: { id },
    });

    if (!visitor) {
      throw ApiException.Error('访客不存在');
    }

    // 如果更新手机号，检查是否已存在
    if (updateDto.phone && updateDto.phone !== visitor.phone) {
      const existingPhone = await this.userVisitorRepository.findOne({
        where: { phone: updateDto.phone },
      });

      if (existingPhone) {
        throw ApiException.Error('手机号已存在');
      }
    }

    // 转换日期字符串为Date对象
    const updateData = {
      ...updateDto,
      birthday: updateDto.birthday ? new Date(updateDto.birthday) : undefined,
    };

    await this.userVisitorRepository.update(id, updateData);
    const updatedVisitor = await this.userVisitorRepository.findOne({
      where: { id },
    });

    return this.transformToResponseDto(updatedVisitor!);
  }

  /**
   * 删除访客
   */
  async delete(id: string): Promise<void> {
    const visitor = await this.userVisitorRepository.findOne({
      where: { id },
    });

    if (!visitor) {
      throw ApiException.Error('访客不存在');
    }

    await this.userVisitorRepository.softDelete(id);
  }

  /**
   * 批量删除访客
   */
  async batchDelete(ids: string[]): Promise<void> {
    await this.userVisitorRepository.softDelete(ids);
  }

  /**
   * 根据手机号查找访客
   */
  async findByPhone(phone: string): Promise<UserVisitor | null> {
    return await this.userVisitorRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 转换为响应DTO
   */
  private transformToResponseDto = (visitor: UserVisitor): UserVisitorResponseDto => ({
    id: visitor.id,
    name: visitor.name,
    phone: visitor.phone,
    gender: visitor.gender,
    avatar: visitor.avatar,
    age: visitor.age,
    birthday: visitor.birthday,
    cardNo: visitor.cardNo,
    cardType: visitor.cardType,
    carNo: visitor.carNo,
    createdAt: visitor.createdAt,
    updatedAt: visitor.updatedAt,
  });
}

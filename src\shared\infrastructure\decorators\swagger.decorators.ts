import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';

// 定义 Swagger schema 类型
type SwaggerSchema = {
  type: string;
  properties?: Record<string, any>;
  items?: any;
  example?: any;
};

/**
 * 成功响应装饰器
 */
export function ApiSuccessResponse(
  summary: string,
  description?: string,
  dataSchema?: SwaggerSchema,
  status: number = 200,
) {
  return applyDecorators(
    ApiOperation({
      summary,
      description,
    }),
    ApiResponse({
      status,
      description: '成功响应',
      schema: {
        type: 'object',
        properties: {
          errCode: { type: 'number', example: 0 },
          message: { type: 'string', example: 'success' },
          data: dataSchema || { type: 'object' },
          timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
        },
      },
    }),
  );
}

/**
 * 分页响应装饰器
 */
export function ApiPaginatedResponse(
  summary: string,
  itemSchema?: SwaggerSchema,
  description?: string,
) {
  return applyDecorators(
    ApiOperation({
      summary,
      description,
    }),
    ApiResponse({
      status: 200,
      description: '分页响应',
      schema: {
        type: 'object',
        properties: {
          errCode: { type: 'number', example: 0 },
          message: { type: 'string', example: 'success' },
          data: {
            type: 'object',
            properties: {
              list: {
                type: 'array',
                items: itemSchema || { type: 'object' },
              },
              pagination: {
                type: 'object',
                properties: {
                  total: { type: 'number', example: 100 },
                  page: { type: 'number', example: 1 },
                  pageSize: { type: 'number', example: 10 },
                  totalPages: { type: 'number', example: 10 },
                },
              },
            },
          },
          timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
        },
      },
    }),
  );
}

/**
 * 错误响应装饰器
 */
export function ApiErrorResponse(status: number, message: string) {
  return ApiResponse({
    status,
    description: message,
    schema: {
      type: 'object',
      properties: {
        errCode: { type: 'number', example: status },
        message: { type: 'string', example: message },
        data: { type: 'null', example: null },
        timestamp: { type: 'string', example: '2024-01-15 10:30:00' },
      },
    },
  });
}

/**
 * 常用错误响应装饰器组合
 */
export function ApiCommonErrorResponses() {
  return applyDecorators(
    ApiErrorResponse(400, '请求参数错误'),
    ApiErrorResponse(401, '未授权访问'),
    ApiErrorResponse(403, '权限不足'),
    ApiErrorResponse(404, '资源不存在'),
    ApiErrorResponse(500, '服务器内部错误'),
  );
}

/**
 * CRUD操作装饰器
 */
export function ApiCrudOperation(
  operation: 'create' | 'read' | 'update' | 'delete' | 'list',
  resource: string,
  dataSchema?: SwaggerSchema,
) {
  const operations = {
    create: {
      summary: `创建${resource}`,
      description: `创建新的${resource}记录`,
      status: 201,
    },
    read: {
      summary: `获取${resource}详情`,
      description: `根据ID获取${resource}的详细信息`,
      status: 200,
    },
    update: {
      summary: `更新${resource}`,
      description: `更新指定${resource}的信息`,
      status: 200,
    },
    delete: {
      summary: `删除${resource}`,
      description: `删除指定的${resource}记录`,
      status: 200,
    },
    list: {
      summary: `获取${resource}列表`,
      description: `分页获取${resource}列表，支持筛选和排序`,
      status: 200,
    },
  };

  const config = operations[operation];

  if (operation === 'list') {
    return applyDecorators(
      ApiPaginatedResponse(config.summary, dataSchema, config.description),
      ApiCommonErrorResponses(),
    );
  }

  return applyDecorators(
    ApiSuccessResponse(config.summary, config.description, dataSchema, config.status),
    ApiCommonErrorResponses(),
  );
}

/**
 * 分页查询参数装饰器
 */
export function ApiPaginationQuery() {
  return applyDecorators(
    ApiQuery({
      name: 'page',
      required: false,
      type: Number,
      description: '页码，从1开始',
      example: 1,
    }),
    ApiQuery({
      name: 'pageSize',
      required: false,
      type: Number,
      description: '每页大小，最大100',
      example: 10,
    }),
    ApiQuery({
      name: 'sortBy',
      required: false,
      type: String,
      description: '排序字段',
      example: 'createdAt',
    }),
    ApiQuery({
      name: 'sortOrder',
      required: false,
      enum: ['ASC', 'DESC'],
      description: '排序方向',
      example: 'DESC',
    }),
  );
}

/**
 * 租户相关API装饰器
 */
export function ApiTenantOperation(
  summary: string,
  description?: string,
  dataSchema?: SwaggerSchema,
) {
  return applyDecorators(
    ApiSuccessResponse(summary, description, dataSchema),
    ApiErrorResponse(403, '租户访问权限不足'),
    ApiCommonErrorResponses(),
  );
}

/**
 * 多租户管理API装饰器
 */
export function ApiMultiTenantOperation(
  summary: string,
  description?: string,
  dataSchema?: SwaggerSchema,
) {
  return applyDecorators(
    ApiSuccessResponse(summary, description, dataSchema),
    ApiErrorResponse(403, '需要超级管理员权限'),
    ApiErrorResponse(403, '跨租户访问权限不足'),
    ApiCommonErrorResponses(),
  );
}

/**
 * ID参数装饰器
 */
export function ApiIdParam(name: string = 'id', description?: string) {
  return ApiParam({
    name,
    type: String,
    description: description || `${name}参数`,
    example: '01234567-89ab-cdef-0123-456789abcdef',
  });
}

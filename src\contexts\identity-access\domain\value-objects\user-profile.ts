import { ValueObject } from '@/shared/domain/base/value-object';
import { ValidationException } from '@/shared/application/exceptions/domain-exception';

/**
 * 用户档案值对象
 */
export interface UserProfileProps {
  name: string;
  phone: string;
  gender?: number;
  avatar?: string;
  age?: number;
  birthday?: Date;
  cardNo?: string;
  cardType?: string;
}

export class UserProfile extends ValueObject<UserProfileProps> {
  constructor(props: UserProfileProps) {
    UserProfile.validate(props);
    super(props);
  }

  private static validate(props: UserProfileProps): void {
    const errors: Record<string, string[]> = {};

    // 验证姓名
    if (!props.name || props.name.trim().length === 0) {
      errors.name = ['姓名不能为空'];
    } else if (props.name.length > 50) {
      errors.name = ['姓名长度不能超过50个字符'];
    }

    // 验证手机号
    if (!props.phone || props.phone.trim().length === 0) {
      errors.phone = ['手机号不能为空'];
    } else if (!/^1[3-9]\d{9}$/.test(props.phone)) {
      errors.phone = ['手机号格式不正确'];
    }

    // 验证性别
    if (props.gender !== undefined && ![0, 1, 2].includes(props.gender)) {
      errors.gender = ['性别值无效，应为0(未知)、1(男)或2(女)'];
    }

    // 验证年龄
    if (props.age !== undefined && (props.age < 0 || props.age > 150)) {
      errors.age = ['年龄应在0-150之间'];
    }

    // 验证身份证号
    if (props.cardNo && props.cardType === '身份证') {
      if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(props.cardNo)) {
        errors.cardNo = ['身份证号格式不正确'];
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new ValidationException(errors);
    }
  }

  get name(): string {
    return this._value.name;
  }

  get phone(): string {
    return this._value.phone;
  }

  get gender(): number | undefined {
    return this._value.gender;
  }

  get avatar(): string | undefined {
    return this._value.avatar;
  }

  get age(): number | undefined {
    return this._value.age;
  }

  get birthday(): Date | undefined {
    return this._value.birthday;
  }

  get cardNo(): string | undefined {
    return this._value.cardNo;
  }

  get cardType(): string | undefined {
    return this._value.cardType;
  }

  /**
   * 更新档案信息
   */
  updateProfile(updates: Partial<UserProfileProps>): UserProfile {
    return new UserProfile({
      ...this._value,
      ...updates,
    });
  }

  /**
   * 检查是否为成年人
   */
  isAdult(): boolean {
    if (!this._value.age && !this._value.birthday) {
      return false;
    }

    if (this._value.age) {
      return this._value.age >= 18;
    }

    if (this._value.birthday) {
      const today = new Date();
      const birthDate = new Date(this._value.birthday);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 18;
      }
      
      return age >= 18;
    }

    return false;
  }
}

import { Entity, Column, TableI<PERSON>eritance, ChildEntity } from 'typeorm';
import { AggregateRoot } from '@/shared/domain/base/aggregate-root';
import { UserProfile } from '../value-objects/user-profile';
import { UserType, UserTypeEnum } from '../value-objects/user-type';
import { UserCreatedEvent } from '../events/user-created.event';
import { UserUpdatedEvent } from '../events/user-updated.event';
import { BusinessRuleException } from '@/shared/application/exceptions/domain-exception';

/**
 * 用户聚合根
 */
@Entity('users')
@TableInheritance({
  column: { type: 'varchar', name: 'user_type', length: 20 },
})
export abstract class User extends AggregateRoot {
  // userType 由 @TableInheritance 自动管理，不需要手动定义列
  protected userType: UserTypeEnum;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 50,
    comment: '姓名',
  })
  protected name: string;

  @Column({
    name: 'phone',
    type: 'varchar',
    length: 20,
    unique: true,
    comment: '手机号',
  })
  protected phone: string;

  @Column({
    name: 'gender',
    type: 'tinyint',
    nullable: true,
    comment: '性别：0-未知，1-男，2-女',
  })
  protected gender?: number;

  @Column({
    name: 'avatar',
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '头像URL',
  })
  protected avatar?: string;

  @Column({
    name: 'age',
    type: 'int',
    nullable: true,
    comment: '年龄',
  })
  protected age?: number;

  @Column({
    name: 'birthday',
    type: 'date',
    nullable: true,
    comment: '生日',
  })
  protected birthday?: Date;

  @Column({
    name: 'card_no',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '证件号码',
  })
  protected cardNo?: string;

  @Column({
    name: 'card_type',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '证件类型',
  })
  protected cardType?: string;

  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
    comment: '是否激活',
  })
  protected isActive: boolean;

  @Column({
    name: 'password',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '密码哈希值',
  })
  protected password: string;

  @Column({
    name: 'last_login_at',
    type: 'datetime',
    nullable: true,
    comment: '最后登录时间',
  })
  protected lastLoginAt: Date;

  constructor(userType?: UserTypeEnum, profile?: UserProfile) {
    super();

    // TypeORM需要无参数构造函数，所以参数都是可选的
    if (userType && profile) {
      this.userType = userType;
      this.updateProfile(profile);
      this.isActive = true;

      // 发布用户创建事件
      this.addDomainEvent(
        new UserCreatedEvent(this.id, this.userType, profile),
      );
    }
  }

  /**
   * 获取用户类型
   */
  getUserType(): UserType {
    return new UserType(this.userType);
  }

  /**
   * 获取用户档案
   */
  getProfile(): UserProfile | null {
    // 如果没有基本信息，返回null（用于TypeORM实体初始化）
    if (!this.name || !this.phone) {
      return null;
    }

    return new UserProfile({
      name: this.name,
      phone: this.phone,
      gender: this.gender,
      avatar: this.avatar,
      age: this.age,
      birthday: this.birthday,
      cardNo: this.cardNo,
      cardType: this.cardType,
    });
  }

  /**
   * 更新用户档案
   */
  updateProfile(profile: UserProfile): void {
    const oldProfile = this.getProfile();

    this.name = profile.name;
    this.phone = profile.phone;
    this.gender = profile.gender;
    this.avatar = profile.avatar;
    this.age = profile.age;
    this.birthday = profile.birthday;
    this.cardNo = profile.cardNo;
    this.cardType = profile.cardType;

    this.markAsModified();

    // 发布用户更新事件
    // 只有在有旧档案时才发布更新事件
    if (oldProfile) {
      this.addDomainEvent(new UserUpdatedEvent(this.id, oldProfile, profile));
    }
  }

  /**
   * 激活用户
   */
  activate(): void {
    if (this.isActive) {
      throw new BusinessRuleException('用户已经是激活状态');
    }

    this.isActive = true;
    this.markAsModified();
  }

  /**
   * 停用用户
   */
  deactivate(): void {
    if (!this.isActive) {
      throw new BusinessRuleException('用户已经是停用状态');
    }

    this.isActive = false;
    this.markAsModified();
  }

  /**
   * 检查用户是否激活
   */
  isUserActive(): boolean {
    return this.isActive;
  }

  /**
   * 检查用户是否被锁定
   */
  isLocked(): boolean {
    // 这里可以添加用户锁定的逻辑
    // 例如检查登录失败次数、锁定时间等
    return false; // 暂时返回false，后续可以扩展
  }

  /**
   * 获取密码哈希值
   */
  getPasswordHash(): string {
    return this.password || '';
  }

  /**
   * 设置密码哈希值
   */
  setPassword(passwordHash: string): void {
    this.password = passwordHash;
    this.markAsModified();
  }

  /**
   * 更新最后登录时间
   */
  updateLastLoginTime(): void {
    this.lastLoginAt = new Date();
    this.markAsModified();
  }

  /**
   * 获取用户类型字符串
   */
  getUserTypeString(): string {
    return this.userType;
  }

  /**
   * 检查是否可以执行某个操作
   */
  canPerformAction(action: string): boolean {
    if (!this.isActive) {
      return false;
    }

    // 子类可以重写此方法实现特定的权限检查
    return true;
  }

  /**
   * 获取显示名称
   */
  getDisplayName(): string {
    return `${this.name}(${this.getUserType().getDisplayName()})`;
  }
}

/**
 * 学生用户
 */
@ChildEntity('student')
export class StudentUser extends User {
  @Column({
    name: 'class_id',
    type: 'varchar',
    length: 36,
    nullable: true,
    comment: '班级ID',
  })
  private classId?: string;

  @Column({
    name: 'grade_id',
    type: 'varchar',
    length: 36,
    nullable: true,
    comment: '年级ID',
  })
  private gradeId?: string;

  @Column({
    name: 'enrollment_time',
    type: 'date',
    nullable: true,
    comment: '入学时间',
  })
  private enrollmentTime?: Date;

  @Column({
    name: 'graduation_time',
    type: 'date',
    nullable: true,
    comment: '毕业时间',
  })
  private graduationTime?: Date;

  constructor(profile: UserProfile, classId?: string, gradeId?: string) {
    super(UserTypeEnum.STUDENT, profile);
    this.classId = classId;
    this.gradeId = gradeId;
  }

  getClassId(): string | undefined {
    return this.classId;
  }

  getGradeId(): string | undefined {
    return this.gradeId;
  }

  updateClassInfo(classId?: string, gradeId?: string): void {
    this.classId = classId;
    this.gradeId = gradeId;
    this.markAsModified();
  }

  /**
   * 检查是否是学生
   */
  isStudent(): boolean {
    return true;
  }

  /**
   * 设置入学时间
   */
  setEnrollmentTime(enrollmentTime: Date): void {
    this.enrollmentTime = enrollmentTime;
    this.markAsModified();
  }

  /**
   * 设置毕业时间
   */
  setGraduationTime(graduationTime: Date): void {
    this.graduationTime = graduationTime;
    this.markAsModified();
  }

  /**
   * 检查是否已毕业
   */
  isGraduated(): boolean {
    return this.graduationTime ? new Date() > this.graduationTime : false;
  }
}

/**
 * 教师用户
 */
@ChildEntity('teacher')
export class TeacherUser extends User {
  @Column({
    name: 'car_no',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '车牌号',
  })
  private carNo?: string;

  constructor(profile: UserProfile, carNo?: string) {
    super(UserTypeEnum.TEACHER, profile);
    this.carNo = carNo;
  }

  getCarNo(): string | undefined {
    return this.carNo;
  }

  updateCarNo(carNo?: string): void {
    this.carNo = carNo;
    this.markAsModified();
  }
}

/**
 * 家长用户
 */
@ChildEntity('parent')
export class ParentUser extends User {
  @Column({
    name: 'children_ids',
    type: 'text',
    nullable: true,
    comment: '关联的学生ID列表（JSON格式）',
  })
  private childrenIds: string;

  @Column({
    name: 'emergency_contact',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '紧急联系电话',
  })
  private emergencyContact: string;

  @Column({
    name: 'relationship',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '与学生关系',
  })
  private relationship: string;

  @Column({
    name: 'work_unit',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '工作单位',
  })
  private workUnit: string;

  constructor(profile?: UserProfile, childrenIds?: string[]) {
    super(UserTypeEnum.PARENT, profile);
    if (childrenIds) {
      this.childrenIds = JSON.stringify(childrenIds);
    }
  }

  /**
   * 添加关联学生
   */
  addChild(studentId: string): void {
    const children = this.getChildrenIds();
    if (!children.includes(studentId)) {
      children.push(studentId);
      this.childrenIds = JSON.stringify(children);
    }
  }

  /**
   * 移除关联学生
   */
  removeChild(studentId: string): void {
    const children = this.getChildrenIds();
    const index = children.indexOf(studentId);
    if (index > -1) {
      children.splice(index, 1);
      this.childrenIds = JSON.stringify(children);
    }
  }

  /**
   * 获取关联的学生ID列表
   */
  getChildrenIds(): string[] {
    if (!this.childrenIds) return [];
    try {
      return JSON.parse(this.childrenIds);
    } catch {
      return [];
    }
  }

  /**
   * 设置紧急联系电话
   */
  setEmergencyContact(contact: string): void {
    this.emergencyContact = contact;
  }

  /**
   * 获取紧急联系电话
   */
  getEmergencyContact(): string | null {
    return this.emergencyContact;
  }

  /**
   * 设置与学生关系
   */
  setRelationship(relationship: string): void {
    this.relationship = relationship;
  }

  /**
   * 获取与学生关系
   */
  getRelationship(): string | null {
    return this.relationship;
  }

  /**
   * 设置工作单位
   */
  setWorkUnit(workUnit: string): void {
    this.workUnit = workUnit;
  }

  /**
   * 获取工作单位
   */
  getWorkUnit(): string | null {
    return this.workUnit;
  }

  /**
   * 检查是否为指定学生的家长
   */
  isParentOf(studentId: string): boolean {
    return this.getChildrenIds().includes(studentId);
  }
}

/**
 * 访客用户
 */
@ChildEntity('visitor')
export class VisitorUser extends User {
  @Column({
    name: 'visit_purpose',
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '访问目的',
  })
  private visitPurpose: string;

  @Column({
    name: 'visit_start_time',
    type: 'datetime',
    nullable: true,
    comment: '访问开始时间',
  })
  private visitStartTime: Date;

  @Column({
    name: 'visit_end_time',
    type: 'datetime',
    nullable: true,
    comment: '访问结束时间',
  })
  private visitEndTime: Date;

  @Column({
    name: 'host_name',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '接待人姓名',
  })
  private hostName: string;

  @Column({
    name: 'host_phone',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '接待人电话',
  })
  private hostPhone: string;

  @Column({
    name: 'company',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '所属公司/机构',
  })
  private company: string;

  @Column({
    name: 'is_approved',
    type: 'boolean',
    default: false,
    comment: '是否已审批',
  })
  private isApproved: boolean;

  constructor(profile?: UserProfile, visitPurpose?: string) {
    super(UserTypeEnum.VISITOR, profile);
    this.visitPurpose = visitPurpose || '';
    this.isApproved = false;
  }

  /**
   * 设置访问时间
   */
  setVisitTime(startTime: Date, endTime: Date): void {
    if (startTime >= endTime) {
      throw new Error('访问开始时间必须早于结束时间');
    }
    this.visitStartTime = startTime;
    this.visitEndTime = endTime;
  }

  /**
   * 设置接待人信息
   */
  setHost(name: string, phone: string): void {
    this.hostName = name;
    this.hostPhone = phone;
  }

  /**
   * 设置所属公司
   */
  setCompany(company: string): void {
    this.company = company;
  }

  /**
   * 审批访问
   */
  approve(): void {
    this.isApproved = true;
  }

  /**
   * 拒绝访问
   */
  reject(): void {
    this.isApproved = false;
  }

  /**
   * 获取访问目的
   */
  getVisitPurpose(): string | null {
    return this.visitPurpose;
  }

  /**
   * 获取访问时间
   */
  getVisitTime(): { startTime: Date | null; endTime: Date | null } {
    return {
      startTime: this.visitStartTime,
      endTime: this.visitEndTime,
    };
  }

  /**
   * 获取接待人信息
   */
  getHost(): { name: string | null; phone: string | null } {
    return {
      name: this.hostName,
      phone: this.hostPhone,
    };
  }

  /**
   * 获取所属公司
   */
  getCompany(): string | null {
    return this.company;
  }

  /**
   * 检查是否已审批
   */
  getApprovalStatus(): boolean {
    return this.isApproved;
  }

  /**
   * 检查访问是否有效
   */
  isVisitValid(): boolean {
    if (!this.isApproved) return false;
    if (!this.visitStartTime || !this.visitEndTime) return false;

    const now = new Date();
    return now >= this.visitStartTime && now <= this.visitEndTime;
  }
}

/**
 * 开发者用户
 */
@ChildEntity('developer')
export class DeveloperUser extends User {
  @Column({
    name: 'access_level',
    type: 'varchar',
    length: 20,
    default: 'admin',
    comment: '访问级别: read, write, admin',
  })
  private accessLevel: 'read' | 'write' | 'admin' = 'admin';

  @Column({
    name: 'allowed_environments',
    type: 'text',
    nullable: true,
    comment: '允许访问的环境列表(JSON格式)',
  })
  private allowedEnvironments?: string;

  @Column({
    name: 'api_key',
    type: 'varchar',
    length: 64,
    nullable: true,
    comment: 'API密钥',
  })
  private apiKey?: string;

  @Column({
    name: 'last_login_ip',
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: '最后登录IP',
  })
  private lastLoginIp?: string;

  @Column({
    name: 'login_count',
    type: 'int',
    default: 0,
    comment: '登录次数',
  })
  private loginCount: number = 0;

  constructor(
    userType: UserTypeEnum,
    profile: UserProfile,
    accessLevel: 'read' | 'write' | 'admin' = 'admin',
    allowedEnvironments: string[] = ['development', 'staging', 'production'],
  ) {
    super(userType, profile);
    this.accessLevel = accessLevel;
    this.setAllowedEnvironments(allowedEnvironments);
  }

  /**
   * 获取访问级别
   */
  getAccessLevel(): string {
    return this.accessLevel;
  }

  /**
   * 设置访问级别
   */
  setAccessLevel(level: 'read' | 'write' | 'admin'): void {
    this.accessLevel = level;
    this.markAsModified();
  }

  /**
   * 获取允许访问的环境列表
   */
  getAllowedEnvironments(): string[] {
    if (!this.allowedEnvironments) {
      return ['development', 'staging', 'production'];
    }
    try {
      return JSON.parse(this.allowedEnvironments);
    } catch {
      return ['development'];
    }
  }

  /**
   * 设置允许访问的环境列表
   */
  setAllowedEnvironments(environments: string[]): void {
    this.allowedEnvironments = JSON.stringify(environments);
    this.markAsModified();
  }

  /**
   * 检查是否可以访问指定环境
   */
  canAccessEnvironment(environment: string): boolean {
    return this.getAllowedEnvironments().includes(environment);
  }

  /**
   * 检查是否可以执行管理操作
   */
  canPerformAdminOperations(): boolean {
    return this.accessLevel === 'admin';
  }

  /**
   * 检查是否可以修改数据
   */
  canModifyData(): boolean {
    return this.accessLevel === 'write' || this.accessLevel === 'admin';
  }

  /**
   * 检查是否只有读权限
   */
  isReadOnly(): boolean {
    return this.accessLevel === 'read';
  }

  /**
   * 获取API密钥
   */
  getApiKey(): string | undefined {
    return this.apiKey;
  }

  /**
   * 设置API密钥
   */
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
    this.markAsModified();
  }

  /**
   * 记录登录信息
   */
  recordLogin(ip: string): void {
    this.lastLoginIp = ip;
    this.loginCount += 1;
    this.markAsModified();
  }

  /**
   * 获取登录统计
   */
  getLoginStats(): { lastLoginIp?: string; loginCount: number } {
    return {
      lastLoginIp: this.lastLoginIp,
      loginCount: this.loginCount,
    };
  }
}

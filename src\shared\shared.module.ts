import { Module, Global } from '@nestjs/common';

// CQRS基础设施
import { SimpleCommandBus } from './infrastructure/cqrs/simple-command-bus';
import { SimpleQueryBus } from './infrastructure/cqrs/simple-query-bus';
import { SimpleEventPublisher } from './application/cqrs/event';

// 领域事件发布器
import { SimpleDomainEventPublisher } from './domain/events/domain-event';

// 基础设施服务
import { AuditService } from './application/services/audit.service';

// 令牌
export const DOMAIN_EVENT_PUBLISHER = Symbol('DOMAIN_EVENT_PUBLISHER');

/**
 * 共享模块
 * 提供所有上下文共用的基础设施和服务
 */
@Global()
@Module({
  imports: [],

  providers: [
    // CQRS总线
    SimpleCommandBus,
    SimpleQueryBus,
    SimpleEventPublisher,

    // 领域事件发布器
    {
      provide: DOMAIN_EVENT_PUBLISHER,
      useFactory: (eventBus: SimpleEventPublisher) => {
        return new SimpleDomainEventPublisher(eventBus);
      },
      inject: [SimpleEventPublisher],
    },

    // 基础设施服务
    AuditService,
  ],

  exports: [
    // 导出CQRS基础设施
    SimpleCommandBus,
    SimpleQueryBus,
    SimpleEventPublisher,
    DOMAIN_EVENT_PUBLISHER,

    // 导出基础设施服务
    AuditService,
  ],
})
export class SharedModule {
  constructor() {
    console.log('SharedModule initialized');
  }
}

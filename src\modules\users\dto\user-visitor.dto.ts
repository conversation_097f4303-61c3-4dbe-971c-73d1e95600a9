import { IsString, IsOptional, <PERSON>N<PERSON>ber, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserVisitorDto {
  @ApiProperty({ description: '访客姓名', example: '访客A' })
  @IsString()
  name: string;

  @ApiProperty({ description: '手机号', example: '13800138000' })
  @IsString()
  phone: string;

  @ApiPropertyOptional({ description: '性别', example: 1 })
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiPropertyOptional({
    description: '头像',
    example: 'https://example.com/avatar.jpg',
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄', example: 30 })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiPropertyOptional({ description: '生日', example: '1993-01-01' })
  @IsOptional()
  @IsDateString()
  birthday?: string;

  @ApiPropertyOptional({
    description: '身份证号',
    example: '110101199301011234',
  })
  @IsOptional()
  @IsString()
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型', example: '身份证' })
  @IsOptional()
  @IsString()
  cardType?: string;

  @ApiPropertyOptional({ description: '车牌号', example: '京A12345' })
  @IsOptional()
  @IsString()
  carNo?: string;
}

export class UpdateUserVisitorDto {
  @ApiPropertyOptional({ description: '访客姓名', example: '访客B' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '手机号', example: '13900139000' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '性别', example: 1 })
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiPropertyOptional({
    description: '头像',
    example: 'https://example.com/new-avatar.jpg',
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄', example: 35 })
  @IsOptional()
  @IsNumber()
  age?: number;

  @ApiPropertyOptional({ description: '生日', example: '1988-01-01' })
  @IsOptional()
  @IsDateString()
  birthday?: string;

  @ApiPropertyOptional({
    description: '身份证号',
    example: '110101198801011234',
  })
  @IsOptional()
  @IsString()
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型', example: '身份证' })
  @IsOptional()
  @IsString()
  cardType?: string;

  @ApiPropertyOptional({ description: '车牌号', example: '京B67890' })
  @IsOptional()
  @IsString()
  carNo?: string;
}

export class QueryUserVisitorDto {
  @ApiPropertyOptional({ description: '访客姓名', example: '访客' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '手机号', example: '138' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: '性别', example: 1 })
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10 })
  @IsOptional()
  pageSize?: number = 10;
}

export class UserVisitorResponseDto {
  @ApiProperty({ description: '访客ID' })
  id: string;

  @ApiProperty({ description: '访客姓名' })
  name: string;

  @ApiProperty({ description: '手机号' })
  phone: string;

  @ApiPropertyOptional({ description: '性别' })
  gender?: number;

  @ApiPropertyOptional({ description: '头像' })
  avatar?: string;

  @ApiPropertyOptional({ description: '年龄' })
  age?: number;

  @ApiPropertyOptional({ description: '生日' })
  birthday?: Date;

  @ApiPropertyOptional({ description: '身份证号' })
  cardNo?: string;

  @ApiPropertyOptional({ description: '证件类型' })
  cardType?: string;

  @ApiPropertyOptional({ description: '车牌号' })
  carNo?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

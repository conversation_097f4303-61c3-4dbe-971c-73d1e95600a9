import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';

/**
 * 数据库配置工厂
 */
export const createDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  const env = configService.get<string>('NODE_ENV', 'development');
  
  return {
    type: 'mysql',
    host: configService.get<string>('DB_HOST', '*************'),
    port: configService.get<number>('DB_PORT', 3306),
    username: configService.get<string>('DB_USERNAME', 'root'),
    password: configService.get<string>('DB_PASSWORD', 'mysql.12345'),
    database: configService.get<string>('DB_DATABASE', 'smart_campus'),

    // 实体配置 - 自动加载所有实体
    autoLoadEntities: true,
    entities: [
      // 身份访问上下文实体
      'dist/contexts/identity-access/domain/aggregates/*.entity{.ts,.js}',
      // 其他上下文实体
      'dist/contexts/**/domain/aggregates/*.entity{.ts,.js}',
      // 共享实体
      'dist/shared/domain/**/*.entity{.ts,.js}',
    ],

    // 环境相关配置
    synchronize: env === 'development' || env === 'test',
    logging: env === 'development' ? ['query', 'error', 'warn'] : ['error'],
    logger: env === 'production' ? 'simple-console' : 'advanced-console',

    // 连接池配置
    extra: {
      connectionLimit: configService.get<number>('DB_CONNECTION_LIMIT', 10),
    },

    // 其他配置
    charset: 'utf8mb4',
    timezone: '+08:00',
    
    // 迁移配置
    migrations: ['dist/shared/infrastructure/database/migrations/*{.ts,.js}'],
    migrationsRun: env === 'production',
    
    // 缓存配置
    cache: {
      duration: configService.get<number>('DB_CACHE_DURATION', 30000), // 30秒
    },
  };
};

/**
 * 环境检查工具
 */
export const isDevelopment = () => process.env.NODE_ENV === 'development';
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isTest = () => process.env.NODE_ENV === 'test';

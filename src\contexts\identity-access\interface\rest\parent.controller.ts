import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ParentApplicationService } from '../../application/services/parent-application.service';
import { ParentUser } from '../../domain/aggregates/user.aggregate';
import { ResponseInterceptor } from '@/shared/infrastructure/interceptors/response.interceptor';
import { GlobalExceptionFilter } from '@/shared/infrastructure/filters/global-exception.filter';
import { PaginatedResponseDto } from '@/shared/application/dto/base-response.dto';

/**
 * 家长管理控制器
 */
@ApiTags('家长管理')
@Controller('parents')
@UseInterceptors(ResponseInterceptor)
export class ParentController {
  constructor(
    private readonly parentApplicationService: ParentApplicationService,
  ) {}

  /**
   * 创建家长
   */
  @Post()
  @ApiOperation({
    summary: '创建家长',
    description: '创建新的家长用户',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '家长创建成功',
    type: ParentUser,
  })
  async createParent(
    @Body()
    createParentDto: {
      name: string;
      phone: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      childrenIds?: string[];
      emergencyContact?: string;
      relationship?: string;
      workUnit?: string;
    },
  ): Promise<ParentUser> {
    return await this.parentApplicationService.createParent(createParentDto);
  }

  /**
   * 获取家长列表
   */
  @Get()
  @ApiOperation({
    summary: '获取家长列表',
    description: '分页获取家长列表，支持筛选和排序',
  })
  @ApiQuery({ name: 'page', required: false, description: '页码', example: 1 })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: '每页数量',
    example: 10,
  })
  @ApiQuery({ name: 'name', required: false, description: '家长姓名' })
  @ApiQuery({ name: 'phone', required: false, description: '手机号' })
  @ApiQuery({ name: 'isActive', required: false, description: '是否激活' })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: '排序字段',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: '排序方向',
    enum: ['ASC', 'DESC'],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取家长列表成功',
    type: PaginatedResponseDto<ParentUser>,
  })
  async getParents(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('name') name?: string,
    @Query('phone') phone?: string,
    @Query('isActive') isActive?: boolean,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResponseDto<ParentUser>> {
    return await this.parentApplicationService.getParents({
      page,
      pageSize,
      name,
      phone,
      isActive,
      sortBy,
      sortOrder,
    });
  }

  /**
   * 搜索家长
   */
  @Get('search')
  @ApiOperation({
    summary: '搜索家长',
    description: '根据关键词搜索家长',
  })
  @ApiQuery({ name: 'keyword', required: true, description: '搜索关键词' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: '返回数量限制',
    example: 20,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '搜索家长成功',
    type: [ParentUser],
  })
  async searchParents(
    @Query('keyword') keyword: string,
    @Query('limit') limit?: number,
  ): Promise<ParentUser[]> {
    return await this.parentApplicationService.searchParents(keyword, limit);
  }

  /**
   * 根据学生ID获取家长列表
   */
  @Get('student/:studentId')
  @ApiOperation({
    summary: '根据学生ID获取家长列表',
    description: '获取指定学生的所有家长',
  })
  @ApiParam({ name: 'studentId', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取家长列表成功',
    type: [ParentUser],
  })
  async getParentsByStudentId(
    @Param('studentId') studentId: string,
  ): Promise<ParentUser[]> {
    return await this.parentApplicationService.getParentsByStudentId(studentId);
  }

  /**
   * 获取家长详情
   */
  @Get(':id')
  @ApiOperation({
    summary: '获取家长详情',
    description: '根据ID获取家长详细信息',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取家长详情成功',
    type: ParentUser,
  })
  async getParent(@Param('id') id: string): Promise<ParentUser> {
    return await this.parentApplicationService.getParent(id);
  }

  /**
   * 更新家长信息
   */
  @Put(':id')
  @ApiOperation({
    summary: '更新家长信息',
    description: '更新指定家长的信息',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '家长信息更新成功',
    type: ParentUser,
  })
  async updateParent(
    @Param('id') id: string,
    @Body()
    updateParentDto: {
      name?: string;
      phone?: string;
      gender?: number;
      avatar?: string;
      age?: number;
      birthday?: Date;
      cardNo?: string;
      cardType?: string;
      emergencyContact?: string;
      relationship?: string;
      workUnit?: string;
    },
  ): Promise<ParentUser> {
    return await this.parentApplicationService.updateParent(
      id,
      updateParentDto,
    );
  }

  /**
   * 为家长添加关联学生
   */
  @Put(':id/children/:studentId')
  @ApiOperation({
    summary: '为家长添加关联学生',
    description: '将学生关联到家长账户',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiParam({ name: 'studentId', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '添加关联学生成功',
    type: ParentUser,
  })
  async addChildToParent(
    @Param('id') id: string,
    @Param('studentId') studentId: string,
  ): Promise<ParentUser> {
    return await this.parentApplicationService.addChildToParent(id, studentId);
  }

  /**
   * 移除家长关联的学生
   */
  @Delete(':id/children/:studentId')
  @ApiOperation({
    summary: '移除家长关联的学生',
    description: '从家长账户中移除学生关联',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiParam({ name: 'studentId', description: '学生ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '移除关联学生成功',
    type: ParentUser,
  })
  async removeChildFromParent(
    @Param('id') id: string,
    @Param('studentId') studentId: string,
  ): Promise<ParentUser> {
    return await this.parentApplicationService.removeChildFromParent(
      id,
      studentId,
    );
  }

  /**
   * 切换家长状态
   */
  @Put(':id/toggle-status')
  @ApiOperation({
    summary: '切换家长状态',
    description: '激活或停用家长账户',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '家长状态切换成功',
    type: ParentUser,
  })
  async toggleParentStatus(@Param('id') id: string): Promise<ParentUser> {
    return await this.parentApplicationService.toggleParentStatus(id);
  }

  /**
   * 删除家长
   */
  @Delete(':id')
  @ApiOperation({
    summary: '删除家长',
    description: '删除指定的家长',
  })
  @ApiParam({ name: 'id', description: '家长ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '家长删除成功',
  })
  async deleteParent(@Param('id') id: string): Promise<void> {
    return await this.parentApplicationService.deleteParent(id);
  }

  /**
   * 批量删除家长
   */
  @Delete('batch')
  @ApiOperation({
    summary: '批量删除家长',
    description: '批量删除多个家长',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量删除家长成功',
  })
  async batchDeleteParents(
    @Body() deleteDto: { parentIds: string[] },
  ): Promise<void> {
    return await this.parentApplicationService.batchDeleteParents(
      deleteDto.parentIds,
    );
  }
}

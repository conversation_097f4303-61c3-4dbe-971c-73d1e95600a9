import { Entity, Column, Index } from 'typeorm';
import { AggregateRoot } from '@/shared/domain/base/aggregate-root';
import { AccessLocation } from '../value-objects/access-location';
import { AccessTimeRange } from '../value-objects/access-time-range';
import { AccessRequestCreatedEvent } from '../events/access-request-created.event';
import { AccessRequestApprovedEvent } from '../events/access-request-approved.event';
import { AccessRequestRejectedEvent } from '../events/access-request-rejected.event';
import { BusinessRuleException } from '@/shared/application/exceptions/domain-exception';

/**
 * 门禁申请类型枚举
 */
export enum AccessRequestType {
  ENTER = 'enter',
  LEAVE = 'leave',
  VISITOR = 'visitor',
  TEMPORARY = 'temporary',
}

/**
 * 门禁申请状态枚举
 */
export enum AccessRequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

/**
 * 门禁申请聚合根
 */
@Entity('access_requests')
@Index(['userId', 'requestTime'])
@Index(['requestTime'])
@Index(['status'])
@Index(['plannedStartTime', 'plannedEndTime'])
export class AccessRequest extends AggregateRoot {
  @Column({
    name: 'user_id',
    type: 'varchar',
    length: 36,
    comment: '申请人ID',
  })
  private userId: string;

  @Column({
    name: 'request_type',
    type: 'enum',
    enum: AccessRequestType,
    comment: '申请类型',
  })
  private requestType: AccessRequestType;

  @Column({
    name: 'request_time',
    type: 'datetime',
    comment: '申请时间',
  })
  private requestTime: Date;

  @Column({
    name: 'planned_start_time',
    type: 'datetime',
    comment: '计划开始时间',
  })
  private plannedStartTime: Date;

  @Column({
    name: 'planned_end_time',
    type: 'datetime',
    comment: '计划结束时间',
  })
  private plannedEndTime: Date;

  @Column({
    name: 'location_name',
    type: 'varchar',
    length: 100,
    comment: '目标位置名称',
  })
  private locationName: string;

  @Column({
    name: 'location_description',
    type: 'varchar',
    length: 200,
    nullable: true,
    comment: '位置描述',
  })
  private locationDescription?: string;

  @Column({
    name: 'location_building',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '建筑名称',
  })
  private locationBuilding?: string;

  @Column({
    name: 'location_floor',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '楼层',
  })
  private locationFloor?: string;

  @Column({
    name: 'device_id',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: '目标设备ID',
  })
  private deviceId?: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: AccessRequestStatus,
    default: AccessRequestStatus.PENDING,
    comment: '申请状态',
  })
  private status: AccessRequestStatus;

  @Column({
    name: 'approver_id',
    type: 'varchar',
    length: 36,
    nullable: true,
    comment: '审批人ID',
  })
  private approverId?: string;

  @Column({
    name: 'approval_time',
    type: 'datetime',
    nullable: true,
    comment: '审批时间',
  })
  private approvalTime?: Date;

  @Column({
    name: 'approval_remark',
    type: 'text',
    nullable: true,
    comment: '审批备注',
  })
  private approvalRemark?: string;

  @Column({
    name: 'reason',
    type: 'text',
    comment: '申请原因',
  })
  private reason: string;

  @Column({
    name: 'face_id',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '人脸识别ID',
  })
  private faceId?: string;

  @Column({
    name: 'valid_until',
    type: 'datetime',
    nullable: true,
    comment: '有效期至',
  })
  private validUntil?: Date;

  constructor(
    userId: string,
    requestType: AccessRequestType,
    timeRange: AccessTimeRange,
    location: AccessLocation,
    reason: string,
    deviceId?: string
  ) {
    super();
    this.userId = userId;
    this.requestType = requestType;
    this.requestTime = new Date();
    this.plannedStartTime = timeRange.startTime;
    this.plannedEndTime = timeRange.endTime;
    this.setLocation(location);
    this.reason = reason;
    this.deviceId = deviceId;
    this.status = AccessRequestStatus.PENDING;

    // 验证业务规则
    this.validateBusinessRules();

    // 发布门禁申请创建事件
    this.addDomainEvent(new AccessRequestCreatedEvent(
      this.id,
      userId,
      requestType,
      timeRange,
      location,
      reason
    ));
  }

  /**
   * 获取申请人ID
   */
  getUserId(): string {
    return this.userId;
  }

  /**
   * 获取申请类型
   */
  getRequestType(): AccessRequestType {
    return this.requestType;
  }

  /**
   * 获取申请时间
   */
  getRequestTime(): Date {
    return this.requestTime;
  }

  /**
   * 获取计划时间范围
   */
  getPlannedTimeRange(): AccessTimeRange {
    return new AccessTimeRange({
      startTime: this.plannedStartTime,
      endTime: this.plannedEndTime,
    });
  }

  /**
   * 获取目标位置
   */
  getLocation(): AccessLocation {
    return new AccessLocation({
      name: this.locationName,
      description: this.locationDescription,
      building: this.locationBuilding,
      floor: this.locationFloor,
    });
  }

  /**
   * 获取申请状态
   */
  getStatus(): AccessRequestStatus {
    return this.status;
  }

  /**
   * 获取申请原因
   */
  getReason(): string {
    return this.reason;
  }

  /**
   * 获取审批人ID
   */
  getApproverId(): string | undefined {
    return this.approverId;
  }

  /**
   * 获取审批时间
   */
  getApprovalTime(): Date | undefined {
    return this.approvalTime;
  }

  /**
   * 获取审批备注
   */
  getApprovalRemark(): string | undefined {
    return this.approvalRemark;
  }

  /**
   * 批准申请
   */
  approve(approverId: string, remark?: string, validUntil?: Date): void {
    if (this.status !== AccessRequestStatus.PENDING) {
      throw new BusinessRuleException('只有待审批的申请才能被批准');
    }

    if (this.isExpired()) {
      throw new BusinessRuleException('申请已过期，无法批准');
    }

    this.status = AccessRequestStatus.APPROVED;
    this.approverId = approverId;
    this.approvalTime = new Date();
    this.approvalRemark = remark;
    this.validUntil = validUntil;
    this.markAsModified();

    // 发布批准事件
    this.addDomainEvent(new AccessRequestApprovedEvent(
      this.id,
      this.userId,
      approverId,
      this.getPlannedTimeRange(),
      this.getLocation(),
      remark
    ));
  }

  /**
   * 拒绝申请
   */
  reject(approverId: string, remark: string): void {
    if (this.status !== AccessRequestStatus.PENDING) {
      throw new BusinessRuleException('只有待审批的申请才能被拒绝');
    }

    this.status = AccessRequestStatus.REJECTED;
    this.approverId = approverId;
    this.approvalTime = new Date();
    this.approvalRemark = remark;
    this.markAsModified();

    // 发布拒绝事件
    this.addDomainEvent(new AccessRequestRejectedEvent(
      this.id,
      this.userId,
      approverId,
      remark
    ));
  }

  /**
   * 取消申请
   */
  cancel(): void {
    if (this.status !== AccessRequestStatus.PENDING) {
      throw new BusinessRuleException('只有待审批的申请才能被取消');
    }

    this.status = AccessRequestStatus.CANCELLED;
    this.markAsModified();
  }

  /**
   * 标记为过期
   */
  markAsExpired(): void {
    if (this.status === AccessRequestStatus.PENDING) {
      this.status = AccessRequestStatus.EXPIRED;
      this.markAsModified();
    }
  }

  /**
   * 设置人脸识别ID
   */
  setFaceId(faceId: string): void {
    this.faceId = faceId;
    this.markAsModified();
  }

  /**
   * 检查是否已批准
   */
  isApproved(): boolean {
    return this.status === AccessRequestStatus.APPROVED;
  }

  /**
   * 检查是否已拒绝
   */
  isRejected(): boolean {
    return this.status === AccessRequestStatus.REJECTED;
  }

  /**
   * 检查是否待审批
   */
  isPending(): boolean {
    return this.status === AccessRequestStatus.PENDING;
  }

  /**
   * 检查是否已过期
   */
  isExpired(): boolean {
    const now = new Date();
    return this.plannedEndTime < now || this.status === AccessRequestStatus.EXPIRED;
  }

  /**
   * 检查是否在有效期内
   */
  isValid(): boolean {
    if (!this.isApproved()) return false;
    if (this.validUntil && new Date() > this.validUntil) return false;
    return !this.isExpired();
  }

  /**
   * 检查是否即将开始
   */
  isStartingSoon(minutes: number = 15): boolean {
    return this.getPlannedTimeRange().isStartingSoon(minutes);
  }

  /**
   * 设置位置信息
   */
  private setLocation(location: AccessLocation): void {
    this.locationName = location.name;
    this.locationDescription = location.description;
    this.locationBuilding = location.building;
    this.locationFloor = location.floor;
  }

  /**
   * 验证业务规则
   */
  private validateBusinessRules(): void {
    // 验证时间范围
    if (this.plannedStartTime <= new Date()) {
      throw new BusinessRuleException('计划开始时间必须晚于当前时间');
    }

    // 验证申请原因
    if (!this.reason || this.reason.trim().length === 0) {
      throw new BusinessRuleException('申请原因不能为空');
    }

    if (this.reason.length > 500) {
      throw new BusinessRuleException('申请原因不能超过500个字符');
    }
  }
}
